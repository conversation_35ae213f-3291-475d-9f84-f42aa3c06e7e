@page {
    margin: 1rem;
    size: a4 landscape;
}

body {
    background-color: black;
    color: gray;
}

.frames {
    display: grid;
    grid-template-rows: auto 2.5rem;
    grid-template-columns: auto 9rem;
    break-before: page;
    /*border: 1px solid black;*/
}

.frames .footer {
    /*border: 1px solid black;*/
}

.controls {
    display: flex;
    justify-content: center;
}

.controls > div {
    width: 40rem;
}

.print-table {
    display: grid;
    grid-template-columns: 15rem 15rem 15rem;
    grid-template-rows: 12rem 12rem 12rem;
    font-family: "Ubuntu Mono", monospace;
    font-size: 2rem;
}

.northCell {
    position: relative;
}

.print-table .info {
    position: absolute;
    top: 0;
    left: 0;
    padding: 0.3rem;
    font-size: 1rem;
}

.print-table > div {
    border: 1px solid lightgray;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1px;
}

.play-table {
    display: grid;
    grid-template-rows: 17rem 8rem 17rem;
    font-family: "Ubuntu Mono", monospace;
    font-size: 3rem;
}

.play-table div {
    justify-self: center;
    display: inline-block;
    margin-right: 1rem;
}

.play-table .player {
    font-size: 1rem;
}

.play-table .suit {
    font-size: 2rem;
}

.play-table .red-suit {
    color: rosybrown;
}

.play-table .active-card {
    color: lightgrey;
}

.play-table .bad-card {
    color: red;
}

.play-table-header {
    display: flex;
    justify-content: space-between;
    font-family: "Arial", sans-serif;
}

.play-table-header a {
    text-decoration: none;
    color: gray;
}

.tricks {
    font-family: "Ubuntu Mono", monospace;
    white-space: pre;
}

.tricks .card {
    display: inline-block;
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
}

.tricks .lead {
    font-size: 0.7rem;
}

.tricks .card .suit {
    font-size: 0.8rem;
}

.tricks .card .winner {
    text-decoration: none;
    width: 1rem;
    font-size: 0.5rem;
    border-bottom-width: 1px;
    border-bottom-style: dotted;
}

.tricks .card .trump {
    text-decoration: none;
    width: 1rem;
    border-top-width: 1px;
    border-top-style: solid;
    border-top-color: gray;
}

footer {
    padding-top: 1.5rem;
    font-size: 0.5rem;
}

.board-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 60rem;
    margin: 0 auto;
    padding: 1rem;
}

.board-item {
    border: 1px solid lightgray;
    padding: 1rem;
    border-radius: 4px;
    background-color: #1a1a1a;
    transition: background-color 0.2s ease;
}

.board-item > div {
    display: inline-block;
}

.board-item .play-link {
    cursor: pointer;
}