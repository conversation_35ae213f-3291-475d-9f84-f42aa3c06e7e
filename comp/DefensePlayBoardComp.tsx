import React, { Component } from 'react';
import { Player, Strain } from "../modules/constants.ts";
import PlayHandComp from "./PlayHandComp.tsx";
import Card from "../modules/Card.ts";
import Board from "../modules/Board.ts";
import Wasm, { DDSModule } from "../modules/Wasm.ts";
import { formatStrain } from "../modules/common.ts";
import { Play } from "../modules/types.ts";
import { Link } from "react-router-dom";

declare var Module: DDSModule;

type Props = {
    board: Board
}

type State = {
    wCards: Card[]
    nCards: Card[],
    plays: Play[]
    showLastTrick: boolean
    wrongCard: Card | undefined;
    showDummy: boolean;
}

export default class DefensePlayBoardComp extends Component<Props, State> {

    state: State = {
        wCards: [],
        nCards: [],
        plays: [],
        showLastTrick: false,
        wrongCard: undefined,
        showDummy: false
    }

    undoKeyHandler: (event: any) => void = undefined;

    async componentDidMount() {
        const board = this.props.board;

        this.undoKeyHandler = (event: any) => {
            if (event.key === 'Backspace') {

                board.undo();

                this.setState({ wrongCard: undefined });

                this.makeOpponentMoveIfNeeded(); // first play only

                this.redrawBoard();
            }
        };

        window.addEventListener('keyup', this.undoKeyHandler);

        this.makeOpponentMoveIfNeeded();

        this.redrawBoard();
    }

    componentWillUnmount() {
        window.removeEventListener('keyup', this.undoKeyHandler);
    }

    playCard(card: Card): void {
        const board = this.props.board;

        if (getCorrectPlays(board).find(each => each.equals(card)) === undefined
            && !card.equals(this.state.wrongCard)) { // accept on second click

            this.setState({wrongCard: card});

            return;
        }

        this.setState({wrongCard: undefined});

        board.play(board.player, card);

        this.makeOpponentMoveIfNeeded();

        this.redrawBoard();
    }

    makeOpponentMoveIfNeeded(): void {
        const board = this.props.board;

        // Keep auto-playing until it's West's turn
        while (this.isOpponentsTurn()) {
            const opponent: Player = board.player;
            const opponentCard = getCorrectPlays(board)[0];

            board.play(opponent, opponentCard);

            // Check if we need to show dummy after first card
            if (board.plays.length === 1 || (board.plays.length === 0 && board.tricks.length > 0)) {
                this.setState({ showDummy: true });
            }
        }
    }

    isOpponentsTurn(): boolean {
        const board = this.props.board;
        // In defense mode, user controls only West
        // North, East, and South are auto-played
        return board.player !== Player.West;
    }

    redrawBoard() {
        const board = this.props.board;

        const wCards = board.deal.getPlayerCards(Player.West);
        const nCards = board.deal.getPlayerCards(Player.North);

        let plays = board.plays;

        if (board.plays.length === 0 && board.getLastTrick() !== undefined) {
            // Show last trick, otherwise can't see opponents last play
            plays = board.getLastTrick().getPlays();
        }

        // Show dummy (North) after first card is played
        const showDummy = board.tricks.length > 0 || board.plays.length > 0;

        this.setState({ wCards, nCards, plays, showDummy });
    }

    render() {

        const board = this.props.board;

        const formatCard = (c: Card) => <React.Fragment key={c.toString()}>
            {c.rank}
            <span className='suit'>{ formatStrain(c.suit as Strain) } </span>
        </React.Fragment>;

        const onTrickClickFunc = () => {
            this.makeOpponentMoveIfNeeded();
            this.redrawBoard();
        };

        return (
            <>
                <div className='play-table-header'>
                    <Link to={'/'}>Back</Link>
                    <div>
                        Defense Practice - {formatStrain(board.strain)} &nbsp;
                        {board.getEwTrickCount()} / {board.getNsTrickCount()}
                    </div>
                </div>
                <div className="defense-table" style={{ display: 'grid', gridTemplateColumns: '1fr 2fr 1fr', gridTemplateRows: '1fr 2fr 1fr', gap: '10px', height: '600px' }}>
                    {/* North - top center (dummy) */}
                    <div style={{ gridColumn: '2', gridRow: '1', display: 'flex', justifyContent: 'center' }}>
                        {this.state.showDummy ? (
                            <PlayHandComp cardClickAction={() => {}} // Dummy cards not clickable
                                          isValidPlayFunc={() => false}
                                          isBadPlayFunc={() => false}
                                          cards={this.state.nCards}/>
                        ) : (
                            <div>Dummy (hidden)</div>
                        )}
                    </div>

                    {/* West - left center */}
                    <div style={{ gridColumn: '1', gridRow: '2', display: 'flex', alignItems: 'center' }}>
                        <PlayHandComp cardClickAction={c => this.playCard(c)}
                                      isValidPlayFunc={c => board.isValidPlay(c)}
                                      isBadPlayFunc={c => c.equals(this.state.wrongCard)}
                                      cards={this.state.wCards}/>
                    </div>

                    {/* Center - trick display */}
                    <div style={{ gridColumn: '2', gridRow: '2', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <div onClick={ onTrickClickFunc }>
                            {this.state.plays.map(play => <div key={play.player + play.card.toString()}>
                                <span className='player'>{play.player}&nbsp;</span>
                                {formatCard(play.card)} </div>)}
                        </div>
                    </div>

                    {/* East - right center */}
                    <div style={{ gridColumn: '3', gridRow: '2', display: 'flex', alignItems: 'center' }}>
                        <div>East (auto)</div>
                    </div>

                    {/* South - bottom center (declarer - auto) */}
                    <div style={{ gridColumn: '2', gridRow: '3', display: 'flex', justifyContent: 'center' }}>
                        <div>South (declarer - auto)</div>
                    </div>
                </div>
            </>);
    }
}

function getCorrectPlays(board: Board): Card[] {
    const playsResult = new Wasm(Module).nextPlays(
        board.getTrickStartPbn(), board.strain, board.plays.map(p => p.card));

    return playsResult.getCorrectPlays();
}
