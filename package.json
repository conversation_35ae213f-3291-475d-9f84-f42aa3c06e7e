{"name": "dds.js", "version": "0.0.0", "description": "Double Dummy <PERSON> for Bridge", "main": "old/dds.js", "dependencies": {"underscore": "^1.8.3"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.7", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/preset-env": "^7.26.7", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@types/jest": "^29.5.14", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@types/underscore": "^1.13.0", "babel-core": "^5", "babel-loader": "^9.2.1", "chai": "^3.4.1", "http-server": "^0.8.5", "jest": "^29.7.0", "mocha": "^2.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^5.3.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "webpack": "^5.97.1", "webpack-cli": "^6.0.1"}, "scripts": {"test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/danvk/dds.js.git"}, "keywords": ["bridge"], "author": "<PERSON> (<EMAIL>)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/danvk/dds.js/issues"}, "homepage": "https://github.com/danvk/dds.js#readme"}