<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Bridge Board Generator / Double Dummy Solver</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:;base64,iVBORw0KGgo=">

    <script>
        const Module = {};
    </script>
    <script src="out.js"></script>

    <script src="./libs/babel.min.js"></script>
    <script src="./libs/system.min.js"></script>

</head>
<body>

<script type="systemjs-importmap">
    {
      "imports": {
        "react": "/libs/react.production.min.js",
        "react-dom": "/libs/react-dom.production.min.js",
        "react-dom/client": "/libs/react-dom.production.min.js",
        "react-router-dom": "/libs/react-router-dom.min.js"
      }
    }
</script>

<script>
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = { isDisabled: true };
</script>

<script type="module" src="systemjs-setup.js"></script>

<script>
    (async () => {
        // enforce loading order
        await System.import('react');
        await System.import('react-dom');
        await System.import('react-router-dom');
        await System.import('./index.tsx');
    })();
</script>

<div id="root">

</div>

</body>
</html>