var e;e||(e=eval("(function() { try { return Module || {} } catch(e) { return {} } })()"));var aa={},l;for(l in e)e.hasOwnProperty(l)&&(aa[l]=e[l]);var ba="object"===typeof window,ca="function"===typeof importScripts,da="object"===typeof process&&"function"===typeof require&&!ba&&!ca,ea=!ba&&!da&&!ca;
if(da){e.print||(e.print=function(a){process.stdout.write(a+"\n")});e.printErr||(e.printErr=function(a){process.stderr.write(a+"\n")});var fa=require("fs"),ga=require("path");e.read=function(a,b){a=ga.normalize(a);var c=fa.readFileSync(a);c||a==ga.resolve(a)||(a=path.join(__dirname,"..","src",a),c=fa.readFileSync(a));c&&!b&&(c=c.toString());return c};e.readBinary=function(a){a=e.read(a,!0);a.buffer||(a=new Uint8Array(a));assert(a.buffer);return a};e.load=function(a){ha(read(a))};e.thisProgram||(e.thisProgram=
1<process.argv.length?process.argv[1].replace(/\\/g,"/"):"unknown-program");e.arguments=process.argv.slice(2);"undefined"!==typeof module&&(module.exports=e);process.on("uncaughtException",function(a){if(!(a instanceof ia))throw a;});e.inspect=function(){return"[Emscripten Module object]"}}else if(ea)e.print||(e.print=print),"undefined"!=typeof printErr&&(e.printErr=printErr),e.read="undefined"!=typeof read?read:function(){throw"no read() available (jsc?)";},e.readBinary=function(a){if("function"===
typeof readbuffer)return new Uint8Array(readbuffer(a));a=read(a,"binary");assert("object"===typeof a);return a},"undefined"!=typeof scriptArgs?e.arguments=scriptArgs:"undefined"!=typeof arguments&&(e.arguments=arguments),eval("if (typeof gc === 'function' && gc.toString().indexOf('[native code]') > 0) var gc = undefined");else if(ba||ca)e.read=function(a){var b=new XMLHttpRequest;b.open("GET",a,!1);b.send(null);return b.responseText},"undefined"!=typeof arguments&&(e.arguments=arguments),"undefined"!==
typeof console?(e.print||(e.print=function(a){console.log(a)}),e.printErr||(e.printErr=function(a){console.log(a)})):e.print||(e.print=function(){}),ca&&(e.load=importScripts),"undefined"===typeof e.setWindowTitle&&(e.setWindowTitle=function(a){document.title=a});else throw"Unknown runtime environment. Where are we?";function ha(a){eval.call(null,a)}!e.load&&e.read&&(e.load=function(a){ha(e.read(a))});e.print||(e.print=function(){});e.printErr||(e.printErr=e.print);e.arguments||(e.arguments=[]);
e.thisProgram||(e.thisProgram="./this.program");e.print=e.print;e.X=e.printErr;e.preRun=[];e.postRun=[];for(l in aa)aa.hasOwnProperty(l)&&(e[l]=aa[l]);
var n={ub:function(a){ja=a},ib:function(){return ja},va:function(){return m},ea:function(a){m=a},Ma:function(a){switch(a){case "i1":case "i8":return 1;case "i16":return 2;case "i32":return 4;case "i64":return 8;case "float":return 4;case "double":return 8;default:return"*"===a[a.length-1]?n.K:"i"===a[0]?(a=parseInt(a.substr(1)),assert(0===a%8),a/8):0}},hb:function(a){return Math.max(n.Ma(a),n.K)},xd:16,Td:function(a,b){"double"===b||"i64"===b?a&7&&(assert(4===(a&7)),a+=4):assert(0===(a&3));return a},
Hd:function(a,b,c){return c||"i64"!=a&&"double"!=a?a?Math.min(b||(a?n.hb(a):0),n.K):Math.min(b,8):8},M:function(a,b,c){return c&&c.length?(c.splice||(c=Array.prototype.slice.call(c)),c.splice(0,0,b),e["dynCall_"+a].apply(null,c)):e["dynCall_"+a].call(null,b)},aa:[],ab:function(a){for(var b=0;b<n.aa.length;b++)if(!n.aa[b])return n.aa[b]=a,2*(1+b);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS.";},qb:function(a){n.aa[(a-2)/2]=null},R:function(a){n.R.ua||
(n.R.ua={});n.R.ua[a]||(n.R.ua[a]=1,e.X(a))},na:{},Kd:function(a,b){assert(b);n.na[b]||(n.na[b]={});var c=n.na[b];c[a]||(c[a]=function(){return n.M(b,a,arguments)});return c[a]},Id:function(){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work";},da:function(a){var b=m;m=m+a|0;m=m+15&-16;return b},Va:function(a){var b=ka;ka=ka+a|0;ka=ka+15&-16;return b},O:function(a){var b=t;t=t+a|0;t=t+15&-16;if(a=t>=v)x("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+
v+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which adjusts the size at runtime but prevents some optimizations, (3) set Module.TOTAL_MEMORY to a higher value before the program runs, or if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 "),a=!0;return a?(t=b,0):b},ja:function(a,b){return Math.ceil(a/(b?b:16))*(b?b:16)},Qd:function(a,b,c){return c?+(a>>>0)+4294967296*+(b>>>0):+(a>>>0)+4294967296*+(b|0)},Ya:8,K:4,yd:0};e.Runtime=n;n.addFunction=n.ab;
n.removeFunction=n.qb;var y=!1,la,ma,ja;function assert(a,b){a||x("Assertion failed: "+b)}function na(a){var b=e["_"+a];if(!b)try{b=eval("_"+a)}catch(c){}assert(b,"Cannot call unknown function "+a+" (perhaps LLVM optimizations or closure removed it?)");return b}var oa,pa;
(function(){function a(a){a=a.toString().match(d).slice(1);return{arguments:a[0],body:a[1],returnValue:a[2]}}var b={stackSave:function(){n.va()},stackRestore:function(){n.ea()},arrayToC:function(a){var b=n.da(a.length);qa(a,b);return b},stringToC:function(a){var b=0;null!==a&&void 0!==a&&0!==a&&(b=n.da((a.length<<2)+1),ra(a,b));return b}},c={string:b.stringToC,array:b.arrayToC};pa=function(a,b,d,f,g){a=na(a);var w=[],z=0;if(f)for(var C=0;C<f.length;C++){var J=c[d[C]];J?(0===z&&(z=n.va()),w[C]=J(f[C])):
w[C]=f[C]}d=a.apply(null,w);"string"===b&&(d=B(d));if(0!==z){if(g&&g.async){EmterpreterAsync.Ad.push(function(){n.ea(z)});return}n.ea(z)}return d};var d=/^function\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/,f={},g;for(g in b)b.hasOwnProperty(g)&&(f[g]=a(b[g]));oa=function(b,c,d){d=d||[];var g=na(b);b=d.every(function(a){return"number"===a});var p="string"!==c;if(p&&b)return g;var w=d.map(function(a,b){return"$"+b});c="(function("+w.join(",")+") {";var z=d.length;if(!b){c+="var stack = "+
f.stackSave.body+";";for(var C=0;C<z;C++){var J=w[C],R=d[C];"number"!==R&&(R=f[R+"ToC"],c+="var "+R.arguments+" = "+J+";",c+=R.body+";",c+=J+"="+R.returnValue+";")}}d=a(function(){return g}).returnValue;c+="var ret = "+d+"("+w.join(",")+");";p||(d=a(function(){return B}).returnValue,c+="ret = "+d+"(ret);");b||(c+=f.stackRestore.body.replace("()","(stack)")+";");return eval(c+"return ret})")}})();e.ccall=pa;e.cwrap=oa;
function sa(a,b,c){c=c||"i8";"*"===c.charAt(c.length-1)&&(c="i32");switch(c){case "i1":D[a>>0]=b;break;case "i8":D[a>>0]=b;break;case "i16":E[a>>1]=b;break;case "i32":F[a>>2]=b;break;case "i64":ma=[b>>>0,(la=b,1<=+ta(la)?0<la?(ua(+va(la/4294967296),4294967295)|0)>>>0:~~+wa((la-+(~~la>>>0))/4294967296)>>>0:0)];F[a>>2]=ma[0];F[a+4>>2]=ma[1];break;case "float":xa[a>>2]=b;break;case "double":ya[a>>3]=b;break;default:x("invalid type for setValue: "+c)}}e.setValue=sa;
function za(a,b){b=b||"i8";"*"===b.charAt(b.length-1)&&(b="i32");switch(b){case "i1":return D[a>>0];case "i8":return D[a>>0];case "i16":return E[a>>1];case "i32":return F[a>>2];case "i64":return F[a>>2];case "float":return xa[a>>2];case "double":return ya[a>>3];default:x("invalid type for setValue: "+b)}return null}e.getValue=za;e.ALLOC_NORMAL=0;e.ALLOC_STACK=1;e.ALLOC_STATIC=2;e.ALLOC_DYNAMIC=3;e.ALLOC_NONE=4;
function G(a,b,c,d){var f,g;"number"===typeof a?(f=!0,g=a):(f=!1,g=a.length);var h="string"===typeof b?b:null;c=4==c?d:[H,n.da,n.Va,n.O][void 0===c?2:c](Math.max(g,h?1:b.length));if(f){d=c;assert(0==(c&3));for(a=c+(g&-4);d<a;d+=4)F[d>>2]=0;for(a=c+g;d<a;)D[d++>>0]=0;return c}if("i8"===h)return a.subarray||a.slice?I.set(a,c):I.set(new Uint8Array(a),c),c;d=0;for(var k,u;d<g;){var r=a[d];"function"===typeof r&&(r=n.Ld(r));f=h||b[d];0===f?d++:("i64"==f&&(f="i32"),sa(c+d,r,f),u!==f&&(k=n.Ma(f),u=f),d+=
k)}return c}e.allocate=G;e.getMemory=function(a){return Aa?"undefined"!==typeof Ba&&!Ba.q||!Ca?n.O(a):H(a):n.Va(a)};function B(a,b){if(0===b||!a)return"";for(var c=0,d,f=0;;){d=I[a+f>>0];c|=d;if(0==d&&!b)break;f++;if(b&&f==b)break}b||(b=f);d="";if(128>c){for(;0<b;)c=String.fromCharCode.apply(String,I.subarray(a,a+Math.min(b,1024))),d=d?d+c:c,a+=1024,b-=1024;return d}return e.UTF8ToString(a)}e.Pointer_stringify=B;e.AsciiToString=function(a){for(var b="";;){var c=D[a++>>0];if(!c)return b;b+=String.fromCharCode(c)}};
e.stringToAscii=function(a,b){return Da(a,b,!1)};
function Ea(a,b){for(var c,d,f,g,h,k,u="";;){c=a[b++];if(!c)return u;c&128?(d=a[b++]&63,192==(c&224)?u+=String.fromCharCode((c&31)<<6|d):(f=a[b++]&63,224==(c&240)?c=(c&15)<<12|d<<6|f:(g=a[b++]&63,240==(c&248)?c=(c&7)<<18|d<<12|f<<6|g:(h=a[b++]&63,248==(c&252)?c=(c&3)<<24|d<<18|f<<12|g<<6|h:(k=a[b++]&63,c=(c&1)<<30|d<<24|f<<18|g<<12|h<<6|k))),65536>c?u+=String.fromCharCode(c):(c-=65536,u+=String.fromCharCode(55296|c>>10,56320|c&1023)))):u+=String.fromCharCode(c)}}e.UTF8ArrayToString=Ea;
e.UTF8ToString=function(a){return Ea(I,a)};
function Fa(a,b,c,d){if(!(0<d))return 0;var f=c;d=c+d-1;for(var g=0;g<a.length;++g){var h=a.charCodeAt(g);55296<=h&&57343>=h&&(h=65536+((h&1023)<<10)|a.charCodeAt(++g)&1023);if(127>=h){if(c>=d)break;b[c++]=h}else{if(2047>=h){if(c+1>=d)break;b[c++]=192|h>>6}else{if(65535>=h){if(c+2>=d)break;b[c++]=224|h>>12}else{if(2097151>=h){if(c+3>=d)break;b[c++]=240|h>>18}else{if(67108863>=h){if(c+4>=d)break;b[c++]=248|h>>24}else{if(c+5>=d)break;b[c++]=252|h>>30;b[c++]=128|h>>24&63}b[c++]=128|h>>18&63}b[c++]=128|
h>>12&63}b[c++]=128|h>>6&63}b[c++]=128|h&63}}b[c]=0;return c-f}e.stringToUTF8Array=Fa;e.stringToUTF8=function(a,b,c){return Fa(a,I,b,c)};function Ga(a){for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);55296<=d&&57343>=d&&(d=65536+((d&1023)<<10)|a.charCodeAt(++c)&1023);127>=d?++b:b=2047>=d?b+2:65535>=d?b+3:2097151>=d?b+4:67108863>=d?b+5:b+6}return b}e.lengthBytesUTF8=Ga;e.UTF16ToString=function(a){for(var b=0,c="";;){var d=E[a+2*b>>1];if(0==d)return c;++b;c+=String.fromCharCode(d)}};
e.stringToUTF16=function(a,b,c){void 0===c&&(c=2147483647);if(2>c)return 0;c-=2;var d=b;c=c<2*a.length?c/2:a.length;for(var f=0;f<c;++f)E[b>>1]=a.charCodeAt(f),b+=2;E[b>>1]=0;return b-d};e.lengthBytesUTF16=function(a){return 2*a.length};e.UTF32ToString=function(a){for(var b=0,c="";;){var d=F[a+4*b>>2];if(0==d)return c;++b;65536<=d?(d=d-65536,c+=String.fromCharCode(55296|d>>10,56320|d&1023)):c+=String.fromCharCode(d)}};
e.stringToUTF32=function(a,b,c){void 0===c&&(c=2147483647);if(4>c)return 0;var d=b;c=d+c-4;for(var f=0;f<a.length;++f){var g=a.charCodeAt(f);if(55296<=g&&57343>=g)var h=a.charCodeAt(++f),g=65536+((g&1023)<<10)|h&1023;F[b>>2]=g;b+=4;if(b+4>c)break}F[b>>2]=0;return b-d};e.lengthBytesUTF32=function(a){for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);55296<=d&&57343>=d&&++c;b+=4}return b};
function Ha(a){function b(c,d,f){d=d||Infinity;var g="",h=[],w;if("N"===a[k]){k++;"K"===a[k]&&k++;for(w=[];"E"!==a[k];)if("S"===a[k]){k++;var A=a.indexOf("_",k);w.push(r[a.substring(k,A)||0]||"?");k=A+1}else if("C"===a[k])w.push(w[w.length-1]),k+=2;else{var A=parseInt(a.substr(k)),Z=A.toString().length;if(!A||!Z){k--;break}var Wb=a.substr(k+Z,A);w.push(Wb);r.push(Wb);k+=Z+A}k++;w=w.join("::");d--;if(0===d)return c?[w]:w}else if(("K"===a[k]||p&&"L"===a[k])&&k++,A=parseInt(a.substr(k)))Z=A.toString().length,
w=a.substr(k+Z,A),k+=Z+A;p=!1;"I"===a[k]?(k++,A=b(!0),Z=b(!0,1,!0),g+=Z[0]+" "+w+"<"+A.join(", ")+">"):g=w;a:for(;k<a.length&&0<d--;)if(w=a[k++],w in u)h.push(u[w]);else switch(w){case "P":h.push(b(!0,1,!0)[0]+"*");break;case "R":h.push(b(!0,1,!0)[0]+"&");break;case "L":k++;A=a.indexOf("E",k)-k;h.push(a.substr(k,A));k+=A+2;break;case "A":A=parseInt(a.substr(k));k+=A.toString().length;if("_"!==a[k])throw"?";k++;h.push(b(!0,1,!0)[0]+" ["+A+"]");break;case "E":break a;default:g+="?"+w;break a}f||1!==
h.length||"void"!==h[0]||(h=[]);return c?(g&&h.push(g+"?"),h):g+("("+h.join(", ")+")")}var c=!!e.___cxa_demangle;if(c)try{var d=H(a.length);ra(a.substr(1),d);var f=H(4),g=e.___cxa_demangle(d,0,0,f);if(0===za(f,"i32")&&g)return B(g)}catch(h){}finally{d&&Ia(d),f&&Ia(f),g&&Ia(g)}var k=3,u={v:"void",b:"bool",c:"char",s:"short",i:"int",l:"long",f:"float",d:"double",w:"wchar_t",a:"signed char",h:"unsigned char",t:"unsigned short",j:"unsigned int",m:"unsigned long",x:"long long",y:"unsigned long long",z:"..."},
r=[],p=!0,d=a;try{if("Object._main"==a||"_main"==a)return"main()";"number"===typeof a&&(a=B(a));if("_"!==a[0]||"_"!==a[1]||"Z"!==a[2])return a;switch(a[3]){case "n":return"operator new()";case "d":return"operator delete()"}d=b()}catch(w){d+="?"}0<=d.indexOf("?")&&!c&&n.R("warning: a problem occurred in builtin C++ name demangling; build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling");return d}
function Ja(){return Ka().replace(/__Z[\w\d_]+/g,function(a){var b=Ha(a);return a===b?a:a+" ["+b+"]"})}function Ka(){var a=Error();if(!a.stack){try{throw Error(0);}catch(b){a=b}if(!a.stack)return"(no stack trace available)"}return a.stack.toString()}e.stackTrace=function(){return Ja()};function La(){var a=t;0<a%4096&&(a+=4096-a%4096);return a}
for(var D,I,E,Ma,F,Na,xa,ya,Oa=0,ka=0,Aa=!1,Pa=0,m=0,Qa=0,Ra=0,t=0,Sa=e.TOTAL_STACK||5242880,v=e.TOTAL_MEMORY||268435456,K=65536;K<v||K<2*Sa;)K=16777216>K?2*K:K+16777216;K!==v&&(v=K);assert("undefined"!==typeof Int32Array&&"undefined"!==typeof Float64Array&&!!(new Int32Array(1)).subarray&&!!(new Int32Array(1)).set,"JS engine does not provide full typed array support");var buffer;
e.buffer?(buffer=e.buffer,assert(buffer.byteLength===v,"provided buffer should be "+v+" bytes, but it is "+buffer.byteLength)):buffer=new ArrayBuffer(v);D=new Int8Array(buffer);E=new Int16Array(buffer);F=new Int32Array(buffer);I=new Uint8Array(buffer);Ma=new Uint16Array(buffer);Na=new Uint32Array(buffer);xa=new Float32Array(buffer);ya=new Float64Array(buffer);F[0]=255;assert(255===I[0]&&0===I[3],"Typed arrays 2 must be run on a little-endian system");e.HEAP=void 0;e.buffer=buffer;e.HEAP8=D;
e.HEAP16=E;e.HEAP32=F;e.HEAPU8=I;e.HEAPU16=Ma;e.HEAPU32=Na;e.HEAPF32=xa;e.HEAPF64=ya;function Ta(a){for(;0<a.length;){var b=a.shift();if("function"==typeof b)b();else{var c=b.ma;"number"===typeof c?void 0===b.T?n.M("v",c):n.M("vi",c,[b.T]):c(void 0===b.T?null:b.T)}}}var Ua=[],Va=[],Wa=[],L=[],Xa=[],Ca=!1;function Ya(a){Ua.unshift(a)}e.addOnPreRun=Ya;e.addOnInit=function(a){Va.unshift(a)};e.addOnPreMain=function(a){Wa.unshift(a)};e.addOnExit=function(a){L.unshift(a)};function Za(a){Xa.unshift(a)}
e.addOnPostRun=Za;function $a(a,b,c){c=Array(0<c?c:Ga(a)+1);a=Fa(a,c,0,c.length);b&&(c.length=a);return c}e.intArrayFromString=$a;e.intArrayToString=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c];255<d&&(d&=255);b.push(String.fromCharCode(d))}return b.join("")};function ra(a,b,c){a=$a(a,c);for(c=0;c<a.length;)D[b+c>>0]=a[c],c+=1}e.writeStringToMemory=ra;function qa(a,b){for(var c=0;c<a.length;c++)D[b++>>0]=a[c]}e.writeArrayToMemory=qa;
function Da(a,b,c){for(var d=0;d<a.length;++d)D[b++>>0]=a.charCodeAt(d);c||(D[b>>0]=0)}e.writeAsciiToMemory=Da;Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(a,b){var c=a&65535,d=b&65535;return c*d+((a>>>16)*d+c*(b>>>16)<<16)|0});Math.Md=Math.imul;Math.clz32||(Math.clz32=function(a){a=a>>>0;for(var b=0;32>b;b++)if(a&1<<31-b)return b;return 32});Math.Cd=Math.clz32;var ta=Math.abs,ab=Math.exp,wa=Math.ceil,va=Math.floor,ua=Math.min,bb=0,cb=null,db=null;
function eb(){bb++;e.monitorRunDependencies&&e.monitorRunDependencies(bb)}e.addRunDependency=eb;function fb(){bb--;e.monitorRunDependencies&&e.monitorRunDependencies(bb);if(0==bb&&(null!==cb&&(clearInterval(cb),cb=null),db)){var a=db;db=null;a()}}e.removeRunDependency=fb;e.preloadedImages={};e.preloadedAudios={};Oa=8;ka=Oa+3764944;Va.push({ma:function(){gb()}});
G([0,0,0,0,0,0,62,64,0,0,0,0,0,0,73,64,104,150,4,168,169,101,179,63,61,10,215,163,112,61,248,63,0,0,0,0,0,0,40,64,0,0,0,0,0,0,62,64,0,0,0,0,0,0,73,64,211,19,150,120,64,217,180,63,170,241,210,77,98,16,250,63,0,0,0,0,0,0,40,64,0,0,0,0,0,0,62,64,0,0,0,0,0,0,73,64,114,220,41,29,172,255,179,63,53,94,186,73,12,2,249,63,0,0,0,0,0,0,42,64,0,0,0,0,0,0,62,64,0,0,0,0,0,0,73,64,171,149,9,191,212,207,179,63,248,83,227,165,155,196,248,63,0,0,0,0,0,0,40,64,88,57,180,200,118,190,231,63,231,251,169,241,210,77,218,
63,81,218,27,124,97,50,165,63,31,133,235,81,184,30,253,63,156,196,32,176,114,104,229,63,49,8,172,28,90,100,219,63,101,25,226,88,23,183,161,63,25,4,86,14,45,178,249,63,0,0,0,0,0,0,62,64,0,0,0,0,0,0,73,64,104,150,4,168,169,101,179,63,61,10,215,163,112,61,248,63,0,0,0,0,0,0,40,64,0,0,0,0,0,0,62,64,0,0,0,0,0,0,73,64,43,48,100,117,171,231,180,63,84,227,165,155,196,32,250,63,0,0,0,0,0,0,42,64,72,3,0,0,236,11,0,0,72,1,0,0,0,0,0,0,32,3,0,0,249,11,0,0,32,3,0,0,6,12,0,0,72,3,0,0,19,12,0,0,80,1,0,0,0,0,0,0,
72,3,0,0,52,12,0,0,88,1,0,0,0,0,0,0,72,3,0,0,122,12,0,0,88,1,0,0,0,0,0,0,72,3,0,0,86,12,0,0,120,1,0,0,0,0,0,0,72,3,0,0,156,12,0,0,104,1,0,0,0,0,0,0,0,1,0,0,16,0,0,0,1,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,3,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,2,0,0,0,3,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,3,0,0,0,7,0,0,0,15,0,0,0,31,0,0,0,63,0,0,0,127,0,0,0,255,0,0,0,255,1,0,0,255,3,0,0,255,7,0,0,255,15,0,0,255,255,0,0,255,255,0,0,254,31,0,0,252,31,0,0,248,31,0,0,240,31,0,0,224,31,0,0,192,31,0,
0,128,31,0,0,0,31,0,0,0,30,0,0,0,28,0,0,0,24,0,0,0,16,0,0,0,0,0,0,96,85,4,0,120,99,1,0,136,144,0,0,216,89,0,0,104,66,0,0,152,58,0,0,200,50,0,0,160,15,0,0,160,235,5,0,224,34,2,0,96,234,0,0,64,156,0,0,48,117,0,0,216,89,0,0,80,70,0,0,112,23,0,0,72,101,2,0,152,183,0,0,144,101,0,0,80,70,0,0,128,62,0,0,176,54,0,0,16,39,0,0,112,23,0,0,200,32,3,0,216,83,1,0,200,175,0,0,160,140,0,0,0,125,0,0,96,109,0,0,192,93,0,0,32,78,0,0,1,0,0,0,2,0,0,0,3,0,0,0,4,0,0,0,1,0,0,0,2,0,0,0,3,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,56,
1,0,0,1,0,0,0,2,0,0,0,1,0,0,0,0,0,0,0,104,1,0,0,3,0,0,0,4,0,0,0,5,0,0,0,6,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,152,1,0,0,3,0,0,0,7,0,0,0,5,0,0,0,6,0,0,0,1,0,0,0,2,0,0,0,2,0,0,0,2,0,0,0,163,14,0,0,232,3,0,0,88,4,0,0,88,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,4,0,0,0,207,114,57,0,0,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,5,0,0,0,4,0,0,0,199,110,57,0,0,4,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,10,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,2,0,4,0,8,0,16,0,32,0,64,0,128,0,0,1,0,2,0,4,0,
8,0,16,0,32,0,0,0,0,1,0,2,0,4,0,8,0,16,0,32,0,64,0,128,0,0,1,0,2,0,4,0,8,0,16,0,32,40,102,105,114,115,116,72,97,110,100,32,62,61,32,48,41,32,38,38,32,40,102,105,114,115,116,72,97,110,100,32,60,61,32,51,41,0,65,66,115,101,97,114,99,104,46,99,112,112,0,69,118,97,108,117,97,116,101,0,107,32,60,32,52,0,120,120,50,51,52,53,54,55,56,57,84,74,81,75,65,45,83,72,68,67,78,78,69,83,87,71,101,110,101,114,97,108,32,101,114,114,111,114,0,90,101,114,111,32,99,97,114,100,115,0,84,97,114,103,101,116,32,101,120,99,
101,101,100,115,32,110,117,109,98,101,114,32,111,102,32,116,114,105,99,107,115,0,67,97,114,100,115,32,100,117,112,108,105,99,97,116,101,100,0,84,97,114,103,101,116,32,105,115,32,108,101,115,115,32,116,104,97,110,32,45,49,0,84,97,114,103,101,116,32,105,115,32,104,105,103,104,101,114,32,116,104,97,110,32,49,51,0,83,111,108,117,116,105,111,110,115,32,112,97,114,97,109,101,116,101,114,32,105,115,32,108,101,115,115,32,116,104,97,110,32,49,0,83,111,108,117,116,105,111,110,115,32,112,97,114,97,109,101,116,
101,114,32,105,115,32,104,105,103,104,101,114,32,116,104,97,110,32,51,0,84,111,111,32,109,97,110,121,32,99,97,114,100,115,0,99,117,114,114,101,110,116,84,114,105,99,107,83,117,105,116,32,111,114,32,99,117,114,114,101,110,116,84,114,105,99,107,82,97,110,107,32,104,97,115,32,119,114,111,110,103,32,100,97,116,97,0,80,108,97,121,101,100,32,99,97,114,100,32,97,108,115,111,32,114,101,109,97,105,110,115,32,105,110,32,97,32,104,97,110,100,0,87,114,111,110,103,32,110,117,109,98,101,114,32,111,102,32,114,101,
109,97,105,110,105,110,103,32,99,97,114,100,115,32,105,110,32,97,32,104,97,110,100,0,84,104,114,101,97,100,32,105,110,100,101,120,32,105,115,32,110,111,116,32,48,32,46,46,32,109,97,120,105,109,117,109,0,77,111,100,101,32,112,97,114,97,109,101,116,101,114,32,105,115,32,108,101,115,115,32,116,104,97,110,32,48,0,77,111,100,101,32,112,97,114,97,109,101,116,101,114,32,105,115,32,104,105,103,104,101,114,32,116,104,97,110,32,50,0,84,114,117,109,112,32,105,115,32,110,111,116,32,105,110,32,48,32,46,46,32,
52,0,70,105,114,115,116,32,105,115,32,110,111,116,32,105,110,32,48,32,46,46,32,50,0,65,110,97,108,121,115,101,80,108,97,121,32,105,110,112,117,116,32,101,114,114,111,114,0,80,66,78,32,115,116,114,105,110,103,32,101,114,114,111,114,0,84,111,111,32,109,97,110,121,32,98,111,97,114,100,115,32,114,101,113,117,101,115,116,101,100,0,67,111,117,108,100,32,110,111,116,32,99,114,101,97,116,101,32,116,104,114,101,97,100,115,0,83,111,109,101,116,104,105,110,103,32,102,97,105,108,101,100,32,119,97,105,116,105,
110,103,32,102,111,114,32,116,104,114,101,97,100,32,116,111,32,101,110,100,0,68,101,110,111,109,105,110,97,116,105,111,110,32,102,105,108,116,101,114,32,118,101,99,116,111,114,32,104,97,115,32,110,111,32,101,110,116,114,105,101,115,0,84,111,111,32,109,97,110,121,32,68,68,32,116,97,98,108,101,115,32,114,101,113,117,101,115,116,101,100,0,67,104,117,110,107,32,115,105,122,101,32,105,115,32,108,101,115,115,32,116,104,97,110,32,49,0,78,111,116,32,97,32,68,68,83,32,101,114,114,111,114,32,99,111,100,101,
0,84,114,117,109,112,48,0,78,84,95,86,111,105,100,49,0,84,114,117,109,112,95,86,111,105,100,49,0,78,84,95,78,111,116,118,111,105,100,49,0,84,114,117,109,112,95,78,111,116,118,111,105,100,49,0,78,84,95,86,111,105,100,50,0,84,114,117,109,112,95,86,111,105,100,50,0,78,84,95,78,111,116,118,111,105,100,50,0,84,114,117,109,112,95,78,111,116,118,111,105,100,50,0,78,84,95,86,111,105,100,51,0,84,114,117,109,112,95,86,111,105,100,51,0,67,111,109,98,95,78,111,116,118,111,105,100,51,0,83,117,109,32,37,100,32,
105,115,32,110,111,116,32,102,111,117,114,10,0,100,117,109,112,46,116,120,116,0,119,0,69,114,114,111,114,32,99,111,100,101,61,37,100,10,0,68,101,97,108,32,100,97,116,97,58,10,0,116,114,117,109,112,61,37,99,10,0,116,114,117,109,112,61,78,10,0,102,105,114,115,116,61,37,99,10,0,105,110,100,101,120,61,37,100,32,99,117,114,114,101,110,116,84,114,105,99,107,83,117,105,116,61,37,99,32,99,117,114,114,101,110,116,84,114,105,99,107,82,97,110,107,61,37,99,10,0,105,110,100,101,120,49,61,37,100,32,105,110,100,
101,120,50,61,37,100,32,114,101,109,97,105,110,67,97,114,100,115,61,37,100,10,0,116,97,114,103,101,116,61,37,100,10,0,115,111,108,117,116,105,111,110,115,61,37,100,10,0,109,111,100,101,61,37,100,10,0,9,37,99,32,0,45,45,0,37,99,32,0,9,9,37,99,32,0,120,120,50,51,52,53,54,55,56,57,84,74,81,75,65,45,83,72,68,67,78,78,69,83,87,98,117,102,95,115,105,122,101,58,32,37,100,10,0,73,110,118,97,108,105,100,32,116,114,117,109,112,58,32,39,37,115,39,32,40,101,120,112,101,99,116,101,100,32,78,44,32,83,44,32,72,
44,32,68,44,32,67,41,10,0,73,110,118,97,108,105,100,32,112,108,97,121,101,114,58,32,39,37,115,39,32,40,101,120,112,101,99,116,101,100,32,78,44,32,83,44,32,69,44,32,87,41,10,0,123,34,115,117,105,116,34,58,32,34,37,99,34,44,32,34,114,97,110,107,34,58,32,34,37,99,34,44,32,34,101,113,117,97,108,115,34,58,32,91,37,115,93,44,32,34,115,99,111,114,101,34,58,32,37,100,125,0,44,0,34,37,99,34,0,123,34,112,108,97,121,101,114,34,58,32,34,37,99,34,44,32,34,116,114,105,99,107,115,34,58,32,123,34,110,115,34,58,32,
37,100,44,32,34,101,119,34,58,32,37,100,125,44,32,34,112,108,97,121,115,34,58,91,0,93,125,0,32,32,0,84,114,105,101,100,32,116,111,32,102,105,110,100,32,119,105,110,110,101,114,32,111,102,32,105,110,99,111,109,112,108,101,116,101,32,116,114,105,99,107,46,10,0,123,34,78,34,58,32,37,100,44,32,34,83,34,58,32,37,100,44,32,34,69,34,58,32,37,100,44,32,34,87,34,58,32,37,100,125,0,123,34,78,34,58,32,37,115,44,34,83,34,58,32,37,115,44,34,72,34,58,32,37,115,44,34,68,34,58,32,37,115,44,34,67,34,58,32,37,115,
125,0,123,34,101,114,114,111,114,34,58,32,37,100,44,32,34,109,101,115,115,97,103,101,34,58,32,34,67,111,110,118,101,114,116,70,114,111,109,80,66,78,32,102,97,105,108,101,100,58,32,37,115,34,125,0,123,34,101,114,114,111,114,34,58,32,49,44,32,34,109,101,115,115,97,103,101,34,58,32,34,80,108,97,121,101,114,32,37,99,32,99,97,110,110,111,116,32,112,108,97,121,32,37,115,34,125,0,123,34,101,114,114,111,114,34,58,32,37,100,44,32,34,109,101,115,115,97,103,101,34,58,32,34,37,115,34,125,0,123,34,101,114,114,
111,114,34,58,32,37,100,44,32,34,102,117,110,99,116,105,111,110,34,58,32,34,67,97,108,99,68,68,116,97,98,108,101,80,66,78,34,44,32,34,109,101,115,115,97,103,101,34,58,32,34,37,115,34,125,0,83,116,57,98,97,100,95,97,108,108,111,99,0,83,116,57,101,120,99,101,112,116,105,111,110,0,83,116,57,116,121,112,101,95,105,110,102,111,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,54,95,95,115,104,105,109,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,55,95,95,99,108,
97,115,115,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,57,95,95,112,111,105,110,116,101,114,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,55,95,95,112,98,97,115,101,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,50,48,95,95,115,105,95,99,108,97,115,115,95,116,121,112,101,95,105,110,102,111,69,0,112,116,104,114,101,97,100,95,111,110,99,101,32,102,97,105,108,117,114,101,32,
105,110,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,95,102,97,115,116,40,41,0,115,116,100,58,58,98,97,100,95,97,108,108,111,99,0,116,101,114,109,105,110,97,116,101,95,104,97,110,100,108,101,114,32,117,110,101,120,112,101,99,116,101,100,108,121,32,114,101,116,117,114,110,101,100,0,99,97,110,110,111,116,32,99,114,101,97,116,101,32,112,116,104,114,101,97,100,32,107,101,121,32,102,111,114,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,40,41,0,99,97,110,110,111,116,32,
122,101,114,111,32,111,117,116,32,116,104,114,101,97,100,32,118,97,108,117,101,32,102,111,114,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,40,41,0,33,34,98,97,115,105,99,95,115,116,114,105,110,103,32,108,101,110,103,116,104,95,101,114,114,111,114,34,0,47,85,115,101,114,115,47,100,97,110,118,107,47,68,111,119,110,108,111,97,100,115,47,101,109,115,100,107,95,112,111,114,116,97,98,108,101,47,101,109,115,99,114,105,112,116,101,110,47,116,97,103,45,49,46,51,53,46,57,47,115,121,115,116,
101,109,47,105,110,99,108,117,100,101,47,108,105,98,99,120,120,47,115,116,114,105,110,103,0,95,95,116,104,114,111,119,95,108,101,110,103,116,104,95,101,114,114,111,114,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,101,120,99,101,112,116,105,111,110,32,111,102,32,116,121,112,101,32,37,115,58,32,37,115,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,101,120,99,101,112,116,105,111,110,32,111,102,32,116,121,112,101,32,37,115,0,116,101,114,109,
105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,102,111,114,101,105,103,110,32,101,120,99,101,112,116,105,111,110,0,116,101,114,109,105,110,97,116,105,110,103,0,117,110,99,97,117,103,104,116,0,84,33,34,25,13,1,2,3,17,75,28,12,16,4,11,29,18,30,39,104,110,111,112,113,98,32,5,6,15,19,20,21,26,8,22,7,40,36,23,24,9,10,14,27,31,37,35,131,130,125,38,42,43,60,61,62,63,67,71,74,77,88,89,90,91,92,93,94,95,96,97,99,100,101,102,103,105,106,107,108,114,115,116,121,122,123,124,0,73,108,108,101,103,97,
108,32,98,121,116,101,32,115,101,113,117,101,110,99,101,0,68,111,109,97,105,110,32,101,114,114,111,114,0,82,101,115,117,108,116,32,110,111,116,32,114,101,112,114,101,115,101,110,116,97,98,108,101,0,78,111,116,32,97,32,116,116,121,0,80,101,114,109,105,115,115,105,111,110,32,100,101,110,105,101,100,0,79,112,101,114,97,116,105,111,110,32,110,111,116,32,112,101,114,109,105,116,116,101,100,0,78,111,32,115,117,99,104,32,102,105,108,101,32,111,114,32,100,105,114,101,99,116,111,114,121,0,78,111,32,115,117,
99,104,32,112,114,111,99,101,115,115,0,70,105,108,101,32,101,120,105,115,116,115,0,86,97,108,117,101,32,116,111,111,32,108,97,114,103,101,32,102,111,114,32,100,97,116,97,32,116,121,112,101,0,78,111,32,115,112,97,99,101,32,108,101,102,116,32,111,110,32,100,101,118,105,99,101,0,79,117,116,32,111,102,32,109,101,109,111,114,121,0,82,101,115,111,117,114,99,101,32,98,117,115,121,0,73,110,116,101,114,114,117,112,116,101,100,32,115,121,115,116,101,109,32,99,97,108,108,0,82,101,115,111,117,114,99,101,32,116,
101,109,112,111,114,97,114,105,108,121,32,117,110,97,118,97,105,108,97,98,108,101,0,73,110,118,97,108,105,100,32,115,101,101,107,0,67,114,111,115,115,45,100,101,118,105,99,101,32,108,105,110,107,0,82,101,97,100,45,111,110,108,121,32,102,105,108,101,32,115,121,115,116,101,109,0,68,105,114,101,99,116,111,114,121,32,110,111,116,32,101,109,112,116,121,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,112,101,101,114,0,79,112,101,114,97,116,105,111,110,32,116,105,109,101,100,
32,111,117,116,0,67,111,110,110,101,99,116,105,111,110,32,114,101,102,117,115,101,100,0,72,111,115,116,32,105,115,32,100,111,119,110,0,72,111,115,116,32,105,115,32,117,110,114,101,97,99,104,97,98,108,101,0,65,100,100,114,101,115,115,32,105,110,32,117,115,101,0,66,114,111,107,101,110,32,112,105,112,101,0,73,47,79,32,101,114,114,111,114,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,32,111,114,32,97,100,100,114,101,115,115,0,66,108,111,99,107,32,100,101,118,105,99,101,32,114,101,113,117,105,114,
101,100,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,0,78,111,116,32,97,32,100,105,114,101,99,116,111,114,121,0,73,115,32,97,32,100,105,114,101,99,116,111,114,121,0,84,101,120,116,32,102,105,108,101,32,98,117,115,121,0,69,120,101,99,32,102,111,114,109,97,116,32,101,114,114,111,114,0,73,110,118,97,108,105,100,32,97,114,103,117,109,101,110,116,0,65,114,103,117,109,101,110,116,32,108,105,115,116,32,116,111,111,32,108,111,110,103,0,83,121,109,98,111,108,105,99,32,108,105,110,107,32,108,111,111,
112,0,70,105,108,101,110,97,109,101,32,116,111,111,32,108,111,110,103,0,84,111,111,32,109,97,110,121,32,111,112,101,110,32,102,105,108,101,115,32,105,110,32,115,121,115,116,101,109,0,78,111,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,115,32,97,118,97,105,108,97,98,108,101,0,66,97,100,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,0,78,111,32,99,104,105,108,100,32,112,114,111,99,101,115,115,0,66,97,100,32,97,100,100,114,101,115,115,0,70,105,108,101,32,116,111,111,32,
108,97,114,103,101,0,84,111,111,32,109,97,110,121,32,108,105,110,107,115,0,78,111,32,108,111,99,107,115,32,97,118,97,105,108,97,98,108,101,0,82,101,115,111,117,114,99,101,32,100,101,97,100,108,111,99,107,32,119,111,117,108,100,32,111,99,99,117,114,0,83,116,97,116,101,32,110,111,116,32,114,101,99,111,118,101,114,97,98,108,101,0,80,114,101,118,105,111,117,115,32,111,119,110,101,114,32,100,105,101,100,0,79,112,101,114,97,116,105,111,110,32,99,97,110,99,101,108,101,100,0,70,117,110,99,116,105,111,110,
32,110,111,116,32,105,109,112,108,101,109,101,110,116,101,100,0,78,111,32,109,101,115,115,97,103,101,32,111,102,32,100,101,115,105,114,101,100,32,116,121,112,101,0,73,100,101,110,116,105,102,105,101,114,32,114,101,109,111,118,101,100,0,68,101,118,105,99,101,32,110,111,116,32,97,32,115,116,114,101,97,109,0,78,111,32,100,97,116,97,32,97,118,97,105,108,97,98,108,101,0,68,101,118,105,99,101,32,116,105,109,101,111,117,116,0,79,117,116,32,111,102,32,115,116,114,101,97,109,115,32,114,101,115,111,117,114,
99,101,115,0,76,105,110,107,32,104,97,115,32,98,101,101,110,32,115,101,118,101,114,101,100,0,80,114,111,116,111,99,111,108,32,101,114,114,111,114,0,66,97,100,32,109,101,115,115,97,103,101,0,70,105,108,101,32,100,101,115,99,114,105,112,116,111,114,32,105,110,32,98,97,100,32,115,116,97,116,101,0,78,111,116,32,97,32,115,111,99,107,101,116,0,68,101,115,116,105,110,97,116,105,111,110,32,97,100,100,114,101,115,115,32,114,101,113,117,105,114,101,100,0,77,101,115,115,97,103,101,32,116,111,111,32,108,97,114,
103,101,0,80,114,111,116,111,99,111,108,32,119,114,111,110,103,32,116,121,112,101,32,102,111,114,32,115,111,99,107,101,116,0,80,114,111,116,111,99,111,108,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,80,114,111,116,111,99,111,108,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,83,111,99,107,101,116,32,116,121,112,101,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,78,111,116,32,115,117,112,112,111,114,116,101,100,0,80,114,111,116,111,99,111,108,32,102,97,109,105,108,121,32,
110,111,116,32,115,117,112,112,111,114,116,101,100,0,65,100,100,114,101,115,115,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,32,98,121,32,112,114,111,116,111,99,111,108,0,65,100,100,114,101,115,115,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,78,101,116,119,111,114,107,32,105,115,32,100,111,119,110,0,78,101,116,119,111,114,107,32,117,110,114,101,97,99,104,97,98,108,101,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,110,101,116,
119,111,114,107,0,67,111,110,110,101,99,116,105,111,110,32,97,98,111,114,116,101,100,0,78,111,32,98,117,102,102,101,114,32,115,112,97,99,101,32,97,118,97,105,108,97,98,108,101,0,83,111,99,107,101,116,32,105,115,32,99,111,110,110,101,99,116,101,100,0,83,111,99,107,101,116,32,110,111,116,32,99,111,110,110,101,99,116,101,100,0,67,97,110,110,111,116,32,115,101,110,100,32,97,102,116,101,114,32,115,111,99,107,101,116,32,115,104,117,116,100,111,119,110,0,79,112,101,114,97,116,105,111,110,32,97,108,114,101,
97,100,121,32,105,110,32,112,114,111,103,114,101,115,115,0,79,112,101,114,97,116,105,111,110,32,105,110,32,112,114,111,103,114,101,115,115,0,83,116,97,108,101,32,102,105,108,101,32,104,97,110,100,108,101,0,82,101,109,111,116,101,32,73,47,79,32,101,114,114,111,114,0,81,117,111,116,97,32,101,120,99,101,101,100,101,100,0,78,111,32,109,101,100,105,117,109,32,102,111,117,110,100,0,87,114,111,110,103,32,109,101,100,105,117,109,32,116,121,112,101,0,78,111,32,101,114,114,111,114,32,105,110,102,111,114,109,
97,116,105,111,110,0,0,114,119,97,0,17,0,10,0,17,17,17,0,0,0,0,5,0,0,0,0,0,0,9,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,15,10,17,17,17,3,10,7,0,1,19,9,11,11,0,0,9,6,11,0,0,11,0,6,17,0,0,0,17,17,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,10,10,17,17,17,0,10,0,0,2,0,9,11,0,0,0,9,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,0,0,0,0,13,0,0,
0,4,13,0,0,0,0,9,14,0,0,0,0,0,14,0,0,14,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16,0,0,0,0,0,0,0,0,0,0,0,15,0,0,0,0,15,0,0,0,0,9,16,0,0,0,0,0,16,0,0,16,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,9,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,0,0,0,10,0,0,0,0,10,0,0,0,0,9,11,0,0,0,0,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,
0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,48,49,50,51,52,53,54,55,56,57,65,66,67,68,69,70,45,43,32,32,32,48,88,48,120,0,40,110,117,108,108,41,0,45,48,88,43,48,88,32,48,88,45,48,120,43,48,120,32,48,120,0,105,110,102,0,73,78,70,0,110,97,110,0,78,65,78,0,46,0],"i8",4,n.Ya);var ib=n.ja(G(12,"i8",2),8);assert(0==ib%8);function jb(a,b){L.unshift({ma:a,T:b})}e._i64Subtract=kb;function lb(){return!!lb.q}var mb=0,nb=[],ob={};function pb(a){if(!a||ob[a])return a;for(var b in ob)if(ob[b].Ca===a)return b;return a}
function qb(){var a=mb;if(!a)return(M.setTempRet0(0),0)|0;var b=ob[a],c=b.type;if(!c)return(M.setTempRet0(0),a)|0;var d=Array.prototype.slice.call(arguments);e.___cxa_is_pointer_type(c);qb.buffer||(qb.buffer=H(4));F[qb.buffer>>2]=a;for(var a=qb.buffer,f=0;f<d.length;f++)if(d[f]&&e.___cxa_can_catch(d[f],c,a))return a=F[a>>2],b.Ca=a,(M.setTempRet0(d[f]),a)|0;a=F[a>>2];return(M.setTempRet0(c),a)|0}e._memset=rb;e._bitshift64Shl=sb;function tb(a,b){tb.q||(tb.q={});a in tb.q||(n.M("v",b),tb.q[a]=1)}
var ub={};function vb(a){e.___errno_location&&(F[e.___errno_location()>>2]=a);return a}
var N={J:1,F:2,jd:3,ec:4,I:5,Ba:6,yb:7,Cc:8,Y:9,Mb:10,wa:11,td:11,Xa:12,fa:13,Yb:14,Oc:15,ga:16,ya:17,ud:18,ia:19,za:20,N:21,p:22,xc:23,Wa:24,S:25,qd:26,Zb:27,Kc:28,Z:29,ed:30,qc:31,Yc:32,Vb:33,bd:34,Gc:42,bc:43,Nb:44,hc:45,ic:46,jc:47,pc:48,rd:49,Ac:50,gc:51,Sb:35,Dc:37,Eb:52,Hb:53,vd:54,yc:55,Ib:56,Jb:57,Tb:35,Kb:59,Mc:60,Bc:61,nd:62,Lc:63,Hc:64,Ic:65,dd:66,Ec:67,Bb:68,kd:69,Ob:70,Zc:71,sc:72,Wb:73,Gb:74,Tc:76,Fb:77,cd:78,kc:79,lc:80,oc:81,nc:82,mc:83,Nc:38,Aa:39,tc:36,ha:40,Uc:95,Xc:96,Rb:104,
zc:105,Cb:97,ad:91,Rc:88,Jc:92,gd:108,Qb:111,zb:98,Pb:103,wc:101,uc:100,od:110,$b:112,ac:113,dc:115,Db:114,Ub:89,rc:90,$c:93,hd:94,Ab:99,vc:102,fc:106,Pc:107,pd:109,sd:87,Xb:122,ld:116,Sc:95,Fc:123,cc:84,Vc:75,Lb:125,Qc:131,Wc:130,md:86},wb=1,xb={0:"Success",1:"Not super-user",2:"No such file or directory",3:"No such process",4:"Interrupted system call",5:"I/O error",6:"No such device or address",7:"Arg list too long",8:"Exec format error",9:"Bad file number",10:"No children",11:"No more processes",
12:"Not enough core",13:"Permission denied",14:"Bad address",15:"Block device required",16:"Mount device busy",17:"File exists",18:"Cross-device link",19:"No such device",20:"Not a directory",21:"Is a directory",22:"Invalid argument",23:"Too many open files in system",24:"Too many open files",25:"Not a typewriter",26:"Text file busy",27:"File too large",28:"No space left on device",29:"Illegal seek",30:"Read only file system",31:"Too many links",32:"Broken pipe",33:"Math arg out of domain of func",
34:"Math result not representable",35:"File locking deadlock error",36:"File or path name too long",37:"No record locks available",38:"Function not implemented",39:"Directory not empty",40:"Too many symbolic links",42:"No message of desired type",43:"Identifier removed",44:"Channel number out of range",45:"Level 2 not synchronized",46:"Level 3 halted",47:"Level 3 reset",48:"Link number out of range",49:"Protocol driver not attached",50:"No CSI structure available",51:"Level 2 halted",52:"Invalid exchange",
53:"Invalid request descriptor",54:"Exchange full",55:"No anode",56:"Invalid request code",57:"Invalid slot",59:"Bad font file fmt",60:"Device not a stream",61:"No data (for no delay io)",62:"Timer expired",63:"Out of streams resources",64:"Machine is not on the network",65:"Package not installed",66:"The object is remote",67:"The link has been severed",68:"Advertise error",69:"Srmount error",70:"Communication error on send",71:"Protocol error",72:"Multihop attempted",73:"Cross mount point (not really error)",
74:"Trying to read unreadable message",75:"Value too large for defined data type",76:"Given log. name not unique",77:"f.d. invalid for this operation",78:"Remote address changed",79:"Can   access a needed shared lib",80:"Accessing a corrupted shared lib",81:".lib section in a.out corrupted",82:"Attempting to link in too many libs",83:"Attempting to exec a shared library",84:"Illegal byte sequence",86:"Streams pipe error",87:"Too many users",88:"Socket operation on non-socket",89:"Destination address required",
90:"Message too long",91:"Protocol wrong type for socket",92:"Protocol not available",93:"Unknown protocol",94:"Socket type not supported",95:"Not supported",96:"Protocol family not supported",97:"Address family not supported by protocol family",98:"Address already in use",99:"Address not available",100:"Network interface is not configured",101:"Network is unreachable",102:"Connection reset by network",103:"Connection aborted",104:"Connection reset by peer",105:"No buffer space available",106:"Socket is already connected",
107:"Socket is not connected",108:"Can't send after socket shutdown",109:"Too many references",110:"Connection timed out",111:"Connection refused",112:"Host is down",113:"Host is unreachable",114:"Socket already connected",115:"Connection already in progress",116:"Stale file handle",122:"Quota exceeded",123:"No medium (in tape drive)",125:"Operation canceled",130:"Previous owner died",131:"State not recoverable"},yb=[];function zb(a,b){yb[a]={input:[],output:[],Q:b};Ab(a,Bb)}
var Bb={open:function(a){var b=yb[a.g.rdev];if(!b)throw new O(N.ia);a.tty=b;a.seekable=!1},close:function(a){a.tty.Q.flush(a.tty)},flush:function(a){a.tty.Q.flush(a.tty)},read:function(a,b,c,d){if(!a.tty||!a.tty.Q.Na)throw new O(N.Ba);for(var f=0,g=0;g<d;g++){var h;try{h=a.tty.Q.Na(a.tty)}catch(k){throw new O(N.I);}if(void 0===h&&0===f)throw new O(N.wa);if(null===h||void 0===h)break;f++;b[c+g]=h}f&&(a.g.timestamp=Date.now());return f},write:function(a,b,c,d){if(!a.tty||!a.tty.Q.ra)throw new O(N.Ba);
for(var f=0;f<d;f++)try{a.tty.Q.ra(a.tty,b[c+f])}catch(g){throw new O(N.I);}d&&(a.g.timestamp=Date.now());return f}},Cb={Na:function(a){if(!a.input.length){var b=null;if(da){var c=new Buffer(256),d=0,f=process.stdin.fd,g=!1;try{f=fs.openSync("/dev/stdin","r"),g=!0}catch(h){}d=fs.readSync(f,c,0,256,null);g&&fs.closeSync(f);0<d?b=c.slice(0,d).toString("utf-8"):b=null}else"undefined"!=typeof window&&"function"==typeof window.prompt?(b=window.prompt("Input: "),null!==b&&(b+="\n")):"function"==typeof readline&&
(b=readline(),null!==b&&(b+="\n"));if(!b)return null;a.input=$a(b,!0)}return a.input.shift()},ra:function(a,b){null===b||10===b?(e.print(Ea(a.output,0)),a.output=[]):0!=b&&a.output.push(b)},flush:function(a){a.output&&0<a.output.length&&(e.print(Ea(a.output,0)),a.output=[])}},Db={ra:function(a,b){null===b||10===b?(e.printErr(Ea(a.output,0)),a.output=[]):0!=b&&a.output.push(b)},flush:function(a){a.output&&0<a.output.length&&(e.printErr(Ea(a.output,0)),a.output=[])}},P={C:null,A:function(){return P.createNode(null,
"/",16895,0)},createNode:function(a,b,c,d){if(24576===(c&61440)||4096===(c&61440))throw new O(N.J);P.C||(P.C={dir:{g:{D:P.k.D,u:P.k.u,lookup:P.k.lookup,U:P.k.U,rename:P.k.rename,unlink:P.k.unlink,rmdir:P.k.rmdir,readdir:P.k.readdir,symlink:P.k.symlink},stream:{H:P.n.H}},file:{g:{D:P.k.D,u:P.k.u},stream:{H:P.n.H,read:P.n.read,write:P.n.write,Da:P.n.Da,Qa:P.n.Qa,Sa:P.n.Sa}},link:{g:{D:P.k.D,u:P.k.u,readlink:P.k.readlink},stream:{}},Ga:{g:{D:P.k.D,u:P.k.u},stream:Eb}});c=Fb(a,b,c,d);Q(c.mode)?(c.k=P.C.dir.g,
c.n=P.C.dir.stream,c.e={}):32768===(c.mode&61440)?(c.k=P.C.file.g,c.n=P.C.file.stream,c.o=0,c.e=null):40960===(c.mode&61440)?(c.k=P.C.link.g,c.n=P.C.link.stream):8192===(c.mode&61440)&&(c.k=P.C.Ga.g,c.n=P.C.Ga.stream);c.timestamp=Date.now();a&&(a.e[b]=c);return c},gb:function(a){if(a.e&&a.e.subarray){for(var b=[],c=0;c<a.o;++c)b.push(a.e[c]);return b}return a.e},Jd:function(a){return a.e?a.e.subarray?a.e.subarray(0,a.o):new Uint8Array(a.e):new Uint8Array},Ia:function(a,b){a.e&&a.e.subarray&&b>a.e.length&&
(a.e=P.gb(a),a.o=a.e.length);if(!a.e||a.e.subarray){var c=a.e?a.e.buffer.byteLength:0;c>=b||(b=Math.max(b,c*(1048576>c?2:1.125)|0),0!=c&&(b=Math.max(b,256)),c=a.e,a.e=new Uint8Array(b),0<a.o&&a.e.set(c.subarray(0,a.o),0))}else for(!a.e&&0<b&&(a.e=[]);a.e.length<b;)a.e.push(0)},rb:function(a,b){if(a.o!=b)if(0==b)a.e=null,a.o=0;else{if(!a.e||a.e.subarray){var c=a.e;a.e=new Uint8Array(new ArrayBuffer(b));c&&a.e.set(c.subarray(0,Math.min(b,a.o)))}else if(a.e||(a.e=[]),a.e.length>b)a.e.length=b;else for(;a.e.length<
b;)a.e.push(0);a.o=b}},k:{D:function(a){var b={};b.dev=8192===(a.mode&61440)?a.id:1;b.ino=a.id;b.mode=a.mode;b.nlink=1;b.uid=0;b.gid=0;b.rdev=a.rdev;Q(a.mode)?b.size=4096:32768===(a.mode&61440)?b.size=a.o:40960===(a.mode&61440)?b.size=a.link.length:b.size=0;b.atime=new Date(a.timestamp);b.mtime=new Date(a.timestamp);b.ctime=new Date(a.timestamp);b.L=4096;b.blocks=Math.ceil(b.size/b.L);return b},u:function(a,b){void 0!==b.mode&&(a.mode=b.mode);void 0!==b.timestamp&&(a.timestamp=b.timestamp);void 0!==
b.size&&P.rb(a,b.size)},lookup:function(){throw Gb[N.F];},U:function(a,b,c,d){return P.createNode(a,b,c,d)},rename:function(a,b,c){if(Q(a.mode)){var d;try{d=Hb(b,c)}catch(f){}if(d)for(var g in d.e)throw new O(N.Aa);}delete a.parent.e[a.name];a.name=c;b.e[c]=a;a.parent=b},unlink:function(a,b){delete a.e[b]},rmdir:function(a,b){var c=Hb(a,b),d;for(d in c.e)throw new O(N.Aa);delete a.e[b]},readdir:function(a){var b=[".",".."],c;for(c in a.e)a.e.hasOwnProperty(c)&&b.push(c);return b},symlink:function(a,
b,c){a=P.createNode(a,b,41471,0);a.link=c;return a},readlink:function(a){if(40960!==(a.mode&61440))throw new O(N.p);return a.link}},n:{read:function(a,b,c,d,f){var g=a.g.e;if(f>=a.g.o)return 0;a=Math.min(a.g.o-f,d);assert(0<=a);if(8<a&&g.subarray)b.set(g.subarray(f,f+a),c);else for(d=0;d<a;d++)b[c+d]=g[f+d];return a},write:function(a,b,c,d,f,g){if(!d)return 0;a=a.g;a.timestamp=Date.now();if(b.subarray&&(!a.e||a.e.subarray)){if(g)return a.e=b.subarray(c,c+d),a.o=d;if(0===a.o&&0===f)return a.e=new Uint8Array(b.subarray(c,
c+d)),a.o=d;if(f+d<=a.o)return a.e.set(b.subarray(c,c+d),f),d}P.Ia(a,f+d);if(a.e.subarray&&b.subarray)a.e.set(b.subarray(c,c+d),f);else for(g=0;g<d;g++)a.e[f+g]=b[c+g];a.o=Math.max(a.o,f+d);return d},H:function(a,b,c){1===c?b+=a.position:2===c&&32768===(a.g.mode&61440)&&(b+=a.g.o);if(0>b)throw new O(N.p);return b},Da:function(a,b,c){P.Ia(a.g,b+c);a.g.o=Math.max(a.g.o,b+c)},Qa:function(a,b,c,d,f,g,h){if(32768!==(a.g.mode&61440))throw new O(N.ia);c=a.g.e;if(h&2||c.buffer!==b&&c.buffer!==b.buffer){if(0<
f||f+d<a.g.o)c.subarray?c=c.subarray(f,f+d):c=Array.prototype.slice.call(c,f,f+d);a=!0;d=H(d);if(!d)throw new O(N.Xa);b.set(c,d)}else a=!1,d=c.byteOffset;return{ob:d,zd:a}},Sa:function(a,b,c,d,f){if(32768!==(a.g.mode&61440))throw new O(N.ia);if(f&2)return 0;P.n.write(a,b,0,d,c,!1);return 0}}},S={ba:!1,vb:function(){S.ba=!!process.platform.match(/^win/)},A:function(a){assert(da);return S.createNode(null,"/",S.La(a.qa.root),0)},createNode:function(a,b,c){if(!Q(c)&&32768!==(c&61440)&&40960!==(c&61440))throw new O(N.p);
a=Fb(a,b,c);a.k=S.k;a.n=S.n;return a},La:function(a){var b;try{b=fs.lstatSync(a),S.ba&&(b.mode=b.mode|(b.mode&146)>>1)}catch(c){if(!c.code)throw c;throw new O(N[c.code]);}return b.mode},B:function(a){for(var b=[];a.parent!==a;)b.push(a.name),a=a.parent;b.push(a.A.qa.root);b.reverse();return Ib.apply(null,b)},Ja:{0:"r",1:"r+",2:"r+",64:"r",65:"r+",66:"r+",129:"rx+",193:"rx+",514:"w+",577:"w",578:"w+",705:"wx",706:"wx+",1024:"a",1025:"a",1026:"a+",1089:"a",1090:"a+",1153:"ax",1154:"ax+",1217:"ax",1218:"ax+",
4096:"rs",4098:"rs+"},eb:function(a){a&=-557057;if(a in S.Ja)return S.Ja[a];throw new O(N.p);},k:{D:function(a){a=S.B(a);var b;try{b=fs.lstatSync(a)}catch(c){if(!c.code)throw c;throw new O(N[c.code]);}S.ba&&!b.L&&(b.L=4096);S.ba&&!b.blocks&&(b.blocks=(b.size+b.L-1)/b.L|0);return{dev:b.dev,ino:b.ino,mode:b.mode,nlink:b.nlink,uid:b.uid,gid:b.gid,rdev:b.rdev,size:b.size,atime:b.atime,mtime:b.mtime,ctime:b.ctime,L:b.L,blocks:b.blocks}},u:function(a,b){var c=S.B(a);try{void 0!==b.mode&&(fs.chmodSync(c,
b.mode),a.mode=b.mode),void 0!==b.size&&fs.truncateSync(c,b.size)}catch(d){if(!d.code)throw d;throw new O(N[d.code]);}},lookup:function(a,b){var c=T(S.B(a),b),c=S.La(c);return S.createNode(a,b,c)},U:function(a,b,c,d){a=S.createNode(a,b,c,d);b=S.B(a);try{Q(a.mode)?fs.mkdirSync(b,a.mode):fs.writeFileSync(b,"",{mode:a.mode})}catch(f){if(!f.code)throw f;throw new O(N[f.code]);}return a},rename:function(a,b,c){a=S.B(a);b=T(S.B(b),c);try{fs.renameSync(a,b)}catch(d){if(!d.code)throw d;throw new O(N[d.code]);
}},unlink:function(a,b){var c=T(S.B(a),b);try{fs.unlinkSync(c)}catch(d){if(!d.code)throw d;throw new O(N[d.code]);}},rmdir:function(a,b){var c=T(S.B(a),b);try{fs.rmdirSync(c)}catch(d){if(!d.code)throw d;throw new O(N[d.code]);}},readdir:function(a){a=S.B(a);try{return fs.readdirSync(a)}catch(b){if(!b.code)throw b;throw new O(N[b.code]);}},symlink:function(a,b,c){a=T(S.B(a),b);try{fs.symlinkSync(c,a)}catch(d){if(!d.code)throw d;throw new O(N[d.code]);}},readlink:function(a){var b=S.B(a);try{return b=
fs.readlinkSync(b),b=Jb.relative(Jb.resolve(a.A.qa.root),b)}catch(c){if(!c.code)throw c;throw new O(N[c.code]);}}},n:{open:function(a){var b=S.B(a.g);try{32768===(a.g.mode&61440)&&(a.W=fs.openSync(b,S.eb(a.flags)))}catch(c){if(!c.code)throw c;throw new O(N[c.code]);}},close:function(a){try{32768===(a.g.mode&61440)&&a.W&&fs.closeSync(a.W)}catch(b){if(!b.code)throw b;throw new O(N[b.code]);}},read:function(a,b,c,d,f){if(0===d)return 0;var g=new Buffer(d),h;try{h=fs.readSync(a.W,g,0,d,f)}catch(k){throw new O(N[k.code]);
}if(0<h)for(a=0;a<h;a++)b[c+a]=g[a];return h},write:function(a,b,c,d,f){b=new Buffer(b.subarray(c,c+d));var g;try{g=fs.writeSync(a.W,b,0,d,f)}catch(h){throw new O(N[h.code]);}return g},H:function(a,b,c){if(1===c)b+=a.position;else if(2===c&&32768===(a.g.mode&61440))try{b+=fs.fstatSync(a.W).size}catch(d){throw new O(N[d.code]);}if(0>b)throw new O(N.p);return b}}};G(1,"i32*",2);G(1,"i32*",2);G(1,"i32*",2);var Kb=null,Lb=[null],Mb=[],Nb=1,Ob=null,Pb=!0,Rb={},O=null,Gb={};
function U(a,b){a=Sb("/",a);b=b||{};if(!a)return{path:"",g:null};var c={Ka:!0,sa:0},d;for(d in c)void 0===b[d]&&(b[d]=c[d]);if(8<b.sa)throw new O(N.ha);var c=Tb(a.split("/").filter(function(a){return!!a}),!1),f=Kb;d="/";for(var g=0;g<c.length;g++){var h=g===c.length-1;if(h&&b.parent)break;f=Hb(f,c[g]);d=T(d,c[g]);f.V&&(!h||h&&b.Ka)&&(f=f.V.root);if(!h||b.la)for(h=0;40960===(f.mode&61440);)if(f=Ub(d),d=Sb(Vb(d),f),f=U(d,{sa:b.sa}).g,40<h++)throw new O(N.ha);}return{path:d,g:f}}
function Xb(a){for(var b;;){if(a===a.parent)return a=a.A.Ra,b?"/"!==a[a.length-1]?a+"/"+b:a+b:a;b=b?a.name+"/"+b:a.name;a=a.parent}}function Yb(a,b){for(var c=0,d=0;d<b.length;d++)c=(c<<5)-c+b.charCodeAt(d)|0;return(a+c>>>0)%Ob.length}function Zb(a){var b=Yb(a.parent.id,a.name);a.P=Ob[b];Ob[b]=a}function Hb(a,b){var c;if(c=(c=$b(a,"x"))?c:a.k.lookup?0:N.fa)throw new O(c,a);for(c=Ob[Yb(a.id,b)];c;c=c.P){var d=c.name;if(c.parent.id===a.id&&d===b)return c}return a.k.lookup(a,b)}
function Fb(a,b,c,d){ac||(ac=function(a,b,c,d){a||(a=this);this.parent=a;this.A=a.A;this.V=null;this.id=Nb++;this.name=b;this.mode=c;this.k={};this.n={};this.rdev=d},ac.prototype={},Object.defineProperties(ac.prototype,{read:{get:function(){return 365===(this.mode&365)},set:function(a){a?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146===(this.mode&146)},set:function(a){a?this.mode|=146:this.mode&=-147}},mb:{get:function(){return Q(this.mode)}},lb:{get:function(){return 8192===(this.mode&
61440)}}}));a=new ac(a,b,c,d);Zb(a);return a}function Q(a){return 16384===(a&61440)}var bc={r:0,rs:1052672,"r+":2,w:577,wx:705,xw:705,"w+":578,"wx+":706,"xw+":706,a:1089,ax:1217,xa:1217,"a+":1090,"ax+":1218,"xa+":1218};function cc(a){var b=["r","w","rw"][a&3];a&512&&(b+="w");return b}function $b(a,b){if(Pb)return 0;if(-1===b.indexOf("r")||a.mode&292){if(-1!==b.indexOf("w")&&!(a.mode&146)||-1!==b.indexOf("x")&&!(a.mode&73))return N.fa}else return N.fa;return 0}
function dc(a,b){try{return Hb(a,b),N.ya}catch(c){}return $b(a,"wx")}function ec(a){var b;b=4096;for(a=a||0;a<=b;a++)if(!Mb[a])return a;throw new O(N.Wa);}
function fc(a,b){gc||(gc=function(){},gc.prototype={},Object.defineProperties(gc.prototype,{object:{get:function(){return this.g},set:function(a){this.g=a}},Od:{get:function(){return 1!==(this.flags&2097155)}},Pd:{get:function(){return 0!==(this.flags&2097155)}},Nd:{get:function(){return this.flags&1024}}}));var c=new gc,d;for(d in a)c[d]=a[d];a=c;c=ec(b);a.fd=c;return Mb[c]=a}var Eb={open:function(a){a.n=Lb[a.g.rdev].n;a.n.open&&a.n.open(a)},H:function(){throw new O(N.Z);}};
function Ab(a,b){Lb[a]={n:b}}function hc(a,b){var c="/"===b,d=!b,f;if(c&&Kb)throw new O(N.ga);if(!c&&!d){f=U(b,{Ka:!1});b=f.path;f=f.g;if(f.V)throw new O(N.ga);if(!Q(f.mode))throw new O(N.za);}var d={type:a,qa:{},Ra:b,nb:[]},g=a.A(d);g.A=d;d.root=g;c?Kb=g:f&&(f.V=d,f.A&&f.A.nb.push(d))}function ic(a,b,c){var d=U(a,{parent:!0}).g;a=jc(a);if(!a||"."===a||".."===a)throw new O(N.p);var f=dc(d,a);if(f)throw new O(f);if(!d.k.U)throw new O(N.J);return d.k.U(d,a,b,c)}
function kc(a,b){b=(void 0!==b?b:438)&4095;b|=32768;return ic(a,b,0)}function V(a,b){b=(void 0!==b?b:511)&1023;b|=16384;return ic(a,b,0)}function lc(a,b,c){"undefined"===typeof c&&(c=b,b=438);return ic(a,b|8192,c)}function mc(a,b){if(!Sb(a))throw new O(N.F);var c=U(b,{parent:!0}).g;if(!c)throw new O(N.F);var d=jc(b),f=dc(c,d);if(f)throw new O(f);if(!c.k.symlink)throw new O(N.J);return c.k.symlink(c,d,a)}
function Ub(a){a=U(a).g;if(!a)throw new O(N.F);if(!a.k.readlink)throw new O(N.p);return Sb(Xb(a.parent),a.k.readlink(a))}function nc(a,b){var c;"string"===typeof a?c=U(a,{la:!0}).g:c=a;if(!c.k.u)throw new O(N.J);c.k.u(c,{mode:b&4095|c.mode&-4096,timestamp:Date.now()})}
function oc(a,b,c,d){if(""===a)throw new O(N.F);if("string"===typeof b){var f=bc[b];if("undefined"===typeof f)throw Error("Unknown file open mode: "+b);b=f}c=b&64?("undefined"===typeof c?438:c)&4095|32768:0;var g;if("object"===typeof a)g=a;else{a=pc(a);try{g=U(a,{la:!(b&131072)}).g}catch(h){}}f=!1;if(b&64)if(g){if(b&128)throw new O(N.ya);}else g=ic(a,c,0),f=!0;if(!g)throw new O(N.F);8192===(g.mode&61440)&&(b&=-513);if(b&65536&&!Q(g.mode))throw new O(N.za);if(!f&&(c=g?40960===(g.mode&61440)?N.ha:Q(g.mode)&&
("r"!==cc(b)||b&512)?N.N:$b(g,cc(b)):N.F))throw new O(c);if(b&512){c=g;var k;"string"===typeof c?k=U(c,{la:!0}).g:k=c;if(!k.k.u)throw new O(N.J);if(Q(k.mode))throw new O(N.N);if(32768!==(k.mode&61440))throw new O(N.p);if(c=$b(k,"w"))throw new O(c);k.k.u(k,{size:0,timestamp:Date.now()})}b&=-641;d=fc({g:g,path:Xb(g),flags:b,seekable:!0,position:0,n:g.n,wb:[],error:!1},d);d.n.open&&d.n.open(d);!e.logReadFiles||b&1||(qc||(qc={}),a in qc||(qc[a]=1,e.printErr("read file: "+a)));try{Rb.onOpenFile&&(g=0,
1!==(b&2097155)&&(g|=1),0!==(b&2097155)&&(g|=2),Rb.onOpenFile(a,g))}catch(u){console.log("FS.trackingDelegate['onOpenFile']('"+a+"', flags) threw an exception: "+u.message)}return d}function rc(a){a.oa&&(a.oa=null);try{a.n.close&&a.n.close(a)}catch(b){throw b;}finally{Mb[a.fd]=null}}function sc(a,b,c){if(!a.seekable||!a.n.H)throw new O(N.Z);a.position=a.n.H(a,b,c);a.wb=[]}
function tc(a,b,c,d,f,g){if(0>d||0>f)throw new O(N.p);if(0===(a.flags&2097155))throw new O(N.Y);if(Q(a.g.mode))throw new O(N.N);if(!a.n.write)throw new O(N.p);a.flags&1024&&sc(a,0,2);var h=!0;if("undefined"===typeof f)f=a.position,h=!1;else if(!a.seekable)throw new O(N.Z);b=a.n.write(a,b,c,d,f,g);h||(a.position+=b);try{if(a.path&&Rb.onWriteToFile)Rb.onWriteToFile(a.path)}catch(k){console.log("FS.trackingDelegate['onWriteToFile']('"+path+"') threw an exception: "+k.message)}return b}
function uc(){O||(O=function(a,b){this.g=b;this.tb=function(a){this.G=a;for(var b in N)if(N[b]===a){this.code=b;break}};this.tb(a);this.message=xb[a]},O.prototype=Error(),O.prototype.constructor=O,[N.F].forEach(function(a){Gb[a]=new O(a);Gb[a].stack="<generic error, no stack>"}))}var vc;function wc(a,b){var c=0;a&&(c|=365);b&&(c|=146);return c}function xc(a,b,c,d){a=T("string"===typeof a?a:Xb(a),b);return kc(a,wc(c,d))}
function yc(a,b,c,d,f,g){a=b?T("string"===typeof a?a:Xb(a),b):a;d=wc(d,f);f=kc(a,d);if(c){if("string"===typeof c){a=Array(c.length);b=0;for(var h=c.length;b<h;++b)a[b]=c.charCodeAt(b);c=a}nc(f,d|146);a=oc(f,"w");tc(a,c,0,c.length,0,g);rc(a);nc(f,d)}return f}
function W(a,b,c,d){a=T("string"===typeof a?a:Xb(a),b);b=wc(!!c,!!d);W.Pa||(W.Pa=64);var f=W.Pa++<<8|0;Ab(f,{open:function(a){a.seekable=!1},close:function(){d&&d.buffer&&d.buffer.length&&d(10)},read:function(a,b,d,f){for(var r=0,p=0;p<f;p++){var w;try{w=c()}catch(z){throw new O(N.I);}if(void 0===w&&0===r)throw new O(N.wa);if(null===w||void 0===w)break;r++;b[d+p]=w}r&&(a.g.timestamp=Date.now());return r},write:function(a,b,c,f){for(var r=0;r<f;r++)try{d(b[c+r])}catch(p){throw new O(N.I);}f&&(a.g.timestamp=
Date.now());return r}});return lc(a,b,f)}
function zc(a){if(a.lb||a.mb||a.link||a.e)return!0;var b=!0;if("undefined"!==typeof XMLHttpRequest)throw Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(e.read)try{a.e=$a(e.read(a.url),!0),a.o=a.e.length}catch(c){b=!1}else throw Error("Cannot load without read() or XMLHttpRequest.");b||vb(N.I);return b}var Ac={},ac,gc,qc;
function Tb(a,b){for(var c=0,d=a.length-1;0<=d;d--){var f=a[d];"."===f?a.splice(d,1):".."===f?(a.splice(d,1),c++):c&&(a.splice(d,1),c--)}if(b)for(;c--;c)a.unshift("..");return a}function pc(a){var b="/"===a.charAt(0),c="/"===a.substr(-1);(a=Tb(a.split("/").filter(function(a){return!!a}),!b).join("/"))||b||(a=".");a&&c&&(a+="/");return(b?"/":"")+a}
function Vb(a){var b=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(a).slice(1);a=b[0];b=b[1];if(!a&&!b)return".";b&&(b=b.substr(0,b.length-1));return a+b}function jc(a){if("/"===a)return"/";var b=a.lastIndexOf("/");return-1===b?a:a.substr(b+1)}function Ib(){var a=Array.prototype.slice.call(arguments,0);return pc(a.join("/"))}function T(a,b){return pc(a+"/"+b)}
function Sb(){for(var a="",b=!1,c=arguments.length-1;-1<=c&&!b;c--){b=0<=c?arguments[c]:"/";if("string"!==typeof b)throw new TypeError("Arguments to path.resolve must be strings");if(!b)return"";a=b+"/"+a;b="/"===b.charAt(0)}a=Tb(a.split("/").filter(function(a){return!!a}),!b).join("/");return(b?"/":"")+a||"."}
function Bc(a,b){Cc=a;Dc=b;if(!Ec)return 1;if(0==a)Fc=function(){setTimeout(Gc,b)},Hc="timeout";else if(1==a)Fc=function(){Ic(Gc)},Hc="rAF";else if(2==a){if(!window.setImmediate){var c=[];window.addEventListener("message",function(a){a.source===window&&"__emcc"===a.data&&(a.stopPropagation(),c.shift()())},!0);window.setImmediate=function(a){c.push(a);window.postMessage("__emcc","*")}}Fc=function(){window.setImmediate(Gc)};Hc="immediate"}return 0}
function Jc(a,b,c,d,f){e.noExitRuntime=!0;assert(!Ec,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters.");Ec=a;Kc=d;var g=Lc;Gc=function(){if(!y)if(0<Mc.length){var b=Date.now(),c=Mc.shift();c.ma(c.T);if(Nc){var f=Nc,r=0==f%1?f-1:Math.floor(f);Nc=c.Dd?r:(8*f+(r+.5))/9}console.log('main loop blocker "'+c.name+'" took '+(Date.now()-b)+" ms");Oc();setTimeout(Gc,0)}else g<
Lc||(Pc=Pc+1|0,1==Cc&&1<Dc&&0!=Pc%Dc?Fc():("timeout"===Hc&&e.ka&&(e.X("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),Hc=""),Qc(function(){"undefined"!==typeof d?n.M("vi",a,[d]):n.M("v",a)}),g<Lc||("object"===typeof SDL&&SDL.audio&&SDL.audio.pb&&SDL.audio.pb(),Fc())))};f||(b&&0<b?Bc(0,1E3/b):Bc(1,1),Fc());if(c)throw"SimulateInfiniteLoop";
}var Fc=null,Hc="",Lc=0,Ec=null,Kc=0,Cc=0,Dc=0,Pc=0,Mc=[];function Oc(){if(e.setStatus){var a=e.statusMessage||"Please wait...",b=Nc,c=Rc.Gd;b?b<c?e.setStatus(a+" ("+(c-b)+"/"+c+")"):e.setStatus(a):e.setStatus("")}}function Qc(a){if(!(y||e.preMainLoop&&!1===e.preMainLoop())){try{a()}catch(b){if(b instanceof ia)return;b&&"object"===typeof b&&b.stack&&e.X("exception thrown: "+[b,b.stack]);throw b;}e.postMainLoop&&e.postMainLoop()}}var Rc={},Gc,Nc,Sc=!1,Tc=!1,Uc=[];
function Vc(){function a(){Tc=document.pointerLockElement===c||document.mozPointerLockElement===c||document.webkitPointerLockElement===c||document.msPointerLockElement===c}e.preloadPlugins||(e.preloadPlugins=[]);if(!Wc){Wc=!0;try{Xc=!0}catch(b){Xc=!1,console.log("warning: no blob constructor, cannot create blobs with mimetypes")}Yc="undefined"!=typeof MozBlobBuilder?MozBlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:Xc?null:console.log("warning: no BlobBuilder");Zc="undefined"!=
typeof window?window.URL?window.URL:window.webkitURL:void 0;e.Ta||"undefined"!==typeof Zc||(console.log("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available."),e.Ta=!0);e.preloadPlugins.push({canHandle:function(a){return!e.Ta&&/\.(jpg|jpeg|png|bmp)$/i.test(a)},handle:function(a,b,c,h){var k=null;if(Xc)try{k=new Blob([a],{type:$c(b)}),k.size!==a.length&&(k=new Blob([(new Uint8Array(a)).buffer],{type:$c(b)}))}catch(u){n.R("Blob constructor present but fails: "+
u+"; falling back to blob builder")}k||(k=new Yc,k.append((new Uint8Array(a)).buffer),k=k.getBlob());var r=Zc.createObjectURL(k),p=new Image;p.onload=function(){assert(p.complete,"Image "+b+" could not be decoded");var h=document.createElement("canvas");h.width=p.width;h.height=p.height;h.getContext("2d").drawImage(p,0,0);e.preloadedImages[b]=h;Zc.revokeObjectURL(r);c&&c(a)};p.onerror=function(){console.log("Image "+r+" could not be decoded");h&&h()};p.src=r}});e.preloadPlugins.push({canHandle:function(a){return!e.Sd&&
a.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},handle:function(a,b,c,h){function k(h){r||(r=!0,e.preloadedAudios[b]=h,c&&c(a))}function u(){r||(r=!0,e.preloadedAudios[b]=new Audio,h&&h())}var r=!1;if(Xc){try{var p=new Blob([a],{type:$c(b)})}catch(w){return u()}var p=Zc.createObjectURL(p),z=new Audio;z.addEventListener("canplaythrough",function(){k(z)},!1);z.onerror=function(){if(!r){console.log("warning: browser could not fully decode audio "+b+", trying slower base64 approach");for(var c="",g=0,h=0,
p=0;p<a.length;p++)for(g=g<<8|a[p],h+=8;6<=h;)var w=g>>h-6&63,h=h-6,c=c+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[w];2==h?(c+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[(g&3)<<4],c+="=="):4==h&&(c+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[(g&15)<<2],c+="=");z.src="data:audio/x-"+b.substr(-3)+";base64,"+c;k(z)}};z.src=p;ad(function(){k(z)})}else return u()}});var c=e.canvas;c&&(c.ta=c.requestPointerLock||c.mozRequestPointerLock||
c.webkitRequestPointerLock||c.msRequestPointerLock||function(){},c.Ha=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||function(){},c.Ha=c.Ha.bind(document),document.addEventListener("pointerlockchange",a,!1),document.addEventListener("mozpointerlockchange",a,!1),document.addEventListener("webkitpointerlockchange",a,!1),document.addEventListener("mspointerlockchange",a,!1),e.elementPointerLock&&c.addEventListener("click",function(a){!Tc&&
c.ta&&(c.ta(),a.preventDefault())},!1))}}function bd(a,b,c,d){if(b&&e.ka&&a==e.canvas)return e.ka;var f,g;if(b){g={antialias:!1,alpha:!1};if(d)for(var h in d)g[h]=d[h];if(g=GL.createContext(a,g))f=GL.getContext(g).wd;a.style.backgroundColor="black"}else f=a.getContext("2d");if(!f)return null;c&&(b||assert("undefined"===typeof GLctx,"cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),e.ka=f,b&&GL.Rd(g),e.Ud=b,Uc.forEach(function(a){a()}),Vc());return f}
var cd=!1,dd=void 0,ed=void 0;
function fd(a,b,c){function d(){Sc=!1;var a=f.parentNode;(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===a?(f.Fa=document.cancelFullScreen||document.mozCancelFullScreen||document.webkitCancelFullScreen||document.msExitFullscreen||document.exitFullscreen||function(){},
f.Fa=f.Fa.bind(document),dd&&f.ta(),Sc=!0,ed&&gd()):(a.parentNode.insertBefore(f,a),a.parentNode.removeChild(a),ed&&hd());if(e.onFullScreen)e.onFullScreen(Sc);id(f)}dd=a;ed=b;jd=c;"undefined"===typeof dd&&(dd=!0);"undefined"===typeof ed&&(ed=!1);"undefined"===typeof jd&&(jd=null);var f=e.canvas;cd||(cd=!0,document.addEventListener("fullscreenchange",d,!1),document.addEventListener("mozfullscreenchange",d,!1),document.addEventListener("webkitfullscreenchange",d,!1),document.addEventListener("MSFullscreenChange",
d,!1));var g=document.createElement("div");f.parentNode.insertBefore(g,f);g.appendChild(f);g.q=g.requestFullScreen||g.mozRequestFullScreen||g.msRequestFullscreen||(g.webkitRequestFullScreen?function(){g.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:null);c?g.q({Vd:c}):g.q()}var kd=0;function ld(a){var b=Date.now();if(0===kd)kd=b+1E3/60;else for(;b+2>=kd;)kd+=1E3/60;b=Math.max(kd-b,0);setTimeout(a,b)}
function Ic(a){"undefined"===typeof window?ld(a):(window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||ld),window.requestAnimationFrame(a))}function ad(a){e.noExitRuntime=!0;setTimeout(function(){y||a()},1E4)}
function $c(a){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[a.substr(a.lastIndexOf(".")+1)]}function md(a,b,c){var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";d.onload=function(){200==d.status||0==d.status&&d.response?b(d.response):c()};d.onerror=c;d.send(null)}
function nd(a,b,c){md(a,function(c){assert(c,'Loading data file "'+a+'" failed (no arrayBuffer).');b(new Uint8Array(c));fb()},function(){if(c)c();else throw'Loading data file "'+a+'" failed.';});eb()}var od=[];function pd(){var a=e.canvas;od.forEach(function(b){b(a.width,a.height)})}function gd(){if("undefined"!=typeof SDL){var a=Na[SDL.screen+0*n.K>>2];F[SDL.screen+0*n.K>>2]=a|8388608}pd()}
function hd(){if("undefined"!=typeof SDL){var a=Na[SDL.screen+0*n.K>>2];F[SDL.screen+0*n.K>>2]=a&-8388609}pd()}
function id(a,b,c){b&&c?(a.xb=b,a.jb=c):(b=a.xb,c=a.jb);var d=b,f=c;e.forcedAspectRatio&&0<e.forcedAspectRatio&&(d/f<e.forcedAspectRatio?d=Math.round(f*e.forcedAspectRatio):f=Math.round(d/e.forcedAspectRatio));if((document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===
a.parentNode&&"undefined"!=typeof screen)var g=Math.min(screen.width/d,screen.height/f),d=Math.round(d*g),f=Math.round(f*g);ed?(a.width!=d&&(a.width=d),a.height!=f&&(a.height=f),"undefined"!=typeof a.style&&(a.style.removeProperty("width"),a.style.removeProperty("height"))):(a.width!=b&&(a.width=b),a.height!=c&&(a.height=c),"undefined"!=typeof a.style&&(d!=b||f!=c?(a.style.setProperty("width",d+"px","important"),a.style.setProperty("height",f+"px","important")):(a.style.removeProperty("width"),a.style.removeProperty("height"))))}
var Wc,Xc,Yc,Zc,jd;function qd(a){e.exit(a)}function H(a){return n.O(a+8)+8&4294967288}e._malloc=H;var X=0;function Y(){X+=4;return F[X-4>>2]}function rd(){var a;a=Y();a=Mb[a];if(!a)throw new O(N.Y);return a}e._i64Add=sd;e._bitshift64Lshr=td;function ud(a,b){L.push(function(){n.M("vi",a,[b])});ud.level=L.length}e._memcpy=vd;function Ba(a){Ba.q||(t=La(),Ba.q=!0,assert(n.O),Ba.fb=n.O,n.O=function(){x("cannot dynamically allocate, sbrk now has control")});var b=t;return 0==a||Ba.fb(a)?b:4294967295}
var wd=G(1,"i32*",2);e.requestFullScreen=function(a,b,c){fd(a,b,c)};e.requestAnimationFrame=function(a){Ic(a)};e.setCanvasSize=function(a,b,c){id(e.canvas,a,b);c||pd()};e.pauseMainLoop=function(){Fc=null;Lc++};e.resumeMainLoop=function(){Lc++;var a=Cc,b=Dc,c=Ec;Ec=null;Jc(c,0,!1,Kc,!0);Bc(a,b);Fc()};e.getUserMedia=function(){window.q||(window.q=navigator.getUserMedia||navigator.mozGetUserMedia);window.q(void 0)};e.createContext=function(a,b,c,d){return bd(a,b,c,d)};uc();Ob=Array(4096);hc(P,"/");V("/tmp");
V("/home");V("/home/<USER>");(function(){V("/dev");Ab(259,{read:function(){return 0},write:function(a,b,f,g){return g}});lc("/dev/null",259);zb(1280,Cb);zb(1536,Db);lc("/dev/tty",1280);lc("/dev/tty1",1536);var a;if("undefined"!==typeof crypto){var b=new Uint8Array(1);a=function(){crypto.getRandomValues(b);return b[0]}}else a=da?function(){return require("crypto").randomBytes(1)[0]}:function(){return 256*Math.random()|0};W("/dev","random",a);W("/dev","urandom",a);V("/dev/shm");V("/dev/shm/tmp")})();
V("/proc");V("/proc/self");V("/proc/self/fd");hc({A:function(){var a=Fb("/proc/self","fd",16895,73);a.k={lookup:function(a,c){var d=Mb[+c];if(!d)throw new O(N.Y);var f={parent:null,A:{Ra:"fake"},k:{readlink:function(){return d.path}}};return f.parent=f}};return a}},"/proc/self/fd");
Va.unshift(function(){if(!e.noFSInit&&!vc){assert(!vc,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)");vc=!0;uc();e.stdin=e.stdin;e.stdout=e.stdout;e.stderr=e.stderr;e.stdin?W("/dev","stdin",e.stdin):mc("/dev/tty","/dev/stdin");e.stdout?W("/dev","stdout",null,e.stdout):mc("/dev/tty","/dev/stdout");e.stderr?W("/dev","stderr",null,e.stderr):mc("/dev/tty1","/dev/stderr");var a=
oc("/dev/stdin","r");assert(0===a.fd,"invalid handle for stdin ("+a.fd+")");a=oc("/dev/stdout","w");assert(1===a.fd,"invalid handle for stdout ("+a.fd+")");a=oc("/dev/stderr","w");assert(2===a.fd,"invalid handle for stderr ("+a.fd+")")}});Wa.push(function(){Pb=!1});L.push(function(){vc=!1;var a=e._fflush;a&&a(0);for(a=0;a<Mb.length;a++){var b=Mb[a];b&&rc(b)}});e.FS_createFolder=function(a,b,c,d){a=T("string"===typeof a?a:Xb(a),b);return V(a,wc(c,d))};
e.FS_createPath=function(a,b){a="string"===typeof a?a:Xb(a);for(var c=b.split("/").reverse();c.length;){var d=c.pop();if(d){var f=T(a,d);try{V(f)}catch(g){}a=f}}return f};e.FS_createDataFile=yc;
e.FS_createPreloadedFile=function(a,b,c,d,f,g,h,k,u,r){function p(c){function p(c){r&&r();k||yc(a,b,c,d,f,u);g&&g();fb()}var J=!1;e.preloadPlugins.forEach(function(a){!J&&a.canHandle(w)&&(a.handle(c,w,p,function(){h&&h();fb()}),J=!0)});J||p(c)}Vc();var w=b?Sb(T(a,b)):a;eb();"string"==typeof c?nd(c,function(a){p(a)},h):p(c)};
e.FS_createLazyFile=function(a,b,c,d,f){var g,h;function k(){this.pa=!1;this.$=[]}k.prototype.get=function(a){if(!(a>this.length-1||0>a)){var b=a%this.chunkSize;return this.Oa(a/this.chunkSize|0)[b]}};k.prototype.sb=function(a){this.Oa=a};k.prototype.Ea=function(){var a=new XMLHttpRequest;a.open("HEAD",c,!1);a.send(null);if(!(200<=a.status&&300>a.status||304===a.status))throw Error("Couldn't load "+c+". Status: "+a.status);var b=Number(a.getResponseHeader("Content-length")),d,f=(d=a.getResponseHeader("Accept-Ranges"))&&
"bytes"===d,a=(d=a.getResponseHeader("Content-Encoding"))&&"gzip"===d,g=1048576;f||(g=b);var h=this;h.sb(function(a){var d=a*g,f=(a+1)*g-1,f=Math.min(f,b-1);if("undefined"===typeof h.$[a]){var k=h.$;if(d>f)throw Error("invalid range ("+d+", "+f+") or no bytes requested!");if(f>b-1)throw Error("only "+b+" bytes available! programmer error!");var p=new XMLHttpRequest;p.open("GET",c,!1);b!==g&&p.setRequestHeader("Range","bytes="+d+"-"+f);"undefined"!=typeof Uint8Array&&(p.responseType="arraybuffer");
p.overrideMimeType&&p.overrideMimeType("text/plain; charset=x-user-defined");p.send(null);if(!(200<=p.status&&300>p.status||304===p.status))throw Error("Couldn't load "+c+". Status: "+p.status);d=void 0!==p.response?new Uint8Array(p.response||[]):$a(p.responseText||"",!0);k[a]=d}if("undefined"===typeof h.$[a])throw Error("doXHR failed!");return h.$[a]});if(a||!b)g=b=1,g=b=this.Oa(0).length,console.log("LazyFiles on gzip forces download of the whole file when length is accessed");this.$a=b;this.Za=
g;this.pa=!0};if("undefined"!==typeof XMLHttpRequest){if(!ca)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";g=new k;Object.defineProperties(g,{length:{get:function(){this.pa||this.Ea();return this.$a}},chunkSize:{get:function(){this.pa||this.Ea();return this.Za}}});h=void 0}else h=c,g=void 0;var u=xc(a,b,d,f);g?u.e=g:h&&(u.e=null,u.url=h);Object.defineProperties(u,{o:{get:function(){return this.e.length}}});var r={};Object.keys(u.n).forEach(function(a){var b=
u.n[a];r[a]=function(){if(!zc(u))throw new O(N.I);return b.apply(null,arguments)}});r.read=function(a,b,c,d,f){if(!zc(u))throw new O(N.I);a=a.g.e;if(f>=a.length)return 0;d=Math.min(a.length-f,d);assert(0<=d);if(a.slice)for(var g=0;g<d;g++)b[c+g]=a[f+g];else for(g=0;g<d;g++)b[c+g]=a.get(f+g);return d};u.n=r;return u};e.FS_createLink=function(a,b,c){a=T("string"===typeof a?a:Xb(a),b);return mc(c,a)};e.FS_createDevice=W;
e.FS_unlink=function(a){var b=U(a,{parent:!0}).g,c=jc(a),d=Hb(b,c),f;a:{try{f=Hb(b,c)}catch(g){f=g.G;break a}var h=$b(b,"wx");f=h?h:Q(f.mode)?N.N:0}if(f)throw f===N.N&&(f=N.J),new O(f);if(!b.k.unlink)throw new O(N.J);if(d.V)throw new O(N.ga);try{Rb.willDeletePath&&Rb.willDeletePath(a)}catch(k){console.log("FS.trackingDelegate['willDeletePath']('"+a+"') threw an exception: "+k.message)}b.k.unlink(b,c);b=Yb(d.parent.id,d.name);if(Ob[b]===d)Ob[b]=d.P;else for(b=Ob[b];b;){if(b.P===d){b.P=d.P;break}b=
b.P}try{if(Rb.onDeletePath)Rb.onDeletePath(a)}catch(u){console.log("FS.trackingDelegate['onDeletePath']('"+a+"') threw an exception: "+u.message)}};Va.unshift(function(){});L.push(function(){});if(da){var fs=require("fs"),Jb=require("path");S.vb()}Pa=m=n.ja(ka);Aa=!0;Qa=Pa+Sa;Ra=t=n.ja(Qa);assert(Ra<v,"TOTAL_MEMORY not big enough for stack");
var xd=G([8,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,7,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,
1,0,3,0,1,0,2,0,1,0],"i8",3);e.bb={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:Infinity};
e.cb={abort:x,assert:assert,invoke_iiii:function(a,b,c,d){try{return e.dynCall_iiii(a,b,c,d)}catch(f){if("number"!==typeof f&&"longjmp"!==f)throw f;M.setThrew(1,0)}},invoke_viiiii:function(a,b,c,d,f,g){try{e.dynCall_viiiii(a,b,c,d,f,g)}catch(h){if("number"!==typeof h&&"longjmp"!==h)throw h;M.setThrew(1,0)}},invoke_vi:function(a,b){try{e.dynCall_vi(a,b)}catch(c){if("number"!==typeof c&&"longjmp"!==c)throw c;M.setThrew(1,0)}},invoke_vii:function(a,b,c){try{e.dynCall_vii(a,b,c)}catch(d){if("number"!==
typeof d&&"longjmp"!==d)throw d;M.setThrew(1,0)}},invoke_ii:function(a,b){try{return e.dynCall_ii(a,b)}catch(c){if("number"!==typeof c&&"longjmp"!==c)throw c;M.setThrew(1,0)}},invoke_v:function(a){try{e.dynCall_v(a)}catch(b){if("number"!==typeof b&&"longjmp"!==b)throw b;M.setThrew(1,0)}},invoke_iiiii:function(a,b,c,d,f){try{return e.dynCall_iiiii(a,b,c,d,f)}catch(g){if("number"!==typeof g&&"longjmp"!==g)throw g;M.setThrew(1,0)}},invoke_viiiiii:function(a,b,c,d,f,g,h){try{e.dynCall_viiiiii(a,b,c,d,
f,g,h)}catch(k){if("number"!==typeof k&&"longjmp"!==k)throw k;M.setThrew(1,0)}},invoke_viiii:function(a,b,c,d,f){try{e.dynCall_viiii(a,b,c,d,f)}catch(g){if("number"!==typeof g&&"longjmp"!==g)throw g;M.setThrew(1,0)}},_pthread_cleanup_pop:function(){assert(ud.level==L.length,"cannot pop if something else added meanwhile!");L.pop();ud.level=L.length},___syscall221:function(a,b){X=b;try{var c=rd();switch(Y()){case 0:var d=Y();return 0>d?-N.p:oc(c.path,c.flags,0,d).fd;case 1:case 2:return 0;case 3:return c.flags;
case 4:return d=Y(),c.flags|=d,0;case 12:case 12:return d=Y(),E[d+0>>1]=2,0;case 13:case 14:case 13:case 14:return 0;case 16:case 8:return-N.p;case 9:return vb(N.p),-1;default:return-N.p}}catch(f){return"undefined"!==typeof Ac&&f instanceof O||x(f),-f.G}},_exp:ab,___syscall54:function(a,b){X=b;try{var c=rd(),d=Y();switch(d){case 21505:return c.tty?0:-N.S;case 21506:return c.tty?0:-N.S;case 21519:if(!c.tty)return-N.S;var f=Y();return F[f>>2]=0;case 21520:return c.tty?-N.p:-N.S;case 21531:f=Y();if(!c.n.kb)throw new O(N.S);
return c.n.kb(c,d,f);default:x("bad ioctl syscall "+d)}}catch(g){return"undefined"!==typeof Ac&&g instanceof O||x(g),-g.G}},_pthread_cleanup_push:ud,_abort:function(){e.abort()},___setErrNo:vb,___gxx_personality_v0:function(){},___assert_fail:function(a,b,c,d){y=!0;throw"Assertion failed: "+B(a)+", at: "+[b?B(b):"unknown filename",c,d?B(d):"unknown function"]+" at "+Ja();},___cxa_allocate_exception:function(a){return H(a)},___cxa_find_matching_catch:qb,_emscripten_set_main_loop_timing:Bc,_sbrk:Ba,
___cxa_begin_catch:function(a){lb.q--;nb.push(a);var b=pb(a);b&&ob[b].Ua++;return a},_emscripten_memcpy_big:function(a,b,c){I.set(I.subarray(b,b+c),a);return a},___resumeException:function(a){mb||(mb=a);var b=pb(a);b&&(ob[b].Ua=0);throw a+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch.";},__ZSt18uncaught_exceptionv:lb,_sysconf:function(a){switch(a){case 30:return 4096;case 85:return K/4096;
case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 79:return 0;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;
case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1E3;case 89:return 700;case 71:return 256;
case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return"object"===typeof navigator?navigator.hardwareConcurrency||1:1}vb(N.p);return-1},_pthread_getspecific:function(a){return ub[a]||0},_pthread_self:function(){return 0},_pthread_once:tb,_pthread_key_create:function(a){if(0==a)return N.p;F[a>>2]=wb;ub[wb]=0;wb++;return 0},___unlock:function(){},_emscripten_set_main_loop:Jc,_pthread_setspecific:function(a,b){if(!(a in
ub))return N.p;ub[a]=b;return 0},___cxa_atexit:function(){return jb.apply(null,arguments)},___cxa_throw:function(a,b,c){ob[a]={ob:a,Ca:a,type:b,Ed:c,Ua:0};mb=a;"uncaught_exception"in lb?lb.q++:lb.q=1;throw a+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch.";},__exit:qd,___lock:function(){},___syscall6:function(a,b){X=b;try{var c=rd();rc(c);return 0}catch(d){return"undefined"!==typeof Ac&&d instanceof
O||x(d),-d.G}},___syscall5:function(a,b){X=b;try{var c=B(Y()),d=Y(),f=Y();return oc(c,d,f).fd}catch(g){return"undefined"!==typeof Ac&&g instanceof O||x(g),-g.G}},_time:function(a){var b=Date.now()/1E3|0;a&&(F[a>>2]=b);return b},_atexit:jb,___syscall140:function(a,b){X=b;try{var c=rd(),d=Y(),f=Y(),g=Y(),h=Y();assert(0===d);sc(c,f,h);F[g>>2]=c.position;c.oa&&0===f&&0===h&&(c.oa=null);return 0}catch(k){return"undefined"!==typeof Ac&&k instanceof O||x(k),-k.G}},_exit:function(a){qd(a)},___syscall145:function(a,
b){X=b;try{var c=rd(),d=Y(),f;a:{for(var g=Y(),h=0,k=0;k<g;k++){var u=F[d+(8*k+4)>>2],r,p=c,w=F[d+8*k>>2],z=u,C=void 0,J=D;if(0>z||0>C)throw new O(N.p);if(1===(p.flags&2097155))throw new O(N.Y);if(Q(p.g.mode))throw new O(N.N);if(!p.n.read)throw new O(N.p);var R=!0;if("undefined"===typeof C)C=p.position,R=!1;else if(!p.seekable)throw new O(N.Z);var Qb=p.n.read(p,J,w,z,C);R||(p.position+=Qb);r=Qb;if(0>r){f=-1;break a}h+=r;if(r<u)break}f=h}return f}catch(hb){return"undefined"!==typeof Ac&&hb instanceof
O||x(hb),-hb.G}},___syscall146:function(a,b){X=b;try{var c=rd(),d=Y(),f;a:{for(var g=Y(),h=0,k=0;k<g;k++){var u=tc(c,D,F[d+8*k>>2],F[d+(8*k+4)>>2],void 0);if(0>u){f=-1;break a}h+=u}f=h}return f}catch(r){return"undefined"!==typeof Ac&&r instanceof O||x(r),-r.G}},STACKTOP:m,STACK_MAX:Qa,tempDoublePtr:ib,ABORT:y,cttz_i8:xd,___dso_handle:wd};// EMSCRIPTEN_START_ASM
var M=(function(global,env,buffer) {
"use asm";var a=new global.Int8Array(buffer);var b=new global.Int16Array(buffer);var c=new global.Int32Array(buffer);var d=new global.Uint8Array(buffer);var e=new global.Uint16Array(buffer);var f=new global.Uint32Array(buffer);var g=new global.Float32Array(buffer);var h=new global.Float64Array(buffer);var i=env.STACKTOP|0;var j=env.STACK_MAX|0;var k=env.tempDoublePtr|0;var l=env.ABORT|0;var m=env.cttz_i8|0;var n=env.___dso_handle|0;var o=0;var p=0;var q=0;var r=0;var s=global.NaN,t=global.Infinity;var u=0,v=0,w=0,x=0,y=0.0,z=0,A=0,B=0,C=0.0;var D=0;var E=0;var F=0;var G=0;var H=0;var I=0;var J=0;var K=0;var L=0;var M=0;var N=global.Math.floor;var O=global.Math.abs;var P=global.Math.sqrt;var Q=global.Math.pow;var R=global.Math.cos;var S=global.Math.sin;var T=global.Math.tan;var U=global.Math.acos;var V=global.Math.asin;var W=global.Math.atan;var X=global.Math.atan2;var Y=global.Math.exp;var Z=global.Math.log;var _=global.Math.ceil;var $=global.Math.imul;var aa=global.Math.min;var ba=global.Math.clz32;var ca=env.abort;var da=env.assert;var ea=env.invoke_iiii;var fa=env.invoke_viiiii;var ga=env.invoke_vi;var ha=env.invoke_vii;var ia=env.invoke_ii;var ja=env.invoke_v;var ka=env.invoke_iiiii;var la=env.invoke_viiiiii;var ma=env.invoke_viiii;var na=env._pthread_cleanup_pop;var oa=env.___syscall221;var pa=env._exp;var qa=env.___syscall54;var ra=env._pthread_cleanup_push;var sa=env._abort;var ta=env.___setErrNo;var ua=env.___gxx_personality_v0;var va=env.___assert_fail;var wa=env.___cxa_allocate_exception;var xa=env.___cxa_find_matching_catch;var ya=env._emscripten_set_main_loop_timing;var za=env._sbrk;var Aa=env.___cxa_begin_catch;var Ba=env._emscripten_memcpy_big;var Ca=env.___resumeException;var Da=env.__ZSt18uncaught_exceptionv;var Ea=env._sysconf;var Fa=env._pthread_getspecific;var Ga=env._pthread_self;var Ha=env._pthread_once;var Ia=env._pthread_key_create;var Ja=env.___unlock;var Ka=env._emscripten_set_main_loop;var La=env._pthread_setspecific;var Ma=env.___cxa_atexit;var Na=env.___cxa_throw;var Oa=env.__exit;var Pa=env.___lock;var Qa=env.___syscall6;var Ra=env.___syscall5;var Sa=env._time;var Ta=env._atexit;var Ua=env.___syscall140;var Va=env._exit;var Wa=env.___syscall145;var Xa=env.___syscall146;var Ya=0.0;
// EMSCRIPTEN_START_FUNCS
function gb(a){a=a|0;var b=0;b=i;i=i+a|0;i=i+15&-16;return b|0}function hb(){return i|0}function ib(a){a=a|0;i=a}function jb(a,b){a=a|0;b=b|0;i=a;j=b}function kb(a,b){a=a|0;b=b|0;if(!o){o=a;p=b}}function lb(b){b=b|0;a[k>>0]=a[b>>0];a[k+1>>0]=a[b+1>>0];a[k+2>>0]=a[b+2>>0];a[k+3>>0]=a[b+3>>0]}function mb(b){b=b|0;a[k>>0]=a[b>>0];a[k+1>>0]=a[b+1>>0];a[k+2>>0]=a[b+2>>0];a[k+3>>0]=a[b+3>>0];a[k+4>>0]=a[b+4>>0];a[k+5>>0]=a[b+5>>0];a[k+6>>0]=a[b+6>>0];a[k+7>>0]=a[b+7>>0]}function nb(a){a=a|0;D=a}function ob(){return D|0}function pb(d,f,g,h){d=d|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0;i=d+472+(g<<2)|0;j=(c[h+(c[i>>2]<<2)>>2]|0)==1;k=h+((g<<1)+401<<2)+2|0;l=k;b[l>>1]=0;b[l+2>>1]=0>>>16;l=k+4|0;b[l>>1]=0;b[l+2>>1]=0>>>16;l=g>>2;k=h+1648324|0;m=h+3324+(g<<4)|0;_b(k,l,d,m,h+4124+(g<<4)|0,h+4944|0)|0;ic(k,l,0,h+3100|0);n=d+(g+9<<3)|0;o=n;b[o>>1]=0;b[o+2>>1]=0>>>16;o=n+4|0;b[o>>1]=0;b[o+2>>1]=0>>>16;o=d+72+(g<<3)|0;n=fc(k,l,0,o)|0;if(!n){p=j^1;return p|0}q=g+-1|0;r=d+472+(q<<2)|0;s=d+672+(g<<4)|0;t=d+72+(q<<3)|0;u=d+72+(q<<3)+2|0;v=d+72+(g<<3)+2|0;w=d+72+(q<<3)+4|0;x=d+72+(g<<3)+4|0;y=d+72+(q<<3)+6|0;z=d+72+(g<<3)+6|0;A=n;while(1){n=c[i>>2]|0;B=c[A>>2]|0;C=A+4|0;D=c[C>>2]|0;c[r>>2]=n;c[s>>2]=c[A>>2];c[s+4>>2]=c[A+4>>2];c[s+8>>2]=c[A+8>>2];c[s+12>>2]=c[A+12>>2];E=1224+(D<<1)|0;D=d+(n<<3)+(B<<1)|0;b[D>>1]=(e[D>>1]|0)&((e[E>>1]|0)^65535);D=d+32+(B<<1)|0;b[D>>1]=b[D>>1]^b[E>>1];E=d+56+(n<<2)|0;c[E>>2]=(c[E>>2]|0)-(c[424+(B<<2)>>2]|0);E=d+40+(n<<2)+B|0;a[E>>0]=(a[E>>0]|0)+-1<<24>>24;E=qb(d,f,q,h)|0;B=c[i>>2]|0;n=c[A>>2]|0;D=1224+(c[C>>2]<<1)|0;C=d+(B<<3)+(n<<1)|0;b[C>>1]=b[C>>1]|b[D>>1];C=d+32+(n<<1)|0;b[C>>1]=b[C>>1]|b[D>>1];D=d+56+(B<<2)|0;c[D>>2]=(c[D>>2]|0)+(c[424+(n<<2)>>2]|0);D=d+40+(B<<2)+n|0;a[D>>0]=(a[D>>0]|0)+1<<24>>24;if(!(j^E)){F=A;G=E;break}b[o>>1]=b[o>>1]|b[t>>1];b[v>>1]=b[v>>1]|b[u>>1];b[x>>1]=b[x>>1]|b[w>>1];b[z>>1]=b[z>>1]|b[y>>1];A=fc(k,l,0,o)|0;if(!A){p=E;H=6;break}}if((H|0)==6)return p|0;b[o>>1]=b[d+72+(q<<3)>>1]|0;b[d+72+(g<<3)+2>>1]=b[d+72+(q<<3)+2>>1]|0;b[d+72+(g<<3)+4>>1]=b[d+72+(q<<3)+4>>1]|0;b[d+72+(g<<3)+6>>1]=b[d+72+(q<<3)+6>>1]|0;c[m>>2]=c[F>>2];c[m+4>>2]=c[F+4>>2];c[m+8>>2]=c[F+8>>2];c[m+12>>2]=c[F+12>>2];p=G;return p|0}function qb(d,f,g,h){d=d|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0;i=d+472+(g<<2)|0;j=(c[i>>2]|0)+1&3;k=(c[h+(j<<2)>>2]|0)==1;l=g+3>>2;if(qc(d,j,g,f,c[h+56>>2]|0,h)|0){m=k;return m|0}j=h+((g<<1)+401<<2)+2|0;n=j;b[n>>1]=0;b[n+2>>1]=0>>>16;n=j+4|0;b[n>>1]=0;b[n+2>>1]=0>>>16;n=h+1648324|0;cc(n,l,1,d)|0;if((c[h+16>>2]|0)==(g|0))ic(n,l,1,h+3100|0);j=d+(g+9<<3)|0;o=j;b[o>>1]=0;b[o+2>>1]=0>>>16;o=j+4|0;b[o>>1]=0;b[o+2>>1]=0>>>16;o=d+72+(g<<3)|0;j=fc(n,l,1,o)|0;if(!j){m=k^1;return m|0}p=g+-1|0;q=d+472+(p<<2)|0;r=d+72+(p<<3)|0;s=d+72+(p<<3)+2|0;t=d+72+(g<<3)+2|0;u=d+72+(p<<3)+4|0;v=d+72+(g<<3)+4|0;w=d+72+(p<<3)+6|0;x=d+72+(g<<3)+6|0;y=j;while(1){j=c[i>>2]|0;c[q>>2]=j;z=j+1&3;j=c[y>>2]|0;A=y+4|0;B=1224+(c[A>>2]<<1)|0;C=d+(z<<3)+(j<<1)|0;b[C>>1]=(e[C>>1]|0)&((e[B>>1]|0)^65535);C=d+32+(j<<1)|0;b[C>>1]=b[C>>1]^b[B>>1];B=d+56+(z<<2)|0;c[B>>2]=(c[B>>2]|0)-(c[424+(j<<2)>>2]|0);B=d+40+(z<<2)+j|0;a[B>>0]=(a[B>>0]|0)+-1<<24>>24;B=tb(d,f,p,h)|0;j=(c[i>>2]|0)+1&3;z=c[y>>2]|0;C=1224+(c[A>>2]<<1)|0;A=d+(j<<3)+(z<<1)|0;b[A>>1]=b[A>>1]|b[C>>1];A=d+32+(z<<1)|0;b[A>>1]=b[A>>1]|b[C>>1];C=d+56+(j<<2)|0;c[C>>2]=(c[C>>2]|0)+(c[424+(z<<2)>>2]|0);C=d+40+(j<<2)+z|0;a[C>>0]=(a[C>>0]|0)+1<<24>>24;if(!(k^B)){D=y;E=B;break}b[o>>1]=b[o>>1]|b[r>>1];b[t>>1]=b[t>>1]|b[s>>1];b[v>>1]=b[v>>1]|b[u>>1];b[x>>1]=b[x>>1]|b[w>>1];y=fc(n,l,1,o)|0;if(!y){m=B;F=9;break}}if((F|0)==9)return m|0;b[o>>1]=b[d+72+(p<<3)>>1]|0;b[d+72+(g<<3)+2>>1]=b[d+72+(p<<3)+2>>1]|0;b[d+72+(g<<3)+4>>1]=b[d+72+(p<<3)+4>>1]|0;b[d+72+(g<<3)+6>>1]=b[d+72+(p<<3)+6>>1]|0;p=h+3324+(g<<4)|0;c[p>>2]=c[D>>2];c[p+4>>2]=c[D+4>>2];c[p+8>>2]=c[D+8>>2];c[p+12>>2]=c[D+12>>2];m=E;return m|0}function rb(d,f,g,h){d=d|0;f=f|0;g=g|0;h=h|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0;j=i;i=i+32|0;k=j;l=j+16|0;m=c[h+56>>2]|0;n=d+472+(g<<2)|0;o=c[n>>2]|0;p=d+(g+9<<3)|0;q=p;r=q;b[r>>1]=0;b[r+2>>1]=0>>>16;r=q+4|0;b[r>>1]=0;b[r+2>>1]=0>>>16;r=g>>2;do if((g|0)>19){q=d+1476|0;s=f-(c[q>>2]|0)+-1|0;t=d+32|0;u=Xc(h+987984|0,r,o,t,d+56|0,(c[h>>2]|0)==1?s:r-s|0,k)|0;if(!u){v=q;break}b[d+72+(g<<3)>>1]=b[3411644+((e[t>>1]|0)*28|0)+(a[u+4>>0]<<1)>>1]|0;b[d+72+(g<<3)+2>>1]=b[3411644+((e[d+34>>1]|0)*28|0)+(a[u+5>>0]<<1)>>1]|0;b[d+72+(g<<3)+4>>1]=b[3411644+((e[d+36>>1]|0)*28|0)+(a[u+6>>0]<<1)>>1]|0;b[d+72+(g<<3)+6>>1]=b[3411644+((e[d+38>>1]|0)*28|0)+(a[u+7>>0]<<1)>>1]|0;t=a[u+3>>0]|0;if(t<<24>>24){c[h+4124+(g<<4)>>2]=a[u+2>>0];c[h+4124+(g<<4)+4>>2]=t<<24>>24}w=(c[h>>2]|0)!=1^(a[k>>0]|0)!=0;i=j;return w|0}else v=d+1476|0;while(0);t=c[v>>2]|0;if((t|0)>=(f|0)){w=1;i=j;return w|0}u=r+1|0;if((u+t|0)<(f|0)){w=0;i=j;return w|0}if(!g){sb(k,d,m,h);t=c[k>>2]|0;q=k+4|0;s=c[q>>2]|0;x=c[q+4>>2]|0;q=d+72|0;y=q;b[y>>1]=s;b[y+2>>1]=s>>>16;s=q+4|0;b[s>>1]=x;b[s+2>>1]=x>>>16;w=(t|0)>=(f|0);i=j;return w|0}t=lc(d,o,g,f,m,k,h)|0;x=h+(o<<2)|0;s=(a[k>>0]|0)!=0;do if((c[x>>2]|0)==1)if(!s){q=Jb(d,o,g,f,m,h)|0;a[k>>0]=q&1;if(q){z=19;break}else{A=0;break}}else{A=(t|0)!=0;break}else if(!s){q=Kb(d,o,g,f,m,h)|0;a[k>>0]=q&1;if(q){A=1;break}else{z=19;break}}else{A=(t|0)==0;break}while(0);a:do if((z|0)==19){do if((g|0)<20){t=f-(c[v>>2]|0)+-1|0;k=d+32|0;m=Xc(h+987984|0,r,o,k,d+56|0,(c[h>>2]|0)==1?t:r-t|0,l)|0;if(!m)break;b[d+72+(g<<3)>>1]=b[3411644+((e[k>>1]|0)*28|0)+(a[m+4>>0]<<1)>>1]|0;b[d+72+(g<<3)+2>>1]=b[3411644+((e[d+34>>1]|0)*28|0)+(a[m+5>>0]<<1)>>1]|0;b[d+72+(g<<3)+4>>1]=b[3411644+((e[d+36>>1]|0)*28|0)+(a[m+6>>0]<<1)>>1]|0;b[d+72+(g<<3)+6>>1]=b[3411644+((e[d+38>>1]|0)*28|0)+(a[m+7>>0]<<1)>>1]|0;k=a[m+3>>0]|0;if(k<<24>>24){c[h+4124+(g<<4)>>2]=a[m+2>>0];c[h+4124+(g<<4)+4>>2]=k<<24>>24}A=(c[h>>2]|0)!=1^(a[l>>0]|0)!=0;break a}while(0);k=(c[x>>2]|0)==1;m=h+((g<<1)+401<<2)+2|0;t=m;b[t>>1]=0;b[t+2>>1]=0>>>16;t=m+4|0;b[t>>1]=0;b[t+2>>1]=0>>>16;t=h+1648324|0;m=h+3324+(g<<4)|0;_b(t,r,d,m,h+4124+(g<<4)|0,h+4944|0)|0;s=p;q=s;b[q>>1]=0;b[q+2>>1]=0>>>16;q=s+4|0;b[q>>1]=0;b[q+2>>1]=0>>>16;q=d+72+(g<<3)|0;s=fc(t,r,0,q)|0;b:do if(s){y=g+-1|0;B=d+472+(y<<2)|0;C=d+672+(g<<4)|0;D=d+72+(y<<3)|0;E=d+72+(y<<3)+2|0;F=d+72+(g<<3)+2|0;G=d+72+(y<<3)+4|0;H=d+72+(g<<3)+4|0;I=d+72+(y<<3)+6|0;J=d+72+(g<<3)+6|0;K=s;while(1){L=c[n>>2]|0;M=c[K>>2]|0;N=K+4|0;O=c[N>>2]|0;c[B>>2]=L;c[C>>2]=c[K>>2];c[C+4>>2]=c[K+4>>2];c[C+8>>2]=c[K+8>>2];c[C+12>>2]=c[K+12>>2];P=1224+(O<<1)|0;O=d+(L<<3)+(M<<1)|0;b[O>>1]=e[O>>1]&(e[P>>1]^65535);O=d+32+(M<<1)|0;b[O>>1]=b[O>>1]^b[P>>1];P=d+56+(L<<2)|0;c[P>>2]=(c[P>>2]|0)-(c[424+(M<<2)>>2]|0);P=d+40+(L<<2)+M|0;a[P>>0]=(a[P>>0]|0)+-1<<24>>24;Q=qb(d,f,y,h)|0;P=c[n>>2]|0;M=c[K>>2]|0;L=1224+(c[N>>2]<<1)|0;N=d+(P<<3)+(M<<1)|0;b[N>>1]=b[N>>1]|b[L>>1];N=d+32+(M<<1)|0;b[N>>1]=b[N>>1]|b[L>>1];L=d+56+(P<<2)|0;c[L>>2]=(c[L>>2]|0)+(c[424+(M<<2)>>2]|0);L=d+40+(P<<2)+M|0;a[L>>0]=(a[L>>0]|0)+1<<24>>24;if(!(k^Q))break;b[q>>1]=b[q>>1]|b[D>>1];b[F>>1]=b[F>>1]|b[E>>1];b[H>>1]=b[H>>1]|b[G>>1];b[J>>1]=b[J>>1]|b[I>>1];L=fc(t,r,0,q)|0;if(!L){R=Q;z=30;break b}else K=L}b[q>>1]=b[d+72+(y<<3)>>1]|0;b[d+72+(g<<3)+2>>1]=b[d+72+(y<<3)+2>>1]|0;b[d+72+(g<<3)+4>>1]=b[d+72+(y<<3)+4>>1]|0;b[d+72+(g<<3)+6>>1]=b[d+72+(y<<3)+6>>1]|0;c[m>>2]=c[K>>2];c[m+4>>2]=c[K+4>>2];c[m+8>>2]=c[K+8>>2];c[m+12>>2]=c[K+12>>2];I=(c[h>>2]|0)==1;if(Q){S=l;T=I;z=31}else{U=l;V=I;z=34}}else{R=k^1;z=30}while(0);if((z|0)==30){k=(c[h>>2]|0)==1;if(R){S=l;T=k;z=31}else{U=l;V=k;z=34}}do if((z|0)==31)if(T){a[S>>0]=u;a[l+1>>0]=f-(c[v>>2]|0);W=S;X=1;break}else{a[S>>0]=u-f+(c[v>>2]|0);a[l+1>>0]=0;W=S;X=1;break}else if((z|0)==34)if(V){a[U>>0]=f+255-(c[v>>2]|0);a[l+1>>0]=0;W=U;X=0;break}else{a[U>>0]=u;a[l+1>>0]=1-f+u+(c[v>>2]|0);W=U;X=0;break}while(0);a[l+2>>0]=c[m>>2];a[l+3>>0]=c[h+3324+(g<<4)+4>>2];k=c[x>>2]|0;t=X^1;if((k|0)!=1|t)Y=(k|0)==0&t;else Y=1;Zc(h+987984|0,r,o,d+32|0,q,l,Y);A=X}while(0);w=A;i=j;return w|0}function sb(a,d,e,f){a=a|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;g=c[d+472>>2]|0;if(g>>>0>=4)va(1288,1325,1203,1338);h=a+4|0;c[h>>2]=0;c[h+4>>2]=0;if((e|0)!=4){h=b[d+(e<<1)>>1]|0;i=b[d+8+(e<<1)>>1]|0;j=(i&65535)>(h&65535);k=j?i:h;l=b[d+16+(e<<1)>>1]|0;m=(l&65535)>(k&65535);n=m?l:k;k=b[d+24+(e<<1)>>1]|0;o=(k<<16>>16!=0&1)+((l<<16>>16!=0&1)+((i<<16>>16!=0&1)+(h<<16>>16!=0&1)))|0;h=(k&65535)>(n&65535);i=h?3:m?2:j&1;j=h?k:n;if(j<<16>>16){if(o>>>0>1)b[a+4+(e<<1)>>1]=j;if((c[f+(i<<2)>>2]|0)==1)p=13;else p=14}else{q=o;r=i;p=8}}else{q=0;r=0;p=8}if((p|0)==8){if(!(b[d+(g<<3)>>1]|0))if(!(b[d+(g<<3)+2>>1]|0))if(!(b[d+(g<<3)+4>>1]|0))if(!(b[d+(g<<3)+6>>1]|0))va(1347,1325,1244,1338);else s=3;else s=2;else s=1;else s=0;g=b[d+(s<<1)>>1]|0;i=b[d+8+(s<<1)>>1]|0;o=(i&65535)>(g&65535);j=o?i:g;e=b[d+16+(s<<1)>>1]|0;n=(e&65535)>(j&65535);k=n?e:j;j=b[d+24+(s<<1)>>1]|0;h=(j&65535)>(k&65535);if(((j<<16>>16!=0&1)+((e<<16>>16!=0&1)+((i<<16>>16!=0&1)+((g<<16>>16!=0&1)+q)))|0)>1)b[a+4+(s<<1)>>1]=h?j:k;if((c[f+((h?3:n?2:o?1:g<<16>>16!=0?0:r)<<2)>>2]|0)==1)p=13;else p=14}if((p|0)==13){c[a>>2]=(c[d+1476>>2]|0)+1;return}else if((p|0)==14){c[a>>2]=c[d+1476>>2];return}}function tb(d,f,g,h){d=d|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0;i=d+472+(g<<2)|0;j=(c[h+(((c[i>>2]|0)+2&3)<<2)>>2]|0)==1;k=h+((g<<1)+401<<2)+2|0;l=k;b[l>>1]=0;b[l+2>>1]=0>>>16;l=k+4|0;b[l>>1]=0;b[l+2>>1]=0>>>16;l=g+3>>2;k=h+1648324|0;cc(k,l,2,d)|0;if((c[h+16>>2]|0)==(g|0))ic(k,l,2,h+3100|0);m=d+(g+9<<3)|0;n=m;b[n>>1]=0;b[n+2>>1]=0>>>16;n=m+4|0;b[n>>1]=0;b[n+2>>1]=0>>>16;n=d+72+(g<<3)|0;m=fc(k,l,2,n)|0;if(!m){o=j^1;return o|0}p=g+-1|0;q=d+472+(p<<2)|0;r=d+72+(p<<3)|0;s=d+72+(p<<3)+2|0;t=d+72+(g<<3)+2|0;u=d+72+(p<<3)+4|0;v=d+72+(g<<3)+4|0;w=d+72+(p<<3)+6|0;x=d+72+(g<<3)+6|0;y=m;while(1){m=c[i>>2]|0;c[q>>2]=m;z=m+2&3;m=c[y>>2]|0;A=y+4|0;B=1224+(c[A>>2]<<1)|0;C=d+(z<<3)+(m<<1)|0;b[C>>1]=(e[C>>1]|0)&((e[B>>1]|0)^65535);C=d+32+(m<<1)|0;b[C>>1]=b[C>>1]^b[B>>1];B=d+56+(z<<2)|0;c[B>>2]=(c[B>>2]|0)-(c[424+(m<<2)>>2]|0);B=d+40+(z<<2)+m|0;a[B>>0]=(a[B>>0]|0)+-1<<24>>24;B=ub(d,f,p,h)|0;m=(c[i>>2]|0)+2&3;z=c[y>>2]|0;C=1224+(c[A>>2]<<1)|0;A=d+(m<<3)+(z<<1)|0;b[A>>1]=b[A>>1]|b[C>>1];A=d+32+(z<<1)|0;b[A>>1]=b[A>>1]|b[C>>1];C=d+56+(m<<2)|0;c[C>>2]=(c[C>>2]|0)+(c[424+(z<<2)>>2]|0);C=d+40+(m<<2)+z|0;a[C>>0]=(a[C>>0]|0)+1<<24>>24;if(!(j^B)){D=y;E=B;break}b[n>>1]=b[n>>1]|b[r>>1];b[t>>1]=b[t>>1]|b[s>>1];b[v>>1]=b[v>>1]|b[u>>1];b[x>>1]=b[x>>1]|b[w>>1];y=fc(k,l,2,n)|0;if(!y){o=B;F=8;break}}if((F|0)==8)return o|0;b[n>>1]=b[d+72+(p<<3)>>1]|0;b[d+72+(g<<3)+2>>1]=b[d+72+(p<<3)+2>>1]|0;b[d+72+(g<<3)+4>>1]=b[d+72+(p<<3)+4>>1]|0;b[d+72+(g<<3)+6>>1]=b[d+72+(p<<3)+6>>1]|0;p=h+3324+(g<<4)|0;c[p>>2]=c[D>>2];c[p+4>>2]=c[D+4>>2];c[p+8>>2]=c[D+8>>2];c[p+12>>2]=c[D+12>>2];o=E;return o|0}function ub(d,e,f,g){d=d|0;e=e|0;f=f|0;g=g|0;var h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0;h=i;i=i+16|0;j=h;k=d+472+(f<<2)|0;l=(c[g+(((c[k>>2]|0)+3&3)<<2)>>2]|0)==1;m=g+((f<<1)+401<<2)+2|0;n=m;b[n>>1]=0;b[n+2>>1]=0>>>16;n=m+4|0;b[n>>1]=0;b[n+2>>1]=0>>>16;n=f+3>>2;m=g+1648324|0;cc(m,n,3,d)|0;if((c[g+16>>2]|0)==(f|0))ic(m,n,3,g+3100|0);o=d+(f+9<<3)|0;p=o;b[p>>1]=0;b[p+2>>1]=0>>>16;p=o+4|0;b[p>>1]=0;b[p+2>>1]=0>>>16;p=d+72+(f<<3)|0;o=fc(m,n,3,p)|0;if(!o){q=l^1;i=h;return q|0}r=g+4940|0;s=f+-1|0;t=d+472+(s<<2)|0;u=d+1476|0;v=g+2008+(n*84|0)|0;w=d+72+(s<<3)|0;x=d+72+(s<<3)+2|0;y=j+2|0;z=d+72+(f<<3)+2|0;A=d+72+(s<<3)+4|0;B=j+4|0;C=d+72+(f<<3)+4|0;D=d+72+(s<<3)+6|0;E=j+6|0;F=d+72+(f<<3)+6|0;G=o;while(1){vb(d,j,f,G,g);c[r>>2]=(c[r>>2]|0)+1;if((c[g+(c[t>>2]<<2)>>2]|0)==1)c[u>>2]=(c[u>>2]|0)+1;o=rb(d,e,s,g)|0;H=(c[k>>2]|0)+3&3;I=c[G>>2]|0;J=1224+(c[G+4>>2]<<1)|0;K=d+(H<<3)+(I<<1)|0;b[K>>1]=b[K>>1]|b[J>>1];K=d+32+(I<<1)|0;b[K>>1]=b[K>>1]|b[J>>1];J=d+56+(H<<2)|0;c[J>>2]=(c[J>>2]|0)+(c[424+(I<<2)>>2]|0);J=d+40+(H<<2)+I|0;a[J>>0]=(a[J>>0]|0)+1<<24>>24;J=c[v>>2]|0;if((J|0)>0){I=0;do{H=c[g+2008+(n*84|0)+4+(I*20|0)>>2]|0;c[d+1480+(H<<3)>>2]=c[g+2008+(n*84|0)+4+(I*20|0)+4>>2];c[d+1480+(H<<3)+4>>2]=c[g+2008+(n*84|0)+4+(I*20|0)+8>>2];c[d+1512+(H<<3)>>2]=c[g+2008+(n*84|0)+4+(I*20|0)+12>>2];c[d+1512+(H<<3)+4>>2]=c[g+2008+(n*84|0)+4+(I*20|0)+16>>2];I=I+1|0}while((I|0)!=(J|0))}if((c[g+(c[t>>2]<<2)>>2]|0)==1)c[u>>2]=(c[u>>2]|0)+-1;if(!(l^o)){L=G;M=o;break}b[p>>1]=b[j>>1]|b[w>>1]|b[p>>1];b[z>>1]=b[y>>1]|b[x>>1]|b[z>>1];b[C>>1]=b[B>>1]|b[A>>1]|b[C>>1];b[F>>1]=b[E>>1]|b[D>>1]|b[F>>1];G=fc(m,n,3,p)|0;if(!G){q=o;N=14;break}}if((N|0)==14){i=h;return q|0}b[p>>1]=b[j>>1]|b[d+72+(s<<3)>>1];b[d+72+(f<<3)+2>>1]=b[j+2>>1]|b[d+72+(s<<3)+2>>1];b[d+72+(f<<3)+4>>1]=b[j+4>>1]|b[d+72+(s<<3)+4>>1];b[d+72+(f<<3)+6>>1]=b[j+6>>1]|b[d+72+(s<<3)+6>>1];s=g+3324+(f<<4)|0;c[s>>2]=c[L>>2];c[s+4>>2]=c[L+4>>2];c[s+8>>2]=c[L+8>>2];c[s+12>>2]=c[L+12>>2];q=M;i=h;return q|0}function vb(d,f,g,h,i){d=d|0;f=f|0;g=g|0;h=h|0;i=i|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;j=c[d+472+(g<<2)>>2]|0;k=g+3>>2;l=jc(i+1648324|0,k)|0;c[d+472+(g+-1<<2)>>2]=(c[l+28>>2]|0)+j&3;g=f;m=g;b[m>>1]=0;b[m+2>>1]=0>>>16;m=g+4|0;b[m>>1]=0;b[m+2>>1]=0>>>16;m=j+3&3;j=c[l+20>>2]|0;if((c[l+(j<<2)>>2]|0)>1)b[f+(j<<1)>>1]=e[1224+(c[l+16>>2]<<1)>>1]|c[l+24>>2];j=c[h>>2]|0;f=1224+(c[h+4>>2]<<1)|0;h=d+(m<<3)+(j<<1)|0;b[h>>1]=e[h>>1]&(e[f>>1]^65535);h=d+32+(j<<1)|0;b[h>>1]=b[h>>1]^b[f>>1];f=d+56+(m<<2)|0;c[f>>2]=(c[f>>2]|0)-(c[424+(j<<2)>>2]|0);f=d+40+(m<<2)+j|0;a[f>>0]=(a[f>>0]|0)+-1<<24>>24;f=i+2008+(k*84|0)|0;c[f>>2]=0;j=0;m=0;while(1){if(!(c[l+(m<<2)>>2]|0))n=j;else{c[i+2008+(k*84|0)+4+(j*20|0)>>2]=m;h=d+1480+(m<<3)|0;c[i+2008+(k*84|0)+4+(j*20|0)+4>>2]=c[h>>2];g=d+1480+(m<<3)+4|0;c[i+2008+(k*84|0)+4+(j*20|0)+8>>2]=c[g>>2];o=d+1512+(m<<3)|0;c[i+2008+(k*84|0)+4+(j*20|0)+12>>2]=c[o>>2];p=d+1512+(m<<3)+4|0;c[i+2008+(k*84|0)+4+(j*20|0)+16>>2]=c[p>>2];q=(c[f>>2]|0)+1|0;c[f>>2]=q;r=e[d+32+(m<<1)>>1]|0;c[h>>2]=a[i+4944+(r*120|0)+8+(m<<1)>>0];c[g>>2]=a[i+4944+(r*120|0)+8+(m<<1)+1>>0];c[o>>2]=a[i+4944+(r*120|0)+16+(m<<1)>>0];c[p>>2]=a[i+4944+(r*120|0)+16+(m<<1)+1>>0];n=q}m=m+1|0;if((m|0)==4)break;else j=n}return}function wb(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,j=0,k=0;d=i;i=i+64912|0;e=d+64808|0;f=d+43204|0;g=d;h=e+32|0;c[h>>2]=c[a>>2];c[h+4>>2]=c[a+4>>2];c[h+8>>2]=c[a+8>>2];c[h+12>>2]=c[a+12>>2];h=e+48|0;j=a+16|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=e+64|0;h=a+32|0;c[j>>2]=c[h>>2];c[j+4>>2]=c[h+4>>2];c[j+8>>2]=c[h+8>>2];c[j+12>>2]=c[h+12>>2];h=e+80|0;j=a+48|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=e+8|0;c[j>>2]=0;c[j+4>>2]=0;c[j+8>>2]=0;c[j+12>>2]=0;c[j+16>>2]=0;c[j+20>>2]=0;c[f>>2]=5;c[e>>2]=4;j=f+4|0;h=e;a=j+96|0;do{c[j>>2]=c[h>>2];j=j+4|0;h=h+4|0}while((j|0)<(a|0));c[f+19204>>2]=-1;c[f+20004>>2]=1;c[f+20804>>2]=1;c[e>>2]=3;j=f+100|0;h=e;a=j+96|0;do{c[j>>2]=c[h>>2];j=j+4|0;h=h+4|0}while((j|0)<(a|0));c[f+19208>>2]=-1;c[f+20008>>2]=1;c[f+20808>>2]=1;c[e>>2]=2;j=f+196|0;h=e;a=j+96|0;do{c[j>>2]=c[h>>2];j=j+4|0;h=h+4|0}while((j|0)<(a|0));c[f+19212>>2]=-1;c[f+20012>>2]=1;c[f+20812>>2]=1;c[e>>2]=1;j=f+292|0;h=e;a=j+96|0;do{c[j>>2]=c[h>>2];j=j+4|0;h=h+4|0}while((j|0)<(a|0));c[f+19216>>2]=-1;c[f+20016>>2]=1;c[f+20816>>2]=1;c[e>>2]=0;j=f+388|0;h=e;a=j+96|0;do{c[j>>2]=c[h>>2];j=j+4|0;h=h+4|0}while((j|0)<(a|0));c[f+19220>>2]=-1;c[f+20020>>2]=1;c[f+20820>>2]=1;h=Bc(f,g,4,1)|0;if((h|0)!=1){k=h;i=d;return k|0}h=c[f+4>>2]|0;c[b+(h<<4)+(c[114]<<2)>>2]=13-(c[g+168>>2]|0);c[b+(h<<4)+(c[115]<<2)>>2]=13-(c[g+172>>2]|0);c[b+(h<<4)+(c[116]<<2)>>2]=13-(c[g+176>>2]|0);c[b+(h<<4)+(c[117]<<2)>>2]=13-(c[g+180>>2]|0);h=c[f+100>>2]|0;c[b+(h<<4)+(c[114]<<2)>>2]=13-(c[g+384>>2]|0);c[b+(h<<4)+(c[115]<<2)>>2]=13-(c[g+388>>2]|0);c[b+(h<<4)+(c[116]<<2)>>2]=13-(c[g+392>>2]|0);c[b+(h<<4)+(c[117]<<2)>>2]=13-(c[g+396>>2]|0);h=c[f+196>>2]|0;c[b+(h<<4)+(c[114]<<2)>>2]=13-(c[g+600>>2]|0);c[b+(h<<4)+(c[115]<<2)>>2]=13-(c[g+604>>2]|0);c[b+(h<<4)+(c[116]<<2)>>2]=13-(c[g+608>>2]|0);c[b+(h<<4)+(c[117]<<2)>>2]=13-(c[g+612>>2]|0);h=c[f+292>>2]|0;c[b+(h<<4)+(c[114]<<2)>>2]=13-(c[g+816>>2]|0);c[b+(h<<4)+(c[115]<<2)>>2]=13-(c[g+820>>2]|0);c[b+(h<<4)+(c[116]<<2)>>2]=13-(c[g+824>>2]|0);c[b+(h<<4)+(c[117]<<2)>>2]=13-(c[g+828>>2]|0);h=c[f+388>>2]|0;c[b+(h<<4)+(c[114]<<2)>>2]=13-(c[g+1032>>2]|0);c[b+(h<<4)+(c[115]<<2)>>2]=13-(c[g+1036>>2]|0);c[b+(h<<4)+(c[116]<<2)>>2]=13-(c[g+1040>>2]|0);c[b+(h<<4)+(c[117]<<2)>>2]=13-(c[g+1044>>2]|0);k=1;i=d;return k|0}function xb(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0;d=i;i=i+128|0;e=d+64|0;f=d;if((kc(a,f)|0)!=1){g=-99;i=d;return g|0}a=e;h=f;f=a+64|0;do{c[a>>2]=c[h>>2];a=a+4|0;h=h+4|0}while((a|0)<(f|0));g=wb(e,b)|0;i=d;return g|0}function yb(a){a=a|0;var b=0,d=0,e=0,f=0;if(!(c[711505]|0))c[449360]=0;Jc();Kc();b=c[449360]|0;d=(a|0)==0?1:(a|0)<1?a:1;c[449360]=d;if((d|0)>0){a=0;do{e=6192+($(a,1688368)|0)+987984|0;Qc(e,95);Rc(e,160);a=a+1|0;e=c[449360]|0}while((a|0)<(e|0));f=e}else f=d;a:do if((f|0)!=(b|0)){if((f|0)>(b|0)){d=b;while(1){Sc(6192+($(d,1688368)|0)+987984|0);d=d+1|0;if((d|0)>=(c[449360]|0))break a}}if((f|0)<(b|0)){d=f;do{Oc(6192+($(d,1688368)|0)+987984|0);d=d+1|0}while((d|0)!=(b|0))}}while(0);if(c[711505]|0)return;c[711505]=1;zb();return}function zb(){var d=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0;c[449361]=0;c[457553]=0;d=e[626]|0;f=e[625]|0;g=e[624]|0;h=e[623]|0;i=e[622]|0;j=e[621]|0;k=e[620]|0;l=e[619]|0;m=e[618]|0;n=e[617]|0;o=e[616]|0;p=b[615]|0;q=p&65535;r=b[614]|0;s=r&65535;t=p&65535;p=e[616]|0;u=e[617]|0;v=e[618]|0;w=e[619]|0;x=e[620]|0;y=e[621]|0;z=e[622]|0;A=e[623]|0;B=e[624]|0;C=e[625]|0;D=b[614]|0;E=1;do{F=(d&E|0)==0;if(F)if(!(f&E))if(!(g&E))if(!(h&E))if(!(i&E))if(!(j&E))if(!(k&E))if(!(l&E))if(!(m&E))if(!(n&E))if(!(o&E))if(!(q&E))if(!(s&E))G=r;else{H=2;I=3}else{H=3;I=3}else{H=4;I=3}else{H=5;I=3}else{H=6;I=3}else{H=7;I=3}else{H=8;I=3}else{H=9;I=3}else{H=10;I=3}else{H=11;I=3}else{H=12;I=3}else{H=13;I=3}else{H=14;I=3}if((I|0)==3){I=0;c[1797444+(E<<2)>>2]=H;G=D}J=G&65535;if(!(J&E))if(!(t&E))if(!(p&E))if(!(u&E))if(!(v&E))if(!(w&E))if(!(x&E))if(!(y&E))if(!(z&E))if(!(A&E))if(!(B&E)){K=(C&E|0)!=0;if(K|F^1){L=K?13:14;I=6}}else{L=12;I=6}else{L=11;I=6}else{L=10;I=6}else{L=9;I=6}else{L=8;I=6}else{L=7;I=6}else{L=6;I=6}else{L=5;I=6}else{L=4;I=6}else{L=3;I=6}else{L=2;I=6}if((I|0)==6){I=0;c[1830212+(E<<2)>>2]=L}E=E+1|0}while((E|0)!=8192);M=J;E=0;do{L=E&1;I=(E&2|0)==0;C=L+1|0;B=I?L:C;A=(E&4|0)==0;z=B+1|0;y=A?B:z;B=(E&8|0)==0;x=y+1|0;w=B?y:x;y=(E&16|0)==0;v=w+1|0;u=y?w:v;w=(E&32|0)==0;p=u+1|0;t=w?u:p;u=(E&64|0)==0;G=t+1|0;D=u?t:G;t=(E&128|0)==0;H=D+1|0;r=t?D:H;D=(E&256|0)==0;s=r+1|0;q=D?r:s;r=(E&512|0)==0;o=q+1|0;n=r?q:o;q=(E&1024|0)==0;m=n+1|0;l=q?n:m;n=(E&2048|0)==0;k=l+1|0;c[1862980+(E<<2)>>2]=(E&4096|0)==0?(n?(q?(r?(D?(t?(u?(w?(y?(B?(A?(I?L:C):z):x):v):p):G):H):s):o):m):k):(n?l:k)+1|0;E=E+1|0}while((E|0)!=8192);E=3641020;k=E+15|0;do{a[E>>0]=0;E=E+1|0}while((E|0)<(k|0));l=e[625]|0;n=e[624]|0;m=e[623]|0;o=e[622]|0;s=e[621]|0;H=e[620]|0;G=e[619]|0;p=e[618]|0;v=e[617]|0;x=e[616]|0;z=e[615]|0;C=1;do{if(!(d&C))N=0;else{a[3641020+(C*15|0)+14>>0]=1;N=1}if(!(l&C))O=N;else{L=N+1<<24>>24;a[3641020+(C*15|0)+13>>0]=L;O=L}if(!(n&C))P=O;else{L=O+1<<24>>24;a[3641020+(C*15|0)+12>>0]=L;P=L}if(!(m&C))Q=P;else{L=P+1<<24>>24;a[3641020+(C*15|0)+11>>0]=L;Q=L}if(!(o&C))R=Q;else{L=Q+1<<24>>24;a[3641020+(C*15|0)+10>>0]=L;R=L}if(!(s&C))S=R;else{L=R+1<<24>>24;a[3641020+(C*15|0)+9>>0]=L;S=L}if(!(H&C))T=S;else{L=S+1<<24>>24;a[3641020+(C*15|0)+8>>0]=L;T=L}if(!(G&C))U=T;else{L=T+1<<24>>24;a[3641020+(C*15|0)+7>>0]=L;U=L}if(!(p&C))V=U;else{L=U+1<<24>>24;a[3641020+(C*15|0)+6>>0]=L;V=L}if(!(v&C))W=V;else{L=V+1<<24>>24;a[3641020+(C*15|0)+5>>0]=L;W=L}if(!(x&C))X=W;else{L=W+1<<24>>24;a[3641020+(C*15|0)+4>>0]=L;X=L}if(!(z&C))Y=X;else{L=X+1<<24>>24;a[3641020+(C*15|0)+3>>0]=L;Y=L}if(M&C)a[3641020+(C*15|0)+2>>0]=Y+1<<24>>24;C=C+1|0}while((C|0)!=8192);Z=0;do{b[3411644+(Z*28|0)>>1]=0;C=1;do{Y=1;M=14;X=0;while(1){z=e[1224+(M<<1)>>1]|0;if(!(z&Z)){_=Y;$=X}else{if((Y|0)>(C|0)){aa=X;break}_=Y+1|0;$=z|X}M=M+-1|0;if((M|0)<=1){aa=$;break}else{Y=_;X=$}}b[3411644+(Z*28|0)+(C<<1)>>1]=aa;C=C+1|0}while((C|0)!=14);Z=Z+1|0}while((Z|0)!=8192);c[473937]=-1;c[473966]=0;c[473967]=2;c[473974]=0;c[473981]=1;c[473988]=0;Z=0;aa=2;$=2;_=1;do{C=_<<1;X=(aa|0)<(C|0);Y=_;_=X?_:C;Z=X?Z:Y;$=(X&1^1)+$|0;X=1895748+(aa*116|0)|0;E=X;Y=1895748+((_^aa)*116|0)|0;k=E+116|0;do{c[E>>2]=c[Y>>2];E=E+4|0;Y=Y+4|0}while((E|0)<(k|0));Y=c[X>>2]|0;if(!(Z&aa)){C=Y+1|0;c[X>>2]=C;c[1895748+(aa*116|0)+4+(C<<2)>>2]=$;c[1895748+(aa*116|0)+32+(C<<2)>>2]=0;c[1895748+(aa*116|0)+60+(C<<2)>>2]=_;c[1895748+(aa*116|0)+88+(C<<2)>>2]=c[548+(c[1895748+(aa*116|0)+4+(Y<<2)>>2]<<2)>>2]&c[488+($<<2)>>2]}else{C=1895748+(aa*116|0)+4+(Y<<2)|0;c[C>>2]=(c[C>>2]|0)+1;C=1895748+(aa*116|0)+32+(Y<<2)|0;c[C>>2]=c[C>>2]|Z;C=1895748+(aa*116|0)+60+(Y<<2)|0;c[C>>2]=c[C>>2]|_}aa=aa+1|0}while((aa|0)!=8192);return}function Ab(d){d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;e=b[d+22>>1]|0;b[d+60>>1]=e;f=b[d+30>>1]|0;b[d+68>>1]=f;g=b[d+38>>1]|0;b[d+76>>1]=g;h=b[d+46>>1]|0;b[d+84>>1]=h;b[d+92>>1]=e|f|g|h;i=b[d+24>>1]|0;b[d+62>>1]=i;j=b[d+32>>1]|0;b[d+70>>1]=j;k=b[d+40>>1]|0;b[d+78>>1]=k;l=b[d+48>>1]|0;b[d+86>>1]=l;b[d+94>>1]=i|j|k|l;m=b[d+26>>1]|0;b[d+64>>1]=m;n=b[d+34>>1]|0;b[d+72>>1]=n;o=b[d+42>>1]|0;b[d+80>>1]=o;p=b[d+50>>1]|0;b[d+88>>1]=p;b[d+96>>1]=m|n|o|p;q=b[d+28>>1]|0;b[d+66>>1]=q;r=b[d+36>>1]|0;b[d+74>>1]=r;s=b[d+44>>1]|0;b[d+82>>1]=s;t=b[d+52>>1]|0;b[d+90>>1]=t;b[d+98>>1]=q|r|s|t;u=c[1862980+((e&65535)<<2)>>2]|0;a[d+100>>0]=u;e=c[1862980+((f&65535)<<2)>>2]|0;a[d+104>>0]=e;f=c[1862980+((g&65535)<<2)>>2]|0;a[d+108>>0]=f;g=d+112|0;a[g>>0]=c[1862980+((h&65535)<<2)>>2];h=c[1862980+((i&65535)<<2)>>2]|0;a[d+101>>0]=h;i=c[1862980+((j&65535)<<2)>>2]|0;a[d+105>>0]=i;j=c[1862980+((k&65535)<<2)>>2]|0;a[d+109>>0]=j;a[d+113>>0]=c[1862980+((l&65535)<<2)>>2];l=c[1862980+((m&65535)<<2)>>2]|0;a[d+102>>0]=l;m=c[1862980+((n&65535)<<2)>>2]|0;a[d+106>>0]=m;n=c[1862980+((o&65535)<<2)>>2]|0;a[d+110>>0]=n;a[d+114>>0]=c[1862980+((p&65535)<<2)>>2];a[d+103>>0]=c[1862980+((q&65535)<<2)>>2];a[d+107>>0]=c[1862980+((r&65535)<<2)>>2];a[d+111>>0]=c[1862980+((s&65535)<<2)>>2];a[d+115>>0]=c[1862980+((t&65535)<<2)>>2];c[d+116>>2]=h<<4&4080|u<<8&65280|l&255;c[d+120>>2]=i<<4&4080|e<<8&65280|m&255;c[d+124>>2]=j<<4&4080|f<<8&65280|n&255;n=c[g>>2]|0;c[d+128>>2]=n>>>4&4080|n<<8&65280|n>>>16&255;return}function Bb(d){d=d|0;var e=0,f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;e=i;i=i+240|0;f=e;g=0;do{a[d+4952+(g<<1)+1>>0]=-1;a[d+4952+(g<<1)>>0]=0;a[d+4960+(g<<1)+1>>0]=-1;a[d+4960+(g<<1)>>0]=0;a[d+4968+(g<<1)+1>>0]=-1;a[d+4968+(g<<1)>>0]=0;a[d+4976+(g<<1)+1>>0]=-1;a[d+4976+(g<<1)>>0]=0;a[d+4984+(g<<1)+1>>0]=-1;a[d+4984+(g<<1)>>0]=0;a[d+4992+(g<<1)+1>>0]=-1;a[d+4992+(g<<1)>>0]=0;a[d+5e3+(g<<1)+1>>0]=-1;a[d+5e3+(g<<1)>>0]=0;a[d+5008+(g<<1)+1>>0]=-1;a[d+5008+(g<<1)>>0]=0;a[d+5016+(g<<1)+1>>0]=-1;a[d+5016+(g<<1)>>0]=0;a[d+5024+(g<<1)+1>>0]=-1;a[d+5024+(g<<1)>>0]=0;a[d+5032+(g<<1)+1>>0]=-1;a[d+5032+(g<<1)>>0]=0;a[d+5040+(g<<1)+1>>0]=-1;a[d+5040+(g<<1)>>0]=0;a[d+5048+(g<<1)+1>>0]=-1;a[d+5048+(g<<1)>>0]=0;g=g+1|0}while((g|0)!=4);g=b[d+22>>1]|0;h=d+30|0;j=d+38|0;k=d+46|0;l=14;do{m=f+(l<<2)|0;c[m>>2]=0;n=b[1224+(l<<1)>>1]|0;if(!((n&g)<<16>>16))if(!((n&b[h>>1])<<16>>16))if(!((n&b[j>>1])<<16>>16)){if((n&b[k>>1])<<16>>16){o=3;p=6}}else{o=2;p=6}else{o=1;p=6}else{o=0;p=6}if((p|0)==6){p=0;c[m>>2]=o}l=l+-1|0}while((l|0)>1);l=b[d+24>>1]|0;o=d+32|0;k=d+40|0;j=d+48|0;h=14;do{g=f+60+(h<<2)|0;c[g>>2]=0;m=b[1224+(h<<1)>>1]|0;if(!((m&l)<<16>>16))if(!((m&b[o>>1])<<16>>16))if(!((m&b[k>>1])<<16>>16)){if((m&b[j>>1])<<16>>16){q=3;p=19}}else{q=2;p=19}else{q=1;p=19}else{q=0;p=19}if((p|0)==19){p=0;c[g>>2]=q}h=h+-1|0}while((h|0)>1);h=b[d+26>>1]|0;q=d+34|0;j=d+42|0;k=d+50|0;o=14;do{l=f+120+(o<<2)|0;c[l>>2]=0;g=b[1224+(o<<1)>>1]|0;if(!((g&h)<<16>>16))if(!((g&b[q>>1])<<16>>16))if(!((g&b[j>>1])<<16>>16)){if((g&b[k>>1])<<16>>16){r=3;p=26}}else{r=2;p=26}else{r=1;p=26}else{r=0;p=26}if((p|0)==26){p=0;c[l>>2]=r}o=o+-1|0}while((o|0)>1);o=b[d+28>>1]|0;r=d+36|0;k=d+44|0;j=d+52|0;q=14;do{h=f+180+(q<<2)|0;c[h>>2]=0;l=b[1224+(q<<1)>>1]|0;if(!((l&o)<<16>>16))if(!((l&b[r>>1])<<16>>16))if(!((l&b[k>>1])<<16>>16)){if((l&b[j>>1])<<16>>16){s=3;p=33}}else{s=2;p=33}else{s=1;p=33}else{s=0;p=33}if((p|0)==33){p=0;c[h>>2]=s}q=q+-1|0}while((q|0)>1);Pc(d+987984|0,f);q=1;s=2;p=1;do{j=p<<1;k=q>>>0<j>>>0;s=(k&1^1)+s|0;p=k?p:j;j=d+4944+(q*120|0)|0;k=d+4944+((p^q)*120|0)|0;r=j+120|0;do{c[j>>2]=c[k>>2];j=j+4|0;k=k+4|0}while((j|0)<(r|0));k=c[1862980+(q<<2)>>2]|0;if((k|0)>1){j=k;do{k=j;j=j+-1|0;a[d+4944+(q*120|0)+(k<<3)+1>>0]=a[d+4944+(q*120|0)+(j<<3)+1>>0]|0;a[d+4944+(q*120|0)+(k<<3)>>0]=a[d+4944+(q*120|0)+(j<<3)>>0]|0;a[d+4944+(q*120|0)+(k<<3)+3>>0]=a[d+4944+(q*120|0)+(j<<3)+3>>0]|0;a[d+4944+(q*120|0)+(k<<3)+2>>0]=a[d+4944+(q*120|0)+(j<<3)+2>>0]|0;a[d+4944+(q*120|0)+(k<<3)+5>>0]=a[d+4944+(q*120|0)+(j<<3)+5>>0]|0;a[d+4944+(q*120|0)+(k<<3)+4>>0]=a[d+4944+(q*120|0)+(j<<3)+4>>0]|0;a[d+4944+(q*120|0)+(k<<3)+7>>0]=a[d+4944+(q*120|0)+(j<<3)+7>>0]|0;a[d+4944+(q*120|0)+(k<<3)+6>>0]=a[d+4944+(q*120|0)+(j<<3)+6>>0]|0}while((j|0)>1)}j=s&255;a[d+4944+(q*120|0)+9>>0]=c[f+(s<<2)>>2];a[d+4944+(q*120|0)+8>>0]=j;a[d+4944+(q*120|0)+11>>0]=c[f+60+(s<<2)>>2];a[d+4944+(q*120|0)+10>>0]=j;a[d+4944+(q*120|0)+13>>0]=c[f+120+(s<<2)>>2];a[d+4944+(q*120|0)+12>>0]=j;a[d+4944+(q*120|0)+15>>0]=c[f+180+(s<<2)>>2];a[d+4944+(q*120|0)+14>>0]=j;q=q+1|0}while((q|0)!=8192);i=e;return}function Cb(d,e,f){d=d|0;e=e|0;f=f|0;var g=0,h=0,j=0,k=0,l=0,m=0,n=0;g=i;i=i+32|0;h=g;j=h;k=j+32|0;do{b[j>>1]=0;j=j+2|0}while((j|0)<(k|0));j=c[e+1472>>2]|0;if((j|0)>0){k=c[d+4>>2]|0;l=0;do{m=h+((k+l&3)<<3)+(c[d+8+(l<<2)>>2]<<1)|0;b[m>>1]=b[m>>1]|b[1224+(c[d+20+(l<<2)>>2]<<1)>>1];l=l+1|0}while((l|0)<(j|0));n=0}else n=0;do{j=(b[f+46+(n<<1)>>1]|b[h+24+(n<<1)>>1]|(b[f+38+(n<<1)>>1]|b[h+16+(n<<1)>>1]|(b[f+30+(n<<1)>>1]|b[h+8+(n<<1)>>1]|(b[f+22+(n<<1)>>1]|b[h+(n<<1)>>1]))))&65535;c[e+1480+(n<<3)>>2]=a[f+4944+(j*120|0)+8+(n<<1)>>0];c[e+1480+(n<<3)+4>>2]=a[f+4944+(j*120|0)+8+(n<<1)+1>>0];c[e+1512+(n<<3)>>2]=a[f+4944+(j*120|0)+16+(n<<1)>>0];c[e+1512+(n<<3)+4>>2]=a[f+4944+(j*120|0)+16+(n<<1)+1>>0];n=n+1|0}while((n|0)!=4);i=g;return}function Db(a){a=a|0;var b=0;b=0;do{c[a+3324+(b<<4)+4>>2]=0;c[a+4124+(b<<4)+4>>2]=0;b=b+1|0}while((b|0)!=50);h[a+4928>>3]=+Uc(a+987984|0)+960.0;return}function Eb(){return 960.0}function Fb(b,c){b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;do switch(b|0){case 1:{d=c;e=d;a[e>>0]=83;a[e+1>>0]=117;a[e+2>>0]=99;a[e+3>>0]=99;e=d+4|0;a[e>>0]=101;a[e+1>>0]=115;a[e+2>>0]=115;a[e+3>>0]=0;return}case -1:{f=c;g=1378;h=f+14|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -2:{f=c;g=1392;h=f+11|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -3:{f=c;g=1403;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -4:{f=c;g=1435;h=f+17|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -5:{f=c;g=1452;h=f+23|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -7:{f=c;g=1475;h=f+25|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -8:{f=c;g=1500;h=f+35|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -9:{f=c;g=1535;h=f+37|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -10:{f=c;g=1572;h=f+15|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -12:{f=c;g=1587;h=f+52|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -13:{f=c;g=1639;h=f+35|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -14:{f=c;g=1674;h=f+42|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -15:{f=c;g=1716;h=f+33|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -16:{f=c;g=1749;h=f+30|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -17:{f=c;g=1779;h=f+32|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -18:{f=c;g=1811;h=f+23|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -19:{f=c;g=1834;h=f+23|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -98:{f=c;g=1857;h=f+24|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -99:{f=c;g=1881;h=f+17|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -101:{f=c;g=1898;h=f+26|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -102:{f=c;g=1924;h=f+25|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -103:{f=c;g=1949;h=f+43|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -201:{f=c;g=1992;h=f+42|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -202:{f=c;g=2034;h=f+29|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}case -301:{f=c;g=2063;h=f+26|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}default:{f=c;g=2089;h=f+21|0;do{a[f>>0]=a[g>>0]|0;f=f+1|0;g=g+1|0}while((f|0)<(h|0));return}}while(0)}function Gb(){Lc(994176);Lb(1654516);Ma(8,0,n|0)|0;rc(1726592);Ma(9,1726592,n|0)|0;return}function Hb(a){a=a|0;Aa(a|0)|0;pd()}function Ib(a){a=a|0;Xb(1654516);Nc(994176);return}function Jb(f,g,h,i,j,k){f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;if((j|0)!=4?(l=f+1480+(j<<3)|0,(c[l>>2]|0)!=0):0){m=c[f+1480+(j<<3)+4>>2]|0;if(c[k+(m<<2)>>2]|0){n=c[f+1512+(j<<3)+4>>2]|0;if((n|0)==-1){o=1;return o|0}if(c[k+(n<<2)>>2]|0){o=1;return o|0}if((d[f+40+(n<<2)+j>>0]|0)<2){o=1;return o|0}if((m|0)==(c[456+(n<<2)>>2]|0)){if(((c[f+1476>>2]|0)+(h>>2)|0)>=(i|0)){o=1;return o|0}n=f+(h+9<<3)|0;m=n;b[m>>1]=0;b[m+2>>1]=0>>>16;m=n+4|0;b[m>>1]=0;b[m+2>>1]=0>>>16;b[f+72+(h<<3)+(j<<1)>>1]=b[1224+(c[f+1512+(j<<3)>>2]<<1)>>1]|0;o=0;return o|0}m=e[f+32+(j<<1)>>1]|0;n=a[k+4944+(m*120|0)+24+(j<<1)+1>>0]|0;if(n<<24>>24==-1){o=1;return o|0}if(c[k+(n<<24>>24<<2)>>2]|0){o=1;return o|0}if(((c[f+1476>>2]|0)+(h>>2)|0)>=(i|0)){o=1;return o|0}n=f+(h+9<<3)|0;p=n;b[p>>1]=0;b[p+2>>1]=0>>>16;p=n+4|0;b[p>>1]=0;b[p+2>>1]=0>>>16;b[f+72+(h<<3)+(j<<1)>>1]=b[1224+(a[k+4944+(m*120|0)+24+(j<<1)>>0]<<1)>>1]|0;o=0;return o|0}if((a[f+40+(g<<2)+j>>0]|0)==0?(a[f+40+(c[472+(g<<2)>>2]<<2)+j>>0]|0)==0:0){m=a[f+40+(c[440+(g<<2)>>2]<<2)+j>>0]|0;p=a[f+40+(c[456+(g<<2)>>2]<<2)+j>>0]|0;if(((h>>2)+1+(c[f+1476>>2]|0)-(((m&255)<(p&255)?p:m)&255)|0)>=(i|0)){o=1;return o|0}m=f+(h+9<<3)|0;p=m;b[p>>1]=0;b[p+2>>1]=0>>>16;p=m+4|0;b[p>>1]=0;b[p+2>>1]=0>>>16;o=0;return o|0}p=(c[f+1476>>2]|0)+(h>>2)|0;if((p|0)<(i|0)){m=f+(h+9<<3)|0;g=m;b[g>>1]=0;b[g+2>>1]=0>>>16;g=m+4|0;b[g>>1]=0;b[g+2>>1]=0>>>16;b[f+72+(h<<3)+(j<<1)>>1]=b[1224+(c[l>>2]<<1)>>1]|0;o=0;return o|0}if((p|0)!=(i|0)){o=1;return o|0}p=c[f+1512+(j<<3)+4>>2]|0;if((p|0)==-1){o=1;return o|0}l=c[f+1512+(j<<3)>>2]|0;if(!((l|0)!=0?(c[k+(p<<2)>>2]|0)==0:0)){o=1;return o|0}if((d[f+40+(p<<2)+j>>0]|0)<=1?(d[f+40+(c[472+(p<<2)>>2]<<2)+j>>0]|0)<=1:0){o=1;return o|0}p=f+(h+9<<3)|0;g=p;b[g>>1]=0;b[g+2>>1]=0>>>16;g=p+4|0;b[g>>1]=0;b[g+2>>1]=0>>>16;b[f+72+(h<<3)+(j<<1)>>1]=b[1224+(l<<1)>>1]|0;o=0;return o|0}l=c[f+1484>>2]|0;if((l|0)!=-1?(c[k+(l<<2)>>2]|0)==1:0){j=a[f+40+(l<<2)>>0]|0;g=a[f+40+(c[472+(l<<2)>>2]<<2)>>0]|0;q=((j&255)<(g&255)?g:j)&255}else q=0;j=c[f+1492>>2]|0;if((j|0)!=-1?(c[k+(j<<2)>>2]|0)==1:0){g=a[f+40+(j<<2)+1>>0]|0;p=a[f+40+(c[472+(j<<2)>>2]<<2)+1>>0]|0;r=(((g&255)<(p&255)?p:g)&255)+q|0}else r=q;q=c[f+1500>>2]|0;if((q|0)!=-1?(c[k+(q<<2)>>2]|0)==1:0){g=a[f+40+(q<<2)+2>>0]|0;p=a[f+40+(c[472+(q<<2)>>2]<<2)+2>>0]|0;s=(((g&255)<(p&255)?p:g)&255)+r|0}else s=r;r=c[f+1508>>2]|0;if((r|0)!=-1?(c[k+(r<<2)>>2]|0)==1:0){g=a[f+40+(r<<2)+3>>0]|0;p=a[f+40+(c[472+(r<<2)>>2]<<2)+3>>0]|0;t=(((g&255)<(p&255)?p:g)&255)+s|0}else t=s;s=c[f+1476>>2]|0;if(!((t|0)>0&(s+t|0)<(i|0))){o=1;return o|0}if((s+(h>>2)|0)<(i|0)){u=l;v=0}else{o=1;return o|0}while(1){do if((u|0)==-1)b[f+72+(h<<3)+(v<<1)>>1]=0;else{if(c[k+(u<<2)>>2]|0){b[f+72+(h<<3)+(v<<1)>>1]=0;break}if(((b[f+(c[472+(u<<2)>>2]<<3)+(v<<1)>>1]|0)==0?(b[f+(c[440+(u<<2)>>2]<<3)+(v<<1)>>1]|0)==0:0)?(b[f+(c[456+(u<<2)>>2]<<3)+(v<<1)>>1]|0)==0:0){b[f+72+(h<<3)+(v<<1)>>1]=0;break}b[f+72+(h<<3)+(v<<1)>>1]=b[1224+(c[f+1480+(v<<3)>>2]<<1)>>1]|0}while(0);l=v+1|0;if((l|0)==4){o=0;break}u=c[f+1480+(l<<3)+4>>2]|0;v=l}return o|0}function Kb(f,g,h,i,j,k){f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;if((j|0)!=4?(l=f+1480+(j<<3)|0,(c[l>>2]|0)!=0):0){m=c[f+1480+(j<<3)+4>>2]|0;if((c[k+(m<<2)>>2]|0)!=1){n=c[f+1512+(j<<3)+4>>2]|0;if((n|0)==-1){o=0;return o|0}if((c[k+(n<<2)>>2]|0)!=1){o=0;return o|0}if((d[f+40+(n<<2)+j>>0]|0)<2){o=0;return o|0}if((m|0)==(c[456+(n<<2)>>2]|0)){if(((c[f+1476>>2]|0)+1|0)<(i|0)){o=0;return o|0}n=f+(h+9<<3)|0;m=n;b[m>>1]=0;b[m+2>>1]=0>>>16;m=n+4|0;b[m>>1]=0;b[m+2>>1]=0>>>16;b[f+72+(h<<3)+(j<<1)>>1]=b[1224+(c[f+1512+(j<<3)>>2]<<1)>>1]|0;o=1;return o|0}m=e[f+32+(j<<1)>>1]|0;n=a[k+4944+(m*120|0)+24+(j<<1)+1>>0]|0;if(n<<24>>24==-1){o=0;return o|0}if((c[k+(n<<24>>24<<2)>>2]|0)!=1){o=0;return o|0}if(((c[f+1476>>2]|0)+1|0)<(i|0)){o=0;return o|0}n=f+(h+9<<3)|0;p=n;b[p>>1]=0;b[p+2>>1]=0>>>16;p=n+4|0;b[p>>1]=0;b[p+2>>1]=0>>>16;b[f+72+(h<<3)+(j<<1)>>1]=b[1224+(a[k+4944+(m*120|0)+24+(j<<1)>>0]<<1)>>1]|0;o=1;return o|0}if((a[f+40+(g<<2)+j>>0]|0)==0?(a[f+40+(c[472+(g<<2)>>2]<<2)+j>>0]|0)==0:0){m=a[f+40+(c[440+(g<<2)>>2]<<2)+j>>0]|0;p=a[f+40+(c[456+(g<<2)>>2]<<2)+j>>0]|0;if(((((m&255)<(p&255)?p:m)&255)+(c[f+1476>>2]|0)|0)<(i|0)){o=0;return o|0}m=f+(h+9<<3)|0;p=m;b[p>>1]=0;b[p+2>>1]=0>>>16;p=m+4|0;b[p>>1]=0;b[p+2>>1]=0>>>16;o=1;return o|0}p=c[f+1476>>2]|0;if((p+1|0)>=(i|0)){m=f+(h+9<<3)|0;g=m;b[g>>1]=0;b[g+2>>1]=0>>>16;g=m+4|0;b[g>>1]=0;b[g+2>>1]=0>>>16;b[f+72+(h<<3)+(j<<1)>>1]=b[1224+(c[l>>2]<<1)>>1]|0;o=1;return o|0}l=c[f+1512+(j<<3)+4>>2]|0;if((l|0)==-1){o=0;return o|0}if((c[k+(l<<2)>>2]|0)!=1){o=0;return o|0}g=f+1512+(j<<3)|0;if(!(c[g>>2]|0)){o=0;return o|0}if((d[f+40+(l<<2)+j>>0]|0)>1){if((p+2|0)<(i|0)){o=0;return o|0}}else if((p+2|0)<(i|0)?1:(d[f+40+(c[472+(l<<2)>>2]<<2)+j>>0]|0)<2){o=0;return o|0}l=f+(h+9<<3)|0;p=l;b[p>>1]=0;b[p+2>>1]=0>>>16;p=l+4|0;b[p>>1]=0;b[p+2>>1]=0>>>16;b[f+72+(h<<3)+(j<<1)>>1]=b[1224+(c[g>>2]<<1)>>1]|0;o=1;return o|0}g=c[f+1484>>2]|0;if((g|0)!=-1?(c[k+(g<<2)>>2]|0)==0:0){j=a[f+40+(g<<2)>>0]|0;p=a[f+40+(c[472+(g<<2)>>2]<<2)>>0]|0;q=((j&255)<(p&255)?p:j)&255}else q=0;j=c[f+1492>>2]|0;if((j|0)!=-1?(c[k+(j<<2)>>2]|0)==0:0){p=a[f+40+(j<<2)+1>>0]|0;l=a[f+40+(c[472+(j<<2)>>2]<<2)+1>>0]|0;r=(((p&255)<(l&255)?l:p)&255)+q|0}else r=q;q=c[f+1500>>2]|0;if((q|0)!=-1?(c[k+(q<<2)>>2]|0)==0:0){p=a[f+40+(q<<2)+2>>0]|0;l=a[f+40+(c[472+(q<<2)>>2]<<2)+2>>0]|0;s=(((p&255)<(l&255)?l:p)&255)+r|0}else s=r;r=c[f+1508>>2]|0;if((r|0)!=-1?(c[k+(r<<2)>>2]|0)==0:0){p=a[f+40+(r<<2)+3>>0]|0;l=a[f+40+(c[472+(r<<2)>>2]<<2)+3>>0]|0;t=(((p&255)<(l&255)?l:p)&255)+s|0}else t=s;s=c[f+1476>>2]|0;if((s+1|0)<(i|0)|((t|0)<1?1:((h>>2)+1-t+s|0)<(i|0))){o=0;return o|0}else{u=g;v=0}while(1){do if((u|0)==-1)b[f+72+(h<<3)+(v<<1)>>1]=0;else{if((c[k+(u<<2)>>2]|0)!=1){b[f+72+(h<<3)+(v<<1)>>1]=0;break}if(((b[f+(c[472+(u<<2)>>2]<<3)+(v<<1)>>1]|0)==0?(b[f+(c[440+(u<<2)>>2]<<3)+(v<<1)>>1]|0)==0:0)?(b[f+(c[456+(u<<2)>>2]<<3)+(v<<1)>>1]|0)==0:0){b[f+72+(h<<3)+(v<<1)>>1]=0;break}b[f+72+(h<<3)+(v<<1)>>1]=b[1224+(c[f+1480+(v<<3)>>2]<<1)>>1]|0}while(0);g=v+1|0;if((g|0)==4){o=1;break}u=c[f+1480+(g<<3)+4>>2]|0;v=g}return o|0}function Lb(b){b=b|0;var d=0,e=0,f=0;d=b+15176|0;a[d>>0]=78;a[d+1>>0]=84;a[d+2>>0]=48;a[d+3>>0]=0;d=b+15216|0;a[d>>0]=a[2110]|0;a[d+1>>0]=a[2111]|0;a[d+2>>0]=a[2112]|0;a[d+3>>0]=a[2113]|0;a[d+4>>0]=a[2114]|0;a[d+5>>0]=a[2115]|0;a[d+6>>0]=a[2116]|0;d=b+15256|0;e=2117;f=d+9|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15296|0;e=2126;f=d+12|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15336|0;e=2138;f=d+12|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15376|0;e=2150;f=d+15|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15416|0;e=2165;f=d+9|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15456|0;e=2174;f=d+12|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15496|0;e=2186;f=d+12|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15536|0;e=2198;f=d+15|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15576|0;e=2213;f=d+9|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15616|0;e=2222;f=d+12|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));d=b+15656|0;e=2234;f=d+14|0;do{a[d>>0]=a[e>>0]|0;d=d+1|0;e=e+1|0}while((d|0)<(f|0));e=0;do{d=0;do{c[b+14968+(e<<4)+(d<<2)>>2]=-1;c[b+15696+(e<<6)+(d<<4)>>2]=0;c[b+16528+(e<<6)+(d<<4)>>2]=0;c[b+17360+(e*848|0)+(d*212|0)>>2]=0;c[b+28384+(e*848|0)+(d*212|0)>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+4>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+4>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+20>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+20>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+36>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+36>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+52>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+52>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+68>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+68>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+84>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+84>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+100>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+100>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+116>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+116>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+132>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+132>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+148>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+148>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+164>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+164>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+180>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+180>>2]=0;c[b+17360+(e*848|0)+(d*212|0)+196>>2]=0;c[b+28384+(e*848|0)+(d*212|0)+196>>2]=0;d=d+1|0}while((d|0)!=4);e=e+1|0}while((e|0)!=13);c[b+39408>>2]=0;c[b+39620>>2]=0;c[b+39412>>2]=0;c[b+39624>>2]=0;c[b+39428>>2]=0;c[b+39640>>2]=0;c[b+39444>>2]=0;c[b+39656>>2]=0;c[b+39460>>2]=0;c[b+39672>>2]=0;c[b+39476>>2]=0;c[b+39688>>2]=0;c[b+39492>>2]=0;c[b+39704>>2]=0;c[b+39508>>2]=0;c[b+39720>>2]=0;c[b+39524>>2]=0;c[b+39736>>2]=0;c[b+39540>>2]=0;c[b+39752>>2]=0;c[b+39556>>2]=0;c[b+39768>>2]=0;c[b+39572>>2]=0;c[b+39784>>2]=0;c[b+39588>>2]=0;c[b+39800>>2]=0;c[b+39604>>2]=0;c[b+39816>>2]=0;a[b+39836>>0]=0;c[b+39832>>2]=c[220];e=b+39948|0;c[e>>2]=1;c[e+4>>2]=0;e=b+39956|0;c[e>>2]=2;c[e+4>>2]=0;e=b+39964|0;c[e>>2]=3;c[e+4>>2]=0;e=b+39972|0;c[e>>2]=4;c[e+4>>2]=0;e=b+39980|0;c[e>>2]=5;c[e+4>>2]=0;e=b+39988|0;c[e>>2]=6;c[e+4>>2]=0;e=b+39996|0;c[e>>2]=7;c[e+4>>2]=0;e=b+40004|0;c[e>>2]=8;c[e+4>>2]=0;e=b+40012|0;c[e>>2]=9;c[e+4>>2]=0;e=b+40020|0;c[e>>2]=9;c[e+4>>2]=0;e=b+40028|0;c[e>>2]=10;c[e+4>>2]=0;e=b+40036|0;c[e>>2]=11;c[e+4>>2]=0;return}function Mb(b,d){b=b|0;d=d|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;f=c[b+4>>2]|0;g=c[b>>2]|0;h=e[d+(c[472+(g<<2)>>2]<<3)+(f<<1)>>1]|0;i=c[1797444+(h<<2)>>2]|0;j=e[d+(c[456+(g<<2)>>2]<<3)+(f<<1)>>1]|0;g=c[1797444+(j<<2)>>2]|0;k=c[(c[b+2896>>2]|0)+80>>2]|0;if((g|0)>(i|0)&(g|0)>(k|0)){g=c[b+28>>2]|0;if((g|0)<=0)return;l=c[b+14964>>2]|0;m=0;do{c[l+(m<<4)+12>>2]=0-(c[l+(m<<4)+4>>2]|0);m=m+1|0}while((m|0)<(g|0));return}g=c[1830212+(h<<2)>>2]|0;h=c[1830212+(j<<2)>>2]|0;j=c[b+28>>2]|0;if((j|0)<=0)return;m=c[b+14964>>2]|0;b=e[d+32+(f<<1)>>1]|0;f=0;do{d=c[m+(f<<4)+4>>2]|0;l=a[3641020+(b*15|0)+d>>0]|0;do if(!((d|0)>(i|0)&(d|0)>(k|0))){if((g|0)>(d|0)|(h|0)>(d|0)){c[m+(f<<4)+12>>2]=l+-3;break}if((d|0)<(k|0)){c[m+(f<<4)+12>>2]=l+-11;break}if(!(c[m+(f<<4)+8>>2]|0)){c[m+(f<<4)+12>>2]=13-d;break}else{c[m+(f<<4)+12>>2]=l+10;break}}else c[m+(f<<4)+12>>2]=81-d;while(0);f=f+1|0}while((f|0)<(j|0));return}function Nb(b,d){b=b|0;d=d|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0;f=c[b+4>>2]|0;g=c[b>>2]|0;h=472+(g<<2)|0;i=e[d+(c[h>>2]<<3)+(f<<1)>>1]|0;j=c[1797444+(i<<2)>>2]|0;k=456+(g<<2)|0;l=e[d+(c[k>>2]<<3)+(f<<1)>>1]|0;m=c[1797444+(l<<2)>>2]|0;n=c[1830212+(i<<2)>>2]|0;i=c[1830212+(l<<2)>>2]|0;l=c[b+28>>2]|0;if((l|0)<=0)return;o=(m|0)>(j|0);p=(m|0)>0;q=(j|0)==0;r=(m|0)==0;s=c[b+14964>>2]|0;t=c[b+20>>2]|0;u=c[(c[b+2896>>2]|0)+80>>2]|0;b=e[d+32+(f<<1)>>1]|0;v=o&(m|0)>(u|0);w=(m|0)>(u|0);x=d+(g<<3)+(f<<1)|0;g=o&(m|0)>(u|0);o=(u|0)>(m|0)&(u|0)>(j|0);y=t;z=0;while(1){A=c[s+(z<<4)+4>>2]|0;B=a[3641020+(b*15|0)+A>>0]|0;do if((f|0)==(y|0))if(!v)if((A|0)>(j|0)&(A|0)>(u|0)){C=f;D=24}else{E=f;D=40}else{C=y;D=24}else{if((A|0)>(j|0)&(A|0)>(u|0)){if(!q){C=y;D=24;break}F=c[h>>2]|0;if(!(a[d+40+(F<<2)+y>>0]|0)){C=y;D=24;break}if(!r){E=y;D=40;break}G=c[k>>2]|0;if(!(a[d+40+(G<<2)+y>>0]|0)){E=y;D=40;break}if((e[d+(G<<3)+(y<<1)>>1]|0)>(e[d+(F<<3)+(y<<1)>>1]|0)){C=y;D=24;break}else{E=y;D=40;break}}if(g){if(!q){C=y;D=24;break}if(!(a[d+40+(c[h>>2]<<2)+y>>0]|0)){C=y;D=24;break}else{E=y;D=40;break}}if(!((u|0)>(A|0)&o)){if(!r){E=y;D=40;break}if(!(a[d+40+(c[k>>2]<<2)+t>>0]|0)){E=t;D=40;break}else{C=t;D=24;break}}if(r?(F=c[k>>2]|0,(a[d+40+(F<<2)+y>>0]|0)!=0):0)if((q?(G=c[h>>2]|0,(a[d+40+(G<<2)+y>>0]|0)!=0):0)?(e[d+(F<<3)+(y<<1)>>1]|0)<=(e[d+(G<<3)+(y<<1)>>1]|0):0){E=y;D=40}else{C=y;D=24}else{E=y;D=40}}while(0);do if((D|0)==24){D=0;if((n|0)>(A|0)){c[s+(z<<4)+12>>2]=B+40;H=C;break}if(w?(e[x>>1]|0)>(e[d+(c[k>>2]<<3)+(f<<1)>>1]|0):0){c[s+(z<<4)+12>>2]=B+41;H=C;break}if((A|0)<=(u|0))if(p){c[s+(z<<4)+12>>2]=47-A;H=C;break}else{c[s+(z<<4)+12>>2]=40-A;H=C;break}if((A|0)<(m|0)){c[s+(z<<4)+12>>2]=78-A;H=C;break}if((A|0)>(j|0)){c[s+(z<<4)+12>>2]=73-A;H=C;break}if(!(c[s+(z<<4)+8>>2]|0)){c[s+(z<<4)+12>>2]=49-A;H=C;break}else{c[s+(z<<4)+12>>2]=62-A;H=C;break}}else if((D|0)==40){D=0;if((A|0)<(n|0)|(A|0)<(i|0)){c[s+(z<<4)+12>>2]=B+-9;H=E;break}if((A|0)<(u|0)){c[s+(z<<4)+12>>2]=B+-16;H=E;break}if(!(c[s+(z<<4)+8>>2]|0)){c[s+(z<<4)+12>>2]=10-A;H=E;break}else{c[s+(z<<4)+12>>2]=22-A;H=E;break}}while(0);z=z+1|0;if((z|0)>=(l|0))break;else y=H}return}function Ob(d,f){d=d|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;g=c[d+4>>2]|0;h=c[d>>2]|0;i=c[d+24>>2]|0;j=c[d+8>>2]|0;k=a[f+40+(j<<2)+i>>0]|0;l=(k&255)<<6;if((e[f+(c[456+(h<<2)>>2]<<3)+(g<<1)>>1]|0)>((b[1224+(c[(c[d+2896>>2]|0)+80>>2]<<1)>>1]|b[f+(c[472+(h<<2)>>2]<<3)+(g<<1)>>1])&65535)){g=(l>>>0)/23|0;switch(k<<24>>24){case 2:{m=(c[f+1512+(i<<3)+4>>2]|0)==(j|0)?g+-2|0:g;break}case 1:{m=(c[f+1480+(i<<3)+4>>2]|0)==(j|0)?g+-3|0:g;break}default:m=g}g=c[d+32>>2]|0;h=c[d+28>>2]|0;if((g|0)>=(h|0))return;n=c[d+14964>>2]|0;o=g;do{c[n+(o<<4)+12>>2]=m-(c[n+(o<<4)+4>>2]|0);o=o+1|0}while((o|0)<(h|0));return}else{h=(l>>>0)/33|0;switch(k<<24>>24){case 2:{p=(c[f+1512+(i<<3)+4>>2]|0)==(j|0)?h+-6|0:h;break}case 1:{p=(c[f+1480+(i<<3)+4>>2]|0)==(j|0)?h+-8|0:h;break}default:p=h}h=c[d+32>>2]|0;j=c[d+28>>2]|0;if((h|0)>=(j|0))return;i=c[d+14964>>2]|0;d=h;do{c[i+(d<<4)+12>>2]=p-(c[i+(d<<4)+4>>2]|0);d=d+1|0}while((d|0)<(j|0));return}}function Pb(d,f){d=d|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;g=c[d+24>>2]|0;h=c[d+8>>2]|0;i=a[f+40+(h<<2)+g>>0]|0;j=c[d+4>>2]|0;k=c[d+20>>2]|0;if((j|0)==(k|0)){l=c[d>>2]|0;m=(i&255)<<6;if((e[f+(c[456+(l<<2)>>2]<<3)+(j<<1)>>1]|0)<=((b[1224+(c[(c[d+2896>>2]|0)+80>>2]<<1)>>1]|b[f+(c[472+(l<<2)>>2]<<3)+(j<<1)>>1])&65535)){l=(m>>>0)/36|0;if(i<<24>>24==2)n=(c[f+1512+(g<<3)+4>>2]|0)==(h|0)?l+-4|0:l;else n=l}else n=(m>>>0)/44|0;m=c[d+32>>2]|0;l=c[d+28>>2]|0;if((m|0)>=(l|0))return;o=c[d+14964>>2]|0;p=m;do{c[o+(p<<4)+12>>2]=n-(c[o+(p<<4)+4>>2]|0);p=p+1|0}while((p|0)<(l|0));return}l=c[d>>2]|0;p=472+(l<<2)|0;o=c[p>>2]|0;n=(a[f+40+(o<<2)+j>>0]|0)!=0;if((g|0)!=(k|0)){m=c[456+(l<<2)>>2]|0;do if(n){if((e[f+(m<<3)+(j<<1)>>1]|0)>((b[1224+(c[(c[d+2896>>2]|0)+80>>2]<<1)>>1]|b[f+(o<<3)+(j<<1)>>1])&65535)){q=(((i&255)<<6>>>0)/44|0)+60|0;break}if((a[f+40+(m<<2)+j>>0]|0)==0?(a[f+40+(m<<2)+k>>0]|0)!=0:0){q=(((i&255)<<6>>>0)/44|0)+60|0;break}r=((i&255)<<6>>>0)/36|0;s=r+-2|0;if(i<<24>>24==2)q=(c[f+1512+(g<<3)+4>>2]|0)==(h|0)?r+-6|0:s;else q=s}else{if((a[f+40+(m<<2)+j>>0]|0)==0?(e[f+(m<<3)+(k<<1)>>1]|0)>(e[f+(o<<3)+(k<<1)>>1]|0):0){q=(((i&255)<<6>>>0)/44|0)+60|0;break}if((a[f+40+(o<<2)+k>>0]|0)==0?(e[f+(m<<3)+(j<<1)>>1]|0)>(e[1224+(c[(c[d+2896>>2]|0)+80>>2]<<1)>>1]|0):0){q=(((i&255)<<6>>>0)/44|0)+60|0;break}s=((i&255)<<6>>>0)/36|0;r=s+-2|0;if(i<<24>>24==2)q=(c[f+1512+(g<<3)+4>>2]|0)==(h|0)?s+-6|0:r;else q=r}while(0);m=c[d+32>>2]|0;k=c[d+28>>2]|0;if((m|0)>=(k|0))return;r=c[d+14964>>2]|0;s=m;do{c[r+(s<<4)+12>>2]=q-(c[r+(s<<4)+4>>2]|0);s=s+1|0}while((s|0)<(k|0));return}if(n){n=c[d+32>>2]|0;k=c[d+28>>2]|0;if((n|0)>=(k|0))return;s=c[d+14964>>2]|0;r=(((i&255)<<6>>>0)/44|0)+24|0;q=n;do{c[s+(q<<4)+12>>2]=r-(c[s+(q<<4)+4>>2]|0);q=q+1|0}while((q|0)<(k|0));return}k=c[456+(l<<2)>>2]|0;if(((a[f+40+(k<<2)+j>>0]|0)==0?(a[f+40+(k<<2)+g>>0]|0)!=0:0)?(e[f+(k<<3)+(g<<1)>>1]|0)>(e[f+(o<<3)+(g<<1)>>1]|0):0){k=c[d+32>>2]|0;j=c[d+28>>2]|0;if((k|0)>=(j|0))return;l=c[d+14964>>2]|0;q=(((i&255)<<6>>>0)/44|0)+24|0;s=k;do{c[l+(s<<4)+12>>2]=q-(c[l+(s<<4)+4>>2]|0);s=s+1|0}while((s|0)<(j|0));return}j=c[d+32>>2]|0;s=c[d+28>>2]|0;if((j|0)>=(s|0))return;l=c[d+14964>>2]|0;d=(i&255)<<6;q=((d>>>0)/44|0)+24|0;k=(d>>>0)/36|0;d=k+-4|0;if(i<<24>>24==2){i=f+1512+(g<<3)+4|0;r=o;n=j;while(1){m=c[l+(n<<4)+4>>2]|0;if((e[1224+(m<<1)>>1]|0)>(e[f+(r<<3)+(g<<1)>>1]|0))c[l+(n<<4)+12>>2]=q-m;else c[l+(n<<4)+12>>2]=((c[i>>2]|0)==(h|0)?d:k)+15-m;m=n+1|0;if((m|0)>=(s|0))break;r=c[p>>2]|0;n=m}return}else{n=k+15|0;k=o;o=j;while(1){j=c[l+(o<<4)+4>>2]|0;if((e[1224+(j<<1)>>1]|0)>(e[f+(k<<3)+(g<<1)>>1]|0))c[l+(o<<4)+12>>2]=q-j;else c[l+(o<<4)+12>>2]=n-j;j=o+1|0;if((j|0)>=(s|0))break;k=c[p>>2]|0;o=j}return}}function Qb(b,d){b=b|0;d=d|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0;f=c[b+4>>2]|0;g=c[b>>2]|0;h=456+(g<<2)|0;i=e[d+(c[h>>2]<<3)+(f<<1)>>1]|0;j=c[1797444+(i<<2)>>2]|0;k=c[1830212+(i<<2)>>2]|0;l=b+14964|0;m=c[l>>2]|0;n=c[m+4>>2]|0;o=c[b+2896>>2]|0;p=c[o+128>>2]|0;if((p|0)==0?(q=c[o+80>>2]|0,(q|0)>(j|0)):0){r=c[b+28>>2]|0;if((r|0)>0?(c[m+12>>2]=0-n,(r|0)>1):0){s=1;do{c[m+(s<<4)+12>>2]=0-(c[m+(s<<4)+4>>2]|0);s=s+1|0}while((s|0)<(r|0))}if(a[d+40+(g<<2)+f>>0]|0)return;if((c[d+1480+(f<<3)+4>>2]|0)!=(c[b+8>>2]|0))return;s=a[d+40+(c[h>>2]<<2)+f>>0]|0;h=a[d+40+(c[440+(g<<2)>>2]<<2)+f>>0]|0;t=(h&255)<(s&255)?(s&255)+-1|0:h&255;h=e[d+(c[472+(g<<2)>>2]<<3)+(f<<1)>>1]|0;g=r+-1|0;r=0;while(1){if((r|0)>=(g|0)){u=r;break}d=r+1|0;if((c[m+(d<<4)+4>>2]|0)>(q|0))r=d;else{u=r;break}}r=c[1895748+(h*116|0)>>2]|0;g=e[1224+(q<<1)>>1]|c[o+204+(f<<2)>>2];q=c[1895748+(h*116|0)+60+(r<<2)>>2]|0;a:do if((r|0)>0){d=q;s=r;while(1){v=c[1895748+(h*116|0)+88+(s<<2)>>2]|0;if((v&g|0)!=(v|0)){w=d;break a}v=s;s=s+-1|0;x=c[1895748+(h*116|0)+60+(s<<2)>>2]|d;if((v|0)<=1){w=x;break}else d=x}}else w=q;while(0);if((t|0)>=(c[1862980+(w<<2)>>2]|0))return;w=m+(u<<4)+12|0;c[w>>2]=(c[w>>2]|0)+20;return}if((n|0)>=(k|0)?(k=c[o+92>>2]|0,(n|0)>=(k|0)):0){b:do if((j|0)>(n|0)&(j|0)>(k|0)){w=c[1895748+(i*116|0)>>2]|0;u=c[o+204+(f<<2)>>2]|0;c:do if((w|0)>0){t=w;while(1){q=c[1895748+(i*116|0)+88+(t<<2)>>2]|0;if((q&u|0)!=(q|0)){y=t;break c}q=t+-1|0;if((t|0)>1)t=q;else{z=q;A=25;break}}}else{z=w;A=25}while(0);if((A|0)==25)if(!z){B=-1;break}else y=z;w=c[1895748+(i*116|0)+4+(y+-1<<2)>>2]|0;if((w|0)>(k|0)){u=c[b+28>>2]|0;if((u|0)>0){if((n|0)>(w|0)){t=0;while(1){q=t+1|0;if((q|0)>=(u|0)){B=t;break b}if((c[m+(q<<4)+4>>2]|0)>(w|0))t=q;else{C=q;break}}}else C=0;if(C){B=C+-1|0;break}}}else if((p|0)==1?(t=c[b+28>>2]|0,(t|0)>0):0){if((n|0)>(k|0)){w=0;while(1){u=w+1|0;if((u|0)>=(t|0)){B=w;break b}if((c[m+(u<<4)+4>>2]|0)>(k|0))w=u;else{D=u;break}}}else D=0;if(D){B=D+-1|0;break}}B=-1}else B=-1;while(0);D=c[b+28>>2]|0;if((D|0)>0?(c[m+12>>2]=((n|0)>(j|0)&(n|0)>(k|0)?60:0)-n,(D|0)>1):0){p=1;do{C=c[m+(p<<4)+4>>2]|0;c[m+(p<<4)+12>>2]=((C|0)>(j|0)&(C|0)>(k|0)?60:0)-C;p=p+1|0}while((p|0)<(D|0))}if((B|0)==-1)return;D=(c[l>>2]|0)+(B<<4)+12|0;c[D>>2]=(c[D>>2]|0)+20;return}D=c[b+28>>2]|0;if((D|0)<=0)return;c[m+12>>2]=0-n;if((D|0)>1)E=1;else return;do{c[m+(E<<4)+12>>2]=0-(c[m+(E<<4)+4>>2]|0);E=E+1|0}while((E|0)<(D|0));return}function Rb(a,b){a=a|0;b=b|0;var d=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0;d=c[a+4>>2]|0;f=e[b+(c[456+(c[a>>2]<<2)>>2]<<3)+(d<<1)>>1]|0;b=c[1797444+(f<<2)>>2]|0;g=c[1830212+(f<<2)>>2]|0;h=a+14964|0;i=c[h>>2]|0;j=c[i+4>>2]|0;k=c[a+20>>2]|0;l=c[a+2896>>2]|0;if((d|0)==(k|0)){m=c[l+128>>2]|0;if((m|0)==0?(c[l+80>>2]|0)>(b|0):0){n=c[a+28>>2]|0;if((n|0)<=0)return;c[i+12>>2]=0-j;if((n|0)>1)o=1;else return;do{c[i+(o<<4)+12>>2]=0-(c[i+(o<<4)+4>>2]|0);o=o+1|0}while((o|0)<(n|0));return}if((j|0)>=(g|0)?(n=c[l+92>>2]|0,(j|0)>=(n|0)):0){if((j|0)>(b|0)){o=c[a+28>>2]|0;if((o|0)>0){p=j;q=0}else return;while(1){if((p|0)>(b|0)&(p|0)>(n|0))c[i+(q<<4)+12>>2]=58-p;else c[i+(q<<4)+12>>2]=0-p;r=q+1|0;if((r|0)>=(o|0))break;p=c[i+(r<<4)+4>>2]|0;q=r}return}q=c[1895748+(f*116|0)>>2]|0;p=c[l+204+(d<<2)>>2]|0;a:do if((q|0)>0){o=q;while(1){r=c[1895748+(f*116|0)+88+(o<<2)>>2]|0;if((r&p|0)!=(r|0)){s=o;t=23;break a}r=o+-1|0;if((o|0)>1)o=r;else{u=r;t=22;break}}}else{u=q;t=22}while(0);if((t|0)==22)if(!u)v=-1;else{s=u;t=23}b:do if((t|0)==23){u=c[1895748+(f*116|0)+4+(s+-1<<2)>>2]|0;if((u|0)>(n|0)){q=c[a+28>>2]|0;if((q|0)>0){if((j|0)>(u|0)){p=0;while(1){o=p+1|0;if((o|0)>=(q|0)){v=p;break b}if((c[i+(o<<4)+4>>2]|0)>(u|0))p=o;else{w=o;break}}}else w=0;if(w){v=w+-1|0;break}}}else if((m|0)==1?(p=c[a+28>>2]|0,(p|0)>0):0){if((j|0)>(n|0)){u=0;while(1){q=u+1|0;if((q|0)>=(p|0)){v=u;break b}if((c[i+(q<<4)+4>>2]|0)>(n|0))u=q;else{x=q;break}}}else x=0;if(x){v=x+-1|0;break}}v=-1}while(0);x=c[a+28>>2]|0;if((x|0)>0?(c[i+12>>2]=0-j,(x|0)>1):0){n=1;do{c[i+(n<<4)+12>>2]=0-(c[i+(n<<4)+4>>2]|0);n=n+1|0}while((n|0)<(x|0))}if((v|0)==-1)return;x=i+(v<<4)+12|0;c[x>>2]=(c[x>>2]|0)+20;return}x=c[a+28>>2]|0;if((x|0)<=0)return;c[i+12>>2]=0-j;if((x|0)>1)y=1;else return;do{c[i+(y<<4)+12>>2]=0-(c[i+(y<<4)+4>>2]|0);y=y+1|0}while((y|0)<(x|0));return}if((c[l+88>>2]|0)==(k|0)){k=c[a+28>>2]|0;if((k|0)<=0)return;c[i+12>>2]=0-j;if((k|0)>1)z=1;else return;do{c[i+(z<<4)+12>>2]=0-(c[i+(z<<4)+4>>2]|0);z=z+1|0}while((z|0)<(k|0));return}k=c[l+128>>2]|0;z=(b|0)==0;if(k){if(z){x=c[a+28>>2]|0;if((x|0)<=0)return;y=c[l+92>>2]|0;c[i+12>>2]=((j|0)>(y|0)?20:0)-j;if((x|0)>1)A=1;else return;do{v=c[i+(A<<4)+4>>2]|0;c[i+(A<<4)+12>>2]=((v|0)>(y|0)?20:0)-v;A=A+1|0}while((A|0)<(x|0));return}if((j|0)>=(g|0)?(x=c[l+92>>2]|0,(j|0)>=(x|0)):0){if((j|0)>(b|0)){A=c[a+28>>2]|0;if((A|0)<=0)return;c[i+12>>2]=((j|0)>(b|0)&(j|0)>(x|0)?58:0)-j;if((A|0)>1)B=1;else return;do{y=c[i+(B<<4)+4>>2]|0;c[i+(B<<4)+12>>2]=((y|0)>(b|0)&(y|0)>(x|0)?58:0)-y;B=B+1|0}while((B|0)<(A|0));return}A=c[1895748+(f*116|0)>>2]|0;B=c[l+204+(d<<2)>>2]|0;c:do if((A|0)>0){y=A;while(1){v=c[1895748+(f*116|0)+88+(y<<2)>>2]|0;if((v&B|0)!=(v|0)){C=y;t=96;break c}v=y+-1|0;if((y|0)>1)y=v;else{D=v;t=95;break}}}else{D=A;t=95}while(0);if((t|0)==95)if(!D)E=-1;else{C=D;t=96}d:do if((t|0)==96){D=c[1895748+(f*116|0)+4+(C+-1<<2)>>2]|0;if((D|0)>(x|0)){A=c[a+28>>2]|0;if((A|0)>0){if((j|0)>(D|0)){B=0;while(1){y=B+1|0;if((y|0)>=(A|0)){E=B;break d}if((c[i+(y<<4)+4>>2]|0)>(D|0))B=y;else{F=y;break}}}else F=0;if(F){E=F+-1|0;break}}}else if((k|0)==1?(B=c[a+28>>2]|0,(B|0)>0):0){if((j|0)>(x|0)){D=0;while(1){A=D+1|0;if((A|0)>=(B|0)){E=D;break d}if((c[i+(A<<4)+4>>2]|0)>(x|0))D=A;else{G=A;break}}}else G=0;if(G){E=G+-1|0;break}}E=-1}while(0);G=c[a+28>>2]|0;if((G|0)>0?(c[i+12>>2]=((j|0)>(b|0)&(j|0)>(x|0)?60:0)-j,(G|0)>1):0){k=1;do{F=c[i+(k<<4)+4>>2]|0;c[i+(k<<4)+12>>2]=((F|0)>(b|0)&(F|0)>(x|0)?60:0)-F;k=k+1|0}while((k|0)<(G|0))}if((E|0)==-1)return;G=(c[h>>2]|0)+(E<<4)+12|0;c[G>>2]=(c[G>>2]|0)+20;return}G=c[a+28>>2]|0;if((G|0)<=0)return;c[i+12>>2]=0-j;if((G|0)>1)H=1;else return;do{c[i+(H<<4)+12>>2]=0-(c[i+(H<<4)+4>>2]|0);H=H+1|0}while((H|0)<(G|0));return}if(z){z=c[a+28>>2]|0;if((z|0)<=0)return;c[i+12>>2]=0-j;if((z|0)>1)I=1;else return;do{c[i+(I<<4)+12>>2]=0-(c[i+(I<<4)+4>>2]|0);I=I+1|0}while((I|0)<(z|0));return}if((c[l+80>>2]|0)>(b|0)){z=c[a+28>>2]|0;if((z|0)<=0)return;c[i+12>>2]=0-j;if((z|0)>1)J=1;else return;do{c[i+(J<<4)+12>>2]=0-(c[i+(J<<4)+4>>2]|0);J=J+1|0}while((J|0)<(z|0));return}if((j|0)>=(g|0)?(g=c[l+92>>2]|0,(j|0)>=(g|0)):0){if((j|0)>(b|0)){z=c[a+28>>2]|0;if((z|0)<=0)return;c[i+12>>2]=((j|0)>(b|0)?58:0)-j;if((z|0)>1)K=1;else return;do{J=c[i+(K<<4)+4>>2]|0;c[i+(K<<4)+12>>2]=((J|0)>(b|0)?58:0)-J;K=K+1|0}while((K|0)<(z|0));return}z=c[1895748+(f*116|0)>>2]|0;K=c[l+204+(d<<2)>>2]|0;e:do if((z|0)>0){d=z;while(1){l=c[1895748+(f*116|0)+88+(d<<2)>>2]|0;if((l&K|0)!=(l|0)){L=d;t=67;break e}l=d+-1|0;if((d|0)>1)d=l;else{M=l;t=66;break}}}else{M=z;t=66}while(0);if((t|0)==66)if(!M)N=-1;else{L=M;t=67}f:do if((t|0)==67){M=c[1895748+(f*116|0)+4+(L+-1<<2)>>2]|0;if((M|0)>(g|0)?(z=c[a+28>>2]|0,(z|0)>0):0){if((j|0)>(M|0)){K=0;while(1){d=K+1|0;if((d|0)>=(z|0)){N=K;break f}if((c[i+(d<<4)+4>>2]|0)>(M|0))K=d;else{O=d;break}}}else O=0;if(O){N=O+-1|0;break}}N=-1}while(0);O=c[a+28>>2]|0;if((O|0)>0?(c[i+12>>2]=((j|0)>(b|0)&(j|0)>(g|0)?60:0)-j,(O|0)>1):0){L=1;do{f=c[i+(L<<4)+4>>2]|0;c[i+(L<<4)+12>>2]=((f|0)>(b|0)&(f|0)>(g|0)?60:0)-f;L=L+1|0}while((L|0)<(O|0))}if((N|0)==-1)return;O=(c[h>>2]|0)+(N<<4)+12|0;c[O>>2]=(c[O>>2]|0)+20;return}O=c[a+28>>2]|0;if((O|0)<=0)return;c[i+12>>2]=0-j;if((O|0)>1)P=1;else return;do{c[i+(P<<4)+12>>2]=0-(c[i+(P<<4)+4>>2]|0);P=P+1|0}while((P|0)<(O|0));return}function Sb(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;e=c[b+24>>2]|0;f=c[b+8>>2]|0;g=a[d+40+(f<<2)+e>>0]|0;h=((g&255)<<6>>>0)/24|0;switch(g<<24>>24){case 2:{i=(c[d+1512+(e<<3)+4>>2]|0)==(f|0)?h+-4|0:h;break}case 1:{i=(c[d+1480+(e<<3)+4>>2]|0)==(f|0)?h+-4|0:h;break}default:i=h}h=c[b+32>>2]|0;f=c[b+28>>2]|0;if((h|0)>=(f|0))return;e=c[b+14964>>2]|0;b=h;do{c[e+(b<<4)+12>>2]=i-(c[e+(b<<4)+4>>2]|0);b=b+1|0}while((b|0)<(f|0));return}function Tb(b,d){b=b|0;d=d|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;f=c[b+24>>2]|0;g=a[d+40+(c[b+8>>2]<<2)+f>>0]|0;h=c[b+4>>2]|0;i=c[b>>2]|0;j=456+(i<<2)|0;k=c[j>>2]|0;l=c[1797444+(e[d+(k<<3)+(h<<1)>>1]<<2)>>2]|0;m=c[b+20>>2]|0;if(!((h|0)!=(m|0)&(f|0)==(m|0))){m=((g&255)<<6>>>0)/40|0;n=c[b+32>>2]|0;o=c[b+28>>2]|0;if((n|0)>=(o|0))return;p=c[b+14964>>2]|0;q=n;do{c[p+(q<<4)+12>>2]=m-(c[p+(q<<4)+4>>2]|0);q=q+1|0}while((q|0)<(o|0));return}o=c[b+2896>>2]|0;q=o+128|0;do if((c[q>>2]|0)==0?(c[o+80>>2]|0)>(l|0):0){if((l|0)==0?(a[d+40+(k<<2)+f>>0]|0)!=0:0)break;p=c[b+32>>2]|0;m=c[b+28>>2]|0;if((p|0)>=(m|0))return;n=c[b+14964>>2]|0;r=p;do{c[n+(r<<4)+12>>2]=-50-(c[n+(r<<4)+4>>2]|0);r=r+1|0}while((r|0)<(m|0));return}while(0);k=c[b+32>>2]|0;m=c[b+28>>2]|0;if((k|0)>=(m|0))return;r=b+14964|0;b=(g&255)<<6;g=((b>>>0)/40|0)+-32|0;n=(l|0)!=0;l=(b>>>0)/50|0;b=l+36|0;p=l+48|0;s=l+-12|0;t=l+72|0;l=c[o+88>>2]|0;u=o+92|0;o=d+32+(f<<1)|0;v=d+1512+(h<<3)+4|0;h=f;w=k;while(1){if((l|0)==(h|0)?(k=c[r>>2]|0,x=c[k+(w<<4)+4>>2]|0,(x|0)<(c[u>>2]|0)):0){c[k+(w<<4)+12>>2]=g+(a[3641020+((e[o>>1]|0)*15|0)+x>>0]|0);y=l}else z=17;do if((z|0)==17){z=0;if(c[q>>2]|0){x=c[r>>2]|0;k=c[x+(w<<4)+4>>2]|0;if(n){c[x+(w<<4)+12>>2]=t-k;y=h;break}if((e[1224+(k<<1)>>1]|0)>(e[d+(c[j>>2]<<3)+(f<<1)>>1]|0)){c[x+(w<<4)+12>>2]=p-k;y=f;break}else{c[x+(w<<4)+12>>2]=b-k;y=f;break}}if(n){k=c[r>>2]|0;x=c[k+(w<<4)+4>>2]|0;if((c[v>>2]|0)==(i|0)){c[k+(w<<4)+12>>2]=b-x;y=h;break}else{c[k+(w<<4)+12>>2]=p-x;y=h;break}}else{x=c[r>>2]|0;k=c[x+(w<<4)+4>>2]|0;if((e[1224+(k<<1)>>1]|0)>(e[d+(c[j>>2]<<3)+(f<<1)>>1]|0)){c[x+(w<<4)+12>>2]=p-k;y=f;break}else{c[x+(w<<4)+12>>2]=s-k;y=f;break}}}while(0);w=w+1|0;if((w|0)>=(m|0))break;else h=y}return}function Ub(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0;b=c[a+2896>>2]|0;do if((c[b+132>>2]|0)!=1){d=c[a+20>>2]|0;if((c[a+4>>2]|0)!=(d|0)?(c[b+100>>2]|0)==(d|0):0)break;d=c[a+28>>2]|0;if((d|0)<=0)return;e=c[a+14964>>2]|0;f=c[b+104>>2]|0;g=0;do{h=c[e+(g<<4)+4>>2]|0;c[e+(g<<4)+12>>2]=((h|0)>(f|0)?30:0)-h;g=g+1|0}while((g|0)<(d|0));return}while(0);b=c[a+28>>2]|0;if((b|0)<=0)return;d=c[a+14964>>2]|0;a=0;do{c[d+(a<<4)+12>>2]=0-(c[d+(a<<4)+4>>2]|0);a=a+1|0}while((a|0)<(b|0));return}function Vb(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;e=c[b+24>>2]|0;f=c[b+8>>2]|0;g=a[d+40+(f<<2)+e>>0]|0;h=((g&255)<<6>>>0)/27|0;switch(g<<24>>24){case 2:{i=(c[d+1512+(e<<3)+4>>2]|0)==(f|0)?h+-6|0:h;break}case 1:{i=(c[d+1480+(e<<3)+4>>2]|0)==(f|0)?h+-8|0:h;break}default:i=h}h=c[b+32>>2]|0;f=c[b+28>>2]|0;if((h|0)>=(f|0))return;e=c[b+14964>>2]|0;b=h;do{c[e+(b<<4)+12>>2]=i-(c[e+(b<<4)+4>>2]|0);b=b+1|0}while((b|0)<(f|0));return}function Wb(b,d){b=b|0;d=d|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;f=b+24|0;g=c[f>>2]|0;h=c[b+8>>2]|0;i=a[d+40+(h<<2)+g>>0]|0;j=((i&255)<<6>>>0)/24|0;if(i<<24>>24==2)k=(c[d+1512+(g<<3)+4>>2]|0)==(h|0)?j+-2|0:j;else k=j;j=c[b+20>>2]|0;if((c[b+4>>2]|0)==(j|0)){h=c[b+32>>2]|0;i=c[b+28>>2]|0;if((h|0)>=(i|0))return;l=c[b+14964>>2]|0;m=h;do{c[l+(m<<4)+12>>2]=k-(c[l+(m<<4)+4>>2]|0);m=m+1|0}while((m|0)<(i|0));return}i=b+2896|0;m=c[i>>2]|0;if((c[m+132>>2]|0)==1){l=c[b+32>>2]|0;h=c[b+28>>2]|0;n=(l|0)<(h|0);if((g|0)==(j|0)){if(!n)return;o=c[b+14964>>2]|0;p=k+2|0;q=l;do{c[o+(q<<4)+12>>2]=p-(c[o+(q<<4)+4>>2]|0);q=q+1|0}while((q|0)<(h|0));return}else{if(!n)return;n=c[b+14964>>2]|0;q=k+25|0;o=l;do{c[n+(o<<4)+12>>2]=q-(c[n+(o<<4)+4>>2]|0);o=o+1|0}while((o|0)<(h|0));return}}h=(g|0)==(j|0);o=c[b+32>>2]|0;n=b+28|0;q=c[n>>2]|0;l=(o|0)<(q|0);if((c[m+100>>2]|0)!=(j|0))if(h){if(!l)return;j=c[b+14964>>2]|0;p=e[d+32+(g<<1)>>1]|0;r=o;do{c[j+(r<<4)+12>>2]=(a[(c[j+(r<<4)+4>>2]|0)+(3641020+(p*15|0))>>0]|0)+33;r=r+1|0}while((r|0)<(q|0));return}else{if(!l)return;r=c[b+14964>>2]|0;p=k+14|0;j=o;do{c[r+(j<<4)+12>>2]=p-(c[r+(j<<4)+4>>2]|0);j=j+1|0}while((j|0)<(q|0));return}if(!h){if(!l)return;h=c[b+14964>>2]|0;j=k+14|0;k=o;do{c[h+(k<<4)+12>>2]=j-(c[h+(k<<4)+4>>2]|0);k=k+1|0}while((k|0)<(q|0));return}if(!l)return;l=b+14964|0;b=g;g=m;m=o;while(1){o=c[l>>2]|0;q=c[o+(m<<4)+4>>2]|0;c[o+(m<<4)+12>>2]=(a[3641020+((e[d+32+(b<<1)>>1]|0)*15|0)+q>>0]|0)+((q|0)>(c[g+104>>2]|0)?33:-13);q=m+1|0;if((q|0)>=(c[n>>2]|0))break;b=c[f>>2]|0;g=c[i>>2]|0;m=q}return}function Xb(a){a=a|0;var b=0;b=c[a+39832>>2]|0;if((b|0)==0|(b|0)==(c[220]|0))return;ge(b)|0;return}function Yb(a,b,d,f,g,h,i,j){a=a|0;b=b|0;d=d|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0;c[a+16>>2]=b;c[a+20>>2]=i;if(!d){c[a+36+(b*220|0)>>2]=j;k=0}else k=0;do{c[a+2900+(k*928|0)+224>>2]=0;c[a+2900+(k*928|0)+228>>2]=0;c[a+2900+(k*928|0)+456>>2]=0;c[a+2900+(k*928|0)+460>>2]=0;c[a+2900+(k*928|0)+688>>2]=0;c[a+2900+(k*928|0)+692>>2]=0;c[a+2900+(k*928|0)+920>>2]=0;c[a+2900+(k*928|0)+924>>2]=0;k=k+1|0}while((k|0)!=13);k=(e[h+2>>1]|0)^65535^(e[h+10>>1]|0)^(e[h+18>>1]|0);j=(e[h+4>>1]|0)^65535^(e[h+12>>1]|0)^(e[h+20>>1]|0);i=(e[h+6>>1]|0)^65535^(e[h+14>>1]|0)^(e[h+22>>1]|0);c[a+36+(b*220|0)+204>>2]=(e[h>>1]|0)^65535^(e[h+8>>1]|0)^(e[h+16>>1]|0)^(e[h+24>>1]|0);c[a+36+(b*220|0)+208>>2]=k^(e[h+26>>1]|0);c[a+36+(b*220|0)+212>>2]=j^(e[h+28>>1]|0);c[a+36+(b*220|0)+216>>2]=i^(e[h+30>>1]|0);if((d|0)>0)l=0;else return;do{h=a+36+(b*220|0)+204+(c[g+(l<<2)>>2]<<2)|0;c[h>>2]=c[h>>2]^(e[1224+(c[f+(l<<2)>>2]<<1)>>1]|0);l=l+1|0}while((l|0)!=(d|0));return}function Zb(a,b,d){a=a|0;b=b|0;d=d|0;c[a+36+(b*220|0)>>2]=d;return}function _b(a,d,e,f,g,h){a=a|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0;i=a+36+(d*220|0)|0;j=a+2896|0;c[j>>2]=i;k=c[i>>2]|0;c[a>>2]=k;c[a+8>>2]=k;c[a+16>>2]=d;k=a+14964|0;c[k>>2]=a+2900+(d*928|0);i=a+36+(d*220|0)+140|0;c[i>>2]=0;c[i+4>>2]=0;c[i+8>>2]=0;c[i+12>>2]=0;i=a+28|0;c[i>>2]=0;l=c[a+20>>2]|0;if((l|0)!=4){m=(c[e+1480+(l<<3)>>2]|0)==0;l=a+24|0;c[l>>2]=0;n=a+32|0;if(m){o=n;p=l;q=4}else{m=0;do{r=b[e+(c[a>>2]<<3)+(m<<1)>>1]|0;if(!(r<<16>>16))s=m;else{t=r&65535;r=c[i>>2]|0;c[n>>2]=r;u=c[1895748+(t*116|0)>>2]|0;v=c[(c[j>>2]|0)+204+(m<<2)>>2]|0;if((u|0)>-1){w=c[k>>2]|0;x=r;r=u;while(1){u=c[1895748+(t*116|0)+4+(r<<2)>>2]|0;y=c[1895748+(t*116|0)+32+(r<<2)>>2]|0;a:do if((r|0)>0){z=r;A=y;while(1){B=c[1895748+(t*116|0)+88+(z<<2)>>2]|0;if((B&v|0)!=(B|0)){C=z;D=A;break a}B=z+-1|0;E=c[1895748+(t*116|0)+60+(B<<2)>>2]|A;if((z|0)>1){z=B;A=E}else{C=B;D=E;break}}}else{C=r;D=y}while(0);c[w+(x<<4)+8>>2]=D;c[w+(x<<4)>>2]=m;c[w+(x<<4)+4>>2]=u;x=x+1|0;c[i>>2]=x;if((C|0)<=0)break;else r=C+-1|0}}$b(a,e,f,g,h);s=c[l>>2]|0}m=s+1|0;c[l>>2]=m}while((m|0)<4)}}else{m=a+24|0;c[m>>2]=0;o=a+32|0;p=m;q=4}if((q|0)==4){q=0;do{m=b[e+(c[a>>2]<<3)+(q<<1)>>1]|0;if(!(m<<16>>16))F=q;else{l=m&65535;m=c[i>>2]|0;c[o>>2]=m;s=c[1895748+(l*116|0)>>2]|0;C=c[(c[j>>2]|0)+204+(q<<2)>>2]|0;if((s|0)>-1){D=c[k>>2]|0;n=m;m=s;while(1){s=c[1895748+(l*116|0)+4+(m<<2)>>2]|0;r=c[1895748+(l*116|0)+32+(m<<2)>>2]|0;b:do if((m|0)>0){x=m;w=r;while(1){t=c[1895748+(l*116|0)+88+(x<<2)>>2]|0;if((t&C|0)!=(t|0)){G=x;H=w;break b}t=x+-1|0;v=c[1895748+(l*116|0)+60+(t<<2)>>2]|w;if((x|0)>1){x=t;w=v}else{G=t;H=v;break}}}else{G=m;H=r}while(0);c[D+(n<<4)+8>>2]=H;c[D+(n<<4)>>2]=q;c[D+(n<<4)+4>>2]=s;n=n+1|0;c[i>>2]=n;if((G|0)<=0)break;else m=G+-1|0}}ac(a,e,f,g,h);F=c[p>>2]|0}q=F+1|0;c[p>>2]=q}while((q|0)<4)}c[a+2900+(d*928|0)+224>>2]=0;q=c[i>>2]|0;c[a+2900+(d*928|0)+228>>2]=q+-1;if((q|0)==1){I=1;return I|0}bc(a);I=c[i>>2]|0;return I|0}function $b(f,g,h,i,j){f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0;k=c[f+24>>2]|0;l=c[f>>2]|0;m=a[g+40+(c[440+(l<<2)>>2]<<2)+k>>0]|0;n=a[g+40+(c[456+(l<<2)>>2]<<2)+k>>0]|0;if(!(m<<24>>24))o=(c[f+16>>2]|0)+1|0;else o=m&255;p=n<<24>>24==0;if(p)q=(c[f+16>>2]|0)+1|0;else q=n&255;r=(q+o<<7|0)/-13|0;o=c[f+32>>2]|0;q=c[f+28>>2]|0;if((o|0)>=(q|0))return;s=e[g+32+(k<<1)>>1]|0;t=n<<24>>24==1;n=m<<24>>24==1;m=h+4|0;u=i+4|0;v=c[f+14964>>2]|0;w=c[f+20>>2]|0;f=(k|0)==(w|0);x=g+1480+(k<<3)+4|0;y=456+(l<<2)|0;z=(k|0)!=(w|0);A=(a[g+40+(l<<2)+k>>0]|0)==1&z;B=g+40+(l<<2)+w|0;C=472+(l<<2)|0;D=g+1480+(k<<3)|0;E=(k|0)==(w|0);F=472+(l<<2)|0;G=440+(l<<2)|0;H=440+(l<<2)|0;I=g+1480+(k<<3)+4|0;J=440+(l<<2)|0;K=g+1480+(k<<3)+4|0;L=456+(l<<2)|0;M=g+1512+(k<<3)+4|0;N=472+(l<<2)|0;O=g+1480+(k<<3)|0;P=472+(l<<2)|0;Q=g+1512+(k<<3)+4|0;R=g+1512+(k<<3)|0;S=g+1480+(k<<3)+4|0;T=j+(s*120|0)+24+(k<<1)+1|0;j=g+1512+(k<<3)+4|0;U=472+(l<<2)|0;V=472+(l<<2)|0;W=g+1512+(k<<3)|0;X=472+(l<<2)|0;Y=440+(l<<2)|0;Z=g+1512+(k<<3)+4|0;_=440+(l<<2)|0;$=472+(l<<2)|0;aa=440+(l<<2)|0;ba=456+(l<<2)|0;ca=472+(l<<2)|0;da=o;do{o=c[v+(da<<4)+4>>2]|0;ea=a[3641020+(s*15|0)+o>>0]|0;if(!f){fa=c[aa>>2]|0;if((b[g+(fa<<3)+(k<<1)>>1]|0)==0?(b[g+(fa<<3)+(w<<1)>>1]|0)!=0:0)ga=15;else{fa=c[ba>>2]|0;if((b[g+(fa<<3)+(k<<1)>>1]|0)==0?(b[g+(fa<<3)+(w<<1)>>1]|0)!=0:0)ga=15;else ha=0}if((ga|0)==15){ga=0;ha=-12}fa=c[ca>>2]|0;if(!(a[g+40+(fa<<2)+k>>0]|0))ia=p|(a[g+40+(fa<<2)+w>>0]|0)==0?ha:ha+17|0;else ia=ha}else ia=0;fa=c[x>>2]|0;ja=c[y>>2]|0;ka=(fa|0)==(ja|0);if(!ka?(la=c[Z>>2]|0,(la|0)!=(ja|0)):0)if((fa|0)==(c[_>>2]|0)?(la|0)==(c[$>>2]|0):0)ma=(a[g+40+(la<<2)+k>>0]|0)==1?ia:ia+27|0;else ma=ia;else ma=t?ia:ia+-12|0;if((A?(a[B>>0]|0)!=0:0)?(la=c[C>>2]|0,(d[g+40+(la<<2)+k>>0]|0)>1):0)na=(fa|0)==(la|0)?ma+19|0:ma;else na=ma;la=na+r|0;oa=(c[D>>2]|0)==(o|0);do if(oa)if(!E){pa=c[F>>2]|0;if((a[g+40+(pa<<2)+k>>0]|0)==0?(a[g+40+(pa<<2)+w>>0]|0)!=0:0){qa=c[G>>2]|0;if((a[g+40+(qa<<2)+k>>0]|0)==0?(e[g+(pa<<3)+(w<<1)>>1]|0)<=(e[g+(qa<<3)+(w<<1)>>1]|0):0){ra=pa;sa=qa;ga=86;break}if(a[g+40+(ja<<2)+k>>0]|0){ga=59;break}if((e[g+(pa<<3)+(w<<1)>>1]|0)>(e[g+(ja<<3)+(w<<1)>>1]|0)){ga=59;break}else{ra=pa;sa=qa;ga=86;break}}qa=c[H>>2]|0;if((a[g+40+(qa<<2)+k>>0]|0)==0?(a[g+40+(qa<<2)+w>>0]|0)!=0:0){ra=pa;sa=qa;ga=86;break}if((a[g+40+(ja<<2)+k>>0]|0)==0?(a[g+40+(ja<<2)+w>>0]|0)!=0:0){ra=pa;sa=qa;ga=86}else ga=59}else ga=59;else{qa=c[X>>2]|0;pa=c[Y>>2]|0;if((e[g+(qa<<3)+(k<<1)>>1]|0)>((b[g+(ja<<3)+(k<<1)>>1]|b[g+(pa<<3)+(k<<1)>>1])&65535)){if(!z){ga=59;break}if((a[g+40+(pa<<2)+k>>0]|0)==0?(a[g+40+(pa<<2)+w>>0]|0)!=0:0){ra=qa;sa=pa;ga=86;break}if(a[g+40+(ja<<2)+k>>0]|0){ga=59;break}if(!(a[g+40+(ja<<2)+w>>0]|0)){ga=59;break}else{ra=qa;sa=pa;ga=86;break}}if((z?(a[g+40+(qa<<2)+k>>0]|0)==0:0)?(a[g+40+(qa<<2)+w>>0]|0)!=0:0){if(!(a[g+40+(pa<<2)+k>>0]|0)){if(((a[g+40+(pa<<2)+w>>0]|0)!=0?(a[g+40+(ja<<2)+k>>0]|0)==0:0)?(a[g+40+(ja<<2)+w>>0]|0)!=0:0)if((e[g+(qa<<3)+(w<<1)>>1]|0)>((b[g+(ja<<3)+(w<<1)>>1]|b[g+(pa<<3)+(w<<1)>>1])&65535)){ga=59;break}else{ra=qa;sa=pa;ga=86;break}if(a[g+40+(pa<<2)+w>>0]|0)if((e[g+(qa<<3)+(w<<1)>>1]|0)>(e[g+(pa<<3)+(w<<1)>>1]|0)){ga=59;break}else{ra=qa;sa=pa;ga=86;break}}if(((a[g+40+(ja<<2)+k>>0]|0)==0?(a[g+40+(ja<<2)+w>>0]|0)!=0:0)?(e[g+(qa<<3)+(w<<1)>>1]|0)<=(e[g+(ja<<3)+(w<<1)>>1]|0):0){ra=qa;sa=pa;ga=86}else ga=59}else{ra=qa;sa=pa;ga=86}}while(0);do if((ga|0)==59){ga=0;if(n?(c[I>>2]|0)==(c[J>>2]|0):0)ga=64;else ga=61;do if((ga|0)==61){ga=0;if(t){ja=c[K>>2]|0;if((ja|0)==(c[L>>2]|0)){ga=64;break}else ta=ja}else ta=c[S>>2]|0;if((ta|0)==(l|0)){if((c[M>>2]|0)==(c[N>>2]|0)){ja=ea+48+la|0;c[v+(da<<4)+12>>2]=ja;ua=ja;break}if((c[O>>2]|0)==(o|0)){ja=la+31|0;c[v+(da<<4)+12>>2]=ja;ua=ja;break}else{ja=ea+-3+la|0;c[v+(da<<4)+12>>2]=ja;ua=ja;break}}if((ta|0)==(c[P>>2]|0))if((c[Q>>2]|0)==(l|0)){ja=ea+42+la|0;c[v+(da<<4)+12>>2]=ja;ua=ja;break}else{ja=ea+28+la|0;c[v+(da<<4)+12>>2]=ja;ua=ja;break}if(!(c[v+(da<<4)+8>>2]|0)){ja=ea+11+la|0;c[v+(da<<4)+12>>2]=ja;ua=ja;break}if((o|0)==(c[R>>2]|0)){ja=la+40|0;c[v+(da<<4)+12>>2]=ja;ua=ja;break}else{ja=ea+22+la|0;c[v+(da<<4)+12>>2]=ja;ua=ja;break}}while(0);if((ga|0)==64){ga=0;ja=ea+35+la|0;c[v+(da<<4)+12>>2]=ja;ua=ja}if((c[h>>2]|0)==(k|0)?(c[m>>2]|0)==(o|0):0){c[v+(da<<4)+12>>2]=ua+55;break}if((c[i>>2]|0)==(k|0)?(c[u>>2]|0)==(o|0):0)c[v+(da<<4)+12>>2]=ua+18}else if((ga|0)==86){ga=0;ja=a[T>>0]|0;pa=c[j>>2]|0;qa=(pa|0)==(ra|0);va=(ra|0)==(ja|0);do if(qa&va)wa=la+20|0;else{if(!(!((pa|0)!=(l|0)|va^1)?(d[g+40+(ra<<2)+k>>0]|0)>1:0)){if(!((l|0)==(ja|0)&qa)){wa=la;break}if((d[g+40+(ra<<2)+k>>0]|0)<=1){wa=la;break}}wa=la+13|0}while(0);do if(n&(fa|0)==(sa|0)|t&ka){qa=ea+2+wa|0;c[v+(da<<4)+12>>2]=qa;xa=qa}else{if((fa|0)==(l|0)){if((pa|0)==(c[U>>2]|0)){qa=ea+33+wa|0;c[v+(da<<4)+12>>2]=qa;xa=qa;break}if(oa){qa=wa+38|0;c[v+(da<<4)+12>>2]=qa;xa=qa;break}else{qa=ea+-14+wa|0;c[v+(da<<4)+12>>2]=qa;xa=qa;break}}if((fa|0)==(c[V>>2]|0)){qa=ea+34+wa|0;c[v+(da<<4)+12>>2]=qa;xa=qa;break}if((c[v+(da<<4)+8>>2]|0)!=0?(o|0)==(c[W>>2]|0):0){qa=wa+35|0;c[v+(da<<4)+12>>2]=qa;xa=qa;break}qa=wa+17-o|0;c[v+(da<<4)+12>>2]=qa;xa=qa}while(0);if((c[h>>2]|0)==(k|0)?(c[m>>2]|0)==(o|0):0)c[v+(da<<4)+12>>2]=xa+18}while(0);da=da+1|0}while((da|0)<(q|0));return}function ac(f,g,h,i,j){f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0;k=c[f+24>>2]|0;l=e[g+32+(k<<1)>>1]|0;m=c[f>>2]|0;n=440+(m<<2)|0;o=a[g+40+(c[n>>2]<<2)+k>>0]|0;p=456+(m<<2)|0;q=a[g+40+(c[p>>2]<<2)+k>>0]|0;if(!(o<<24>>24))r=(c[f+16>>2]|0)+1|0;else r=o&255;if(!(q<<24>>24))s=(c[f+16>>2]|0)+1|0;else s=q&255;t=(s+r<<7|0)/-19|0;r=(a[g+40+(c[472+(m<<2)>>2]<<2)+k>>0]|0)==0?t+-9|0:t;t=c[f+32>>2]|0;s=c[f+28>>2]|0;if((t|0)>=(s|0))return;u=(q<<24>>24!=1)<<31>>31;v=o<<24>>24==1;o=q<<24>>24==1;q=h+4|0;w=i+4|0;x=o?r:r+-10|0;y=r+31|0;z=c[f+14964>>2]|0;f=g+1480+(k<<3)|0;A=g+1512+(k<<3)+4|0;B=v?16:22;C=472+(m<<2)|0;D=440+(m<<2)|0;E=456+(m<<2)|0;F=g+1480+(k<<3)+4|0;G=g+1512+(k<<3)+4|0;H=j+(l*120|0)+24+(k<<1)+1|0;j=g+1512+(k<<3)|0;I=g+1512+(k<<3)+4|0;J=t;do{t=c[z+(J<<4)+4>>2]|0;K=a[3641020+(l*15|0)+t>>0]|0;do if((c[f>>2]|0)!=(t|0)?(L=c[C>>2]|0,M=c[D>>2]|0,N=c[E>>2]|0,(e[g+(L<<3)+(k<<1)>>1]|0)<=((b[g+(N<<3)+(k<<1)>>1]|b[g+(M<<3)+(k<<1)>>1])&65535)):0){O=c[F>>2]|0;P=(O|0)==(N|0);if(!P){Q=c[I>>2]|0;if((Q|0)!=(N|0))if((O|0)==(M|0)&(Q|0)==(L|0)){R=L;S=(a[g+40+(L<<2)+k>>0]|0)==1?r:y}else{R=Q;S=r}else{R=N;S=x}}else{R=c[G>>2]|0;S=x}N=a[H>>0]|0;Q=(R|0)==(L|0);T=(L|0)==(N|0);do if(Q&T)U=S+35|0;else{if(!(!((R|0)!=(m|0)|T^1)?(d[g+40+(L<<2)+k>>0]|0)>1:0)){if(!((m|0)==(N|0)&Q)){U=S;break}if((d[g+40+(L<<2)+k>>0]|0)<=1){U=S;break}}U=S+25|0}while(0);do if(!(v&(O|0)==(M|0)|o&P)){if((O|0)==(m|0)){L=K+-17+U|0;c[z+(J<<4)+12>>2]=L;V=L;break}if(!(c[z+(J<<4)+8>>2]|0)){L=K+12+U|0;c[z+(J<<4)+12>>2]=L;V=L;break}if((t|0)==(c[j>>2]|0)){L=U+48|0;c[z+(J<<4)+12>>2]=L;V=L;break}else{L=29-K+U|0;c[z+(J<<4)+12>>2]=L;V=L;break}}else{L=K+28+U|0;c[z+(J<<4)+12>>2]=L;V=L}while(0);if((c[h>>2]|0)==(k|0)?(c[q>>2]|0)==(t|0):0){c[z+(J<<4)+12>>2]=V+47;break}if((c[i>>2]|0)==(k|0)?(c[w>>2]|0)==(t|0):0)c[z+(J<<4)+12>>2]=V+19}else W=12;while(0);do if((W|0)==12){W=0;O=c[A>>2]|0;P=c[p>>2]|0;M=c[n>>2]|0;L=r+((O|0)==(P|0)?u:(O|0)==(M|0)?B:0)|0;if((v|(O|0)!=(M|0))&(o|(O|0)!=(P|0))){P=K+45+L|0;c[z+(J<<4)+12>>2]=P;X=P}else{P=K+18+L|0;c[z+(J<<4)+12>>2]=P;X=P}if((c[h>>2]|0)==(k|0)?(c[q>>2]|0)==(t|0):0){c[z+(J<<4)+12>>2]=X+126;break}if((c[i>>2]|0)==(k|0)?(c[w>>2]|0)==(t|0):0)c[z+(J<<4)+12>>2]=X+32}while(0);J=J+1|0}while((J|0)<(s|0));return}function bc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0,Qa=0,Ra=0,Sa=0,Ta=0,Ua=0,Va=0,Wa=0,Xa=0,Ya=0,Za=0,_a=0,$a=0,ab=0,bb=0,cb=0,db=0,eb=0,fb=0,gb=0,hb=0,ib=0,jb=0,kb=0,lb=0,mb=0,nb=0,ob=0,pb=0,qb=0,rb=0,sb=0,tb=0,ub=0,vb=0,wb=0,xb=0,yb=0,zb=0,Ab=0,Bb=0,Cb=0,Db=0,Eb=0,Fb=0,Gb=0,Hb=0,Ib=0,Jb=0,Kb=0,Lb=0,Mb=0,Nb=0,Ob=0,Pb=0,Qb=0,Rb=0,Sb=0,Tb=0,Ub=0,Vb=0,Wb=0,Xb=0,Yb=0,Zb=0,_b=0,$b=0,ac=0,bc=0,cc=0,dc=0,ec=0,fc=0,gc=0,hc=0,ic=0,jc=0,kc=0,lc=0,mc=0,nc=0,oc=0,pc=0,qc=0,rc=0,sc=0,tc=0,uc=0,vc=0,wc=0,xc=0,yc=0,zc=0,Ac=0,Bc=0,Cc=0,Dc=0,Ec=0,Fc=0,Gc=0,Hc=0,Ic=0,Jc=0,Kc=0,Lc=0,Mc=0,Nc=0,Oc=0,Pc=0,Qc=0,Rc=0,Sc=0,Tc=0,Uc=0,Vc=0,Wc=0,Xc=0,Yc=0,Zc=0,_c=0,$c=0,ad=0,bd=0,cd=0,dd=0,ed=0,fd=0,gd=0,hd=0,id=0,jd=0,kd=0;b=i;i=i+16|0;d=b;e=a+28|0;f=c[e>>2]|0;do switch(f|0){case 12:{g=a+14964|0;h=c[g>>2]|0;j=h+12|0;if((c[j>>2]|0)<(c[h+28>>2]|0)){c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];k=c[j>>2]|0;j=h+16|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=c[g>>2]|0;l=j+16|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+28>>2]=k;m=c[g>>2]|0}else m=h;h=m+44|0;if((c[h>>2]|0)<(c[m+60>>2]|0)){k=m+32|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];j=c[h>>2]|0;h=m+48|0;c[k>>2]=c[h>>2];c[k+4>>2]=c[h+4>>2];c[k+8>>2]=c[h+8>>2];c[k+12>>2]=c[h+12>>2];h=c[g>>2]|0;k=h+48|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[h+60>>2]=j;n=c[g>>2]|0}else n=m;j=n+76|0;if((c[j>>2]|0)<(c[n+92>>2]|0)){h=n+64|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];k=c[j>>2]|0;j=n+80|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=c[g>>2]|0;h=j+80|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[j+92>>2]=k;o=c[g>>2]|0}else o=n;k=o+108|0;if((c[k>>2]|0)<(c[o+124>>2]|0)){j=o+96|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];h=c[k>>2]|0;k=o+112|0;c[j>>2]=c[k>>2];c[j+4>>2]=c[k+4>>2];c[j+8>>2]=c[k+8>>2];c[j+12>>2]=c[k+12>>2];k=c[g>>2]|0;j=k+112|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[k+124>>2]=h;p=c[g>>2]|0}else p=o;h=p+140|0;if((c[h>>2]|0)<(c[p+156>>2]|0)){k=p+128|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];j=c[h>>2]|0;h=p+144|0;c[k>>2]=c[h>>2];c[k+4>>2]=c[h+4>>2];c[k+8>>2]=c[h+8>>2];c[k+12>>2]=c[h+12>>2];h=c[g>>2]|0;k=h+144|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[h+156>>2]=j;q=c[g>>2]|0}else q=p;j=q+172|0;if((c[j>>2]|0)<(c[q+188>>2]|0)){h=q+160|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];k=c[j>>2]|0;j=q+176|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=c[g>>2]|0;h=j+176|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[j+188>>2]=k;r=c[g>>2]|0}else r=q;k=r+28|0;if((c[k>>2]|0)<(c[r+60>>2]|0)){j=r+16|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];h=c[k>>2]|0;k=r+48|0;c[j>>2]=c[k>>2];c[j+4>>2]=c[k+4>>2];c[j+8>>2]=c[k+8>>2];c[j+12>>2]=c[k+12>>2];k=c[g>>2]|0;j=k+48|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[k+60>>2]=h;s=c[g>>2]|0}else s=r;h=s+92|0;if((c[h>>2]|0)<(c[s+124>>2]|0)){k=s+80|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];j=c[h>>2]|0;h=s+112|0;c[k>>2]=c[h>>2];c[k+4>>2]=c[h+4>>2];c[k+8>>2]=c[h+8>>2];c[k+12>>2]=c[h+12>>2];h=c[g>>2]|0;k=h+112|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[h+124>>2]=j;t=c[g>>2]|0}else t=s;j=t+156|0;if((c[j>>2]|0)<(c[t+188>>2]|0)){h=t+144|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];k=c[j>>2]|0;j=t+176|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=c[g>>2]|0;h=j+176|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[j+188>>2]=k;u=c[g>>2]|0}else u=t;k=u+12|0;if((c[k>>2]|0)<(c[u+44>>2]|0)){c[d>>2]=c[u>>2];c[d+4>>2]=c[u+4>>2];c[d+8>>2]=c[u+8>>2];j=c[k>>2]|0;k=u+32|0;c[u>>2]=c[k>>2];c[u+4>>2]=c[k+4>>2];c[u+8>>2]=c[k+8>>2];c[u+12>>2]=c[k+12>>2];k=c[g>>2]|0;h=k+32|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+44>>2]=j;v=c[g>>2]|0}else v=u;j=v+76|0;if((c[j>>2]|0)<(c[v+108>>2]|0)){k=v+64|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];h=c[j>>2]|0;j=v+96|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[g>>2]|0;k=j+96|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+108>>2]=h;w=c[g>>2]|0}else w=v;h=w+140|0;if((c[h>>2]|0)<(c[w+172>>2]|0)){j=w+128|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[h>>2]|0;h=w+160|0;c[j>>2]=c[h>>2];c[j+4>>2]=c[h+4>>2];c[j+8>>2]=c[h+8>>2];c[j+12>>2]=c[h+12>>2];h=c[g>>2]|0;j=h+160|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[h+172>>2]=k;x=c[g>>2]|0}else x=w;k=x+28|0;if((c[k>>2]|0)<(c[x+44>>2]|0)){h=x+16|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];j=c[k>>2]|0;k=x+32|0;c[h>>2]=c[k>>2];c[h+4>>2]=c[k+4>>2];c[h+8>>2]=c[k+8>>2];c[h+12>>2]=c[k+12>>2];k=c[g>>2]|0;h=k+32|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+44>>2]=j;y=c[g>>2]|0}else y=x;j=y+92|0;if((c[j>>2]|0)<(c[y+108>>2]|0)){k=y+80|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];h=c[j>>2]|0;j=y+96|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[g>>2]|0;k=j+96|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+108>>2]=h;z=c[g>>2]|0}else z=y;h=z+156|0;if((c[h>>2]|0)<(c[z+172>>2]|0)){j=z+144|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[h>>2]|0;h=z+160|0;c[j>>2]=c[h>>2];c[j+4>>2]=c[h+4>>2];c[j+8>>2]=c[h+8>>2];c[j+12>>2]=c[h+12>>2];h=c[g>>2]|0;j=h+160|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[h+172>>2]=k;A=c[g>>2]|0}else A=z;k=A+28|0;if((c[k>>2]|0)<(c[A+92>>2]|0)){h=A+16|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];j=c[k>>2]|0;k=A+80|0;c[h>>2]=c[k>>2];c[h+4>>2]=c[k+4>>2];c[h+8>>2]=c[k+8>>2];c[h+12>>2]=c[k+12>>2];k=c[g>>2]|0;h=k+80|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+92>>2]=j;B=c[g>>2]|0}else B=A;j=B+108|0;if((c[j>>2]|0)<(c[B+172>>2]|0)){k=B+96|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];h=c[j>>2]|0;j=B+160|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[g>>2]|0;k=j+160|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+172>>2]=h;C=c[g>>2]|0}else C=B;h=C+92|0;if((c[h>>2]|0)<(c[C+156>>2]|0)){j=C+80|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[h>>2]|0;h=C+144|0;c[j>>2]=c[h>>2];c[j+4>>2]=c[h+4>>2];c[j+8>>2]=c[h+8>>2];c[j+12>>2]=c[h+12>>2];h=c[g>>2]|0;j=h+144|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[h+156>>2]=k;D=c[g>>2]|0}else D=C;k=D+44|0;if((c[k>>2]|0)<(c[D+108>>2]|0)){h=D+32|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];j=c[k>>2]|0;k=D+96|0;c[h>>2]=c[k>>2];c[h+4>>2]=c[k+4>>2];c[h+8>>2]=c[k+8>>2];c[h+12>>2]=c[k+12>>2];k=c[g>>2]|0;h=k+96|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+108>>2]=j;E=c[g>>2]|0}else E=D;j=E+28|0;if((c[j>>2]|0)<(c[E+92>>2]|0)){k=E+16|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];h=c[j>>2]|0;j=E+80|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[g>>2]|0;k=j+80|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+92>>2]=h;F=c[g>>2]|0}else F=E;h=F+108|0;if((c[h>>2]|0)<(c[F+172>>2]|0)){j=F+96|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[h>>2]|0;h=F+160|0;c[j>>2]=c[h>>2];c[j+4>>2]=c[h+4>>2];c[j+8>>2]=c[h+8>>2];c[j+12>>2]=c[h+12>>2];h=c[g>>2]|0;j=h+160|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[h+172>>2]=k;G=c[g>>2]|0}else G=F;k=G+12|0;if((c[k>>2]|0)<(c[G+76>>2]|0)){c[d>>2]=c[G>>2];c[d+4>>2]=c[G+4>>2];c[d+8>>2]=c[G+8>>2];h=c[k>>2]|0;k=G+64|0;c[G>>2]=c[k>>2];c[G+4>>2]=c[k+4>>2];c[G+8>>2]=c[k+8>>2];c[G+12>>2]=c[k+12>>2];k=c[g>>2]|0;j=k+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[k+76>>2]=h;H=c[g>>2]|0}else H=G;h=H+124|0;k=c[h>>2]|0;if((k|0)<(c[H+188>>2]|0)){j=H+112|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[h>>2]|0;h=H+176|0;c[j>>2]=c[h>>2];c[j+4>>2]=c[h+4>>2];c[j+8>>2]=c[h+8>>2];c[j+12>>2]=c[h+12>>2];h=c[g>>2]|0;j=h+176|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[h+188>>2]=l;l=c[g>>2]|0;I=l;J=c[l+124>>2]|0}else{I=H;J=k}k=I+60|0;if((c[k>>2]|0)<(J|0)){l=I+48|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];h=c[k>>2]|0;k=I+112|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[g>>2]|0;l=k+112|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+124>>2]=h;K=c[g>>2]|0}else K=I;h=K+76|0;k=c[h>>2]|0;if((k|0)<(c[K+140>>2]|0)){l=K+64|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[h>>2]|0;h=K+128|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[g>>2]|0;l=h+128|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[h+140>>2]=j;j=c[g>>2]|0;L=j;M=c[j+76>>2]|0}else{L=K;M=k}k=L+12|0;if((c[k>>2]|0)<(M|0)){c[d>>2]=c[L>>2];c[d+4>>2]=c[L+4>>2];c[d+8>>2]=c[L+8>>2];j=c[k>>2]|0;k=L+64|0;c[L>>2]=c[k>>2];c[L+4>>2]=c[k+4>>2];c[L+8>>2]=c[k+8>>2];c[L+12>>2]=c[k+12>>2];k=c[g>>2]|0;h=k+64|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+76>>2]=j;N=c[g>>2]|0}else N=L;j=N+124|0;if((c[j>>2]|0)<(c[N+188>>2]|0)){k=N+112|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];h=c[j>>2]|0;j=N+176|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[g>>2]|0;k=j+176|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+188>>2]=h;O=c[g>>2]|0}else O=N;h=O+28|0;if((c[h>>2]|0)<(c[O+76>>2]|0)){j=O+16|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[h>>2]|0;h=O+64|0;c[j>>2]=c[h>>2];c[j+4>>2]=c[h+4>>2];c[j+8>>2]=c[h+8>>2];c[j+12>>2]=c[h+12>>2];h=c[g>>2]|0;j=h+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[h+76>>2]=k;P=c[g>>2]|0}else P=O;k=P+124|0;if((c[k>>2]|0)<(c[P+172>>2]|0)){h=P+112|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];j=c[k>>2]|0;k=P+160|0;c[h>>2]=c[k>>2];c[h+4>>2]=c[k+4>>2];c[h+8>>2]=c[k+8>>2];c[h+12>>2]=c[k+12>>2];k=c[g>>2]|0;h=k+160|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+172>>2]=j;Q=c[g>>2]|0}else Q=P;j=Q+60|0;k=c[j>>2]|0;if((k|0)<(c[Q+140>>2]|0)){h=Q+48|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];l=c[j>>2]|0;j=Q+128|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=c[g>>2]|0;h=j+128|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[j+140>>2]=l;l=c[g>>2]|0;R=l;S=c[l+60>>2]|0}else{R=Q;S=k}k=R+44|0;if((c[k>>2]|0)<(S|0)){l=R+32|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[k>>2]|0;k=R+48|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[g>>2]|0;l=k+48|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+60>>2]=j;T=c[g>>2]|0}else T=R;j=T+140|0;if((c[j>>2]|0)<(c[T+156>>2]|0)){k=T+128|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[j>>2]|0;j=T+144|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[g>>2]|0;k=j+144|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+156>>2]=l;U=c[g>>2]|0}else U=T;l=U+44|0;if((c[l>>2]|0)<(c[U+76>>2]|0)){j=U+32|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[l>>2]|0;l=U+64|0;c[j>>2]=c[l>>2];c[j+4>>2]=c[l+4>>2];c[j+8>>2]=c[l+8>>2];c[j+12>>2]=c[l+12>>2];l=c[g>>2]|0;j=l+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[l+76>>2]=k;V=c[g>>2]|0}else V=U;k=V+124|0;if((c[k>>2]|0)<(c[V+156>>2]|0)){l=V+112|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[k>>2]|0;k=V+144|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[g>>2]|0;l=k+144|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+156>>2]=j;W=c[g>>2]|0}else W=V;j=W+60|0;if((c[j>>2]|0)<(c[W+92>>2]|0)){k=W+48|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[j>>2]|0;j=W+80|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[g>>2]|0;k=j+80|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+92>>2]=l;X=c[g>>2]|0}else X=W;l=X+108|0;if((c[l>>2]|0)<(c[X+140>>2]|0)){j=X+96|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[l>>2]|0;l=X+128|0;c[j>>2]=c[l>>2];c[j+4>>2]=c[l+4>>2];c[j+8>>2]=c[l+8>>2];c[j+12>>2]=c[l+12>>2];l=c[g>>2]|0;j=l+128|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[l+140>>2]=k;Y=c[g>>2]|0}else Y=X;k=Y+60|0;if((c[k>>2]|0)<(c[Y+76>>2]|0)){l=Y+48|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[k>>2]|0;k=Y+64|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[g>>2]|0;l=k+64|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+76>>2]=j;Z=c[g>>2]|0}else Z=Y;j=Z+92|0;if((c[j>>2]|0)<(c[Z+108>>2]|0)){k=Z+80|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[j>>2]|0;j=Z+96|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[g>>2]|0;k=j+96|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+108>>2]=l;_=c[g>>2]|0}else _=Z;l=_+124|0;if((c[l>>2]|0)>=(c[_+140>>2]|0)){i=b;return}j=_+112|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[l>>2]|0;l=_+128|0;c[j>>2]=c[l>>2];c[j+4>>2]=c[l+4>>2];c[j+8>>2]=c[l+8>>2];c[j+12>>2]=c[l+12>>2];l=c[g>>2]|0;g=l+128|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+140>>2]=k;i=b;return}case 11:{k=a+14964|0;l=c[k>>2]|0;g=l+12|0;if((c[g>>2]|0)<(c[l+28>>2]|0)){c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[g>>2]|0;g=l+16|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[k>>2]|0;h=g+16|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[g+28>>2]=j;$=c[k>>2]|0}else $=l;l=$+44|0;if((c[l>>2]|0)<(c[$+60>>2]|0)){j=$+32|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];g=c[l>>2]|0;l=$+48|0;c[j>>2]=c[l>>2];c[j+4>>2]=c[l+4>>2];c[j+8>>2]=c[l+8>>2];c[j+12>>2]=c[l+12>>2];l=c[k>>2]|0;j=l+48|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[l+60>>2]=g;aa=c[k>>2]|0}else aa=$;g=aa+76|0;if((c[g>>2]|0)<(c[aa+92>>2]|0)){l=aa+64|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[g>>2]|0;g=aa+80|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[k>>2]|0;l=g+80|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+92>>2]=j;ba=c[k>>2]|0}else ba=aa;j=ba+108|0;if((c[j>>2]|0)<(c[ba+124>>2]|0)){g=ba+96|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[j>>2]|0;j=ba+112|0;c[g>>2]=c[j>>2];c[g+4>>2]=c[j+4>>2];c[g+8>>2]=c[j+8>>2];c[g+12>>2]=c[j+12>>2];j=c[k>>2]|0;g=j+112|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[j+124>>2]=l;ca=c[k>>2]|0}else ca=ba;l=ca+140|0;if((c[l>>2]|0)<(c[ca+156>>2]|0)){j=ca+128|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];g=c[l>>2]|0;l=ca+144|0;c[j>>2]=c[l>>2];c[j+4>>2]=c[l+4>>2];c[j+8>>2]=c[l+8>>2];c[j+12>>2]=c[l+12>>2];l=c[k>>2]|0;j=l+144|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[l+156>>2]=g;da=c[k>>2]|0}else da=ca;g=da+28|0;if((c[g>>2]|0)<(c[da+60>>2]|0)){l=da+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[g>>2]|0;g=da+48|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[k>>2]|0;l=g+48|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+60>>2]=j;ea=c[k>>2]|0}else ea=da;j=ea+92|0;if((c[j>>2]|0)<(c[ea+124>>2]|0)){g=ea+80|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[j>>2]|0;j=ea+112|0;c[g>>2]=c[j>>2];c[g+4>>2]=c[j+4>>2];c[g+8>>2]=c[j+8>>2];c[g+12>>2]=c[j+12>>2];j=c[k>>2]|0;g=j+112|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[j+124>>2]=l;fa=c[k>>2]|0}else fa=ea;l=fa+12|0;if((c[l>>2]|0)<(c[fa+44>>2]|0)){c[d>>2]=c[fa>>2];c[d+4>>2]=c[fa+4>>2];c[d+8>>2]=c[fa+8>>2];j=c[l>>2]|0;l=fa+32|0;c[fa>>2]=c[l>>2];c[fa+4>>2]=c[l+4>>2];c[fa+8>>2]=c[l+8>>2];c[fa+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+44>>2]=j;ga=c[k>>2]|0}else ga=fa;j=ga+76|0;if((c[j>>2]|0)<(c[ga+108>>2]|0)){l=ga+64|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=ga+96|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[k>>2]|0;l=j+96|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+108>>2]=g;ha=c[k>>2]|0}else ha=ga;g=ha+140|0;if((c[g>>2]|0)<(c[ha+172>>2]|0)){j=ha+128|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=ha+160|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+160|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+172>>2]=l;ia=c[k>>2]|0}else ia=ha;l=ia+28|0;if((c[l>>2]|0)<(c[ia+44>>2]|0)){g=ia+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[l>>2]|0;l=ia+32|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+44>>2]=j;ja=c[k>>2]|0}else ja=ia;j=ja+92|0;if((c[j>>2]|0)<(c[ja+108>>2]|0)){l=ja+80|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=ja+96|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[k>>2]|0;l=j+96|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+108>>2]=g;ka=c[k>>2]|0}else ka=ja;g=ka+156|0;if((c[g>>2]|0)<(c[ka+172>>2]|0)){j=ka+144|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=ka+160|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+160|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+172>>2]=l;la=c[k>>2]|0}else la=ka;l=la+28|0;if((c[l>>2]|0)<(c[la+92>>2]|0)){g=la+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[l>>2]|0;l=la+80|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+80|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+92>>2]=j;ma=c[k>>2]|0}else ma=la;j=ma+108|0;if((c[j>>2]|0)<(c[ma+172>>2]|0)){l=ma+96|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=ma+160|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[k>>2]|0;l=j+160|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+172>>2]=g;na=c[k>>2]|0}else na=ma;g=na+92|0;if((c[g>>2]|0)<(c[na+156>>2]|0)){j=na+80|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=na+144|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+144|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+156>>2]=l;oa=c[k>>2]|0}else oa=na;l=oa+44|0;if((c[l>>2]|0)<(c[oa+108>>2]|0)){g=oa+32|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[l>>2]|0;l=oa+96|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+96|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+108>>2]=j;pa=c[k>>2]|0}else pa=oa;j=pa+28|0;if((c[j>>2]|0)<(c[pa+92>>2]|0)){l=pa+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=pa+80|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[k>>2]|0;l=j+80|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+92>>2]=g;qa=c[k>>2]|0}else qa=pa;g=qa+108|0;if((c[g>>2]|0)<(c[qa+172>>2]|0)){j=qa+96|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=qa+160|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+160|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+172>>2]=l;ra=c[k>>2]|0}else ra=qa;l=ra+12|0;if((c[l>>2]|0)<(c[ra+76>>2]|0)){c[d>>2]=c[ra>>2];c[d+4>>2]=c[ra+4>>2];c[d+8>>2]=c[ra+8>>2];g=c[l>>2]|0;l=ra+64|0;c[ra>>2]=c[l>>2];c[ra+4>>2]=c[l+4>>2];c[ra+8>>2]=c[l+8>>2];c[ra+12>>2]=c[l+12>>2];l=c[k>>2]|0;j=l+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[l+76>>2]=g;sa=c[k>>2]|0}else sa=ra;g=sa+60|0;if((c[g>>2]|0)<(c[sa+124>>2]|0)){l=sa+48|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[g>>2]|0;g=sa+112|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[k>>2]|0;l=g+112|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+124>>2]=j;ta=c[k>>2]|0}else ta=sa;j=ta+76|0;g=c[j>>2]|0;if((g|0)<(c[ta+140>>2]|0)){l=ta+64|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];h=c[j>>2]|0;j=ta+128|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[k>>2]|0;l=j+128|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+140>>2]=h;h=c[k>>2]|0;ua=h;va=c[h+76>>2]|0}else{ua=ta;va=g}g=ua+12|0;if((c[g>>2]|0)<(va|0)){c[d>>2]=c[ua>>2];c[d+4>>2]=c[ua+4>>2];c[d+8>>2]=c[ua+8>>2];h=c[g>>2]|0;g=ua+64|0;c[ua>>2]=c[g>>2];c[ua+4>>2]=c[g+4>>2];c[ua+8>>2]=c[g+8>>2];c[ua+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+76>>2]=h;h=c[k>>2]|0;wa=h;xa=c[h+76>>2]|0}else{wa=ua;xa=va}h=wa+28|0;if((c[h>>2]|0)<(xa|0)){g=wa+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[h>>2]|0;h=wa+64|0;c[g>>2]=c[h>>2];c[g+4>>2]=c[h+4>>2];c[g+8>>2]=c[h+8>>2];c[g+12>>2]=c[h+12>>2];h=c[k>>2]|0;g=h+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[h+76>>2]=j;ya=c[k>>2]|0}else ya=wa;j=ya+124|0;if((c[j>>2]|0)<(c[ya+172>>2]|0)){h=ya+112|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];g=c[j>>2]|0;j=ya+160|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=c[k>>2]|0;h=j+160|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[j+172>>2]=g;za=c[k>>2]|0}else za=ya;g=za+60|0;j=c[g>>2]|0;if((j|0)<(c[za+140>>2]|0)){h=za+48|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];l=c[g>>2]|0;g=za+128|0;c[h>>2]=c[g>>2];c[h+4>>2]=c[g+4>>2];c[h+8>>2]=c[g+8>>2];c[h+12>>2]=c[g+12>>2];g=c[k>>2]|0;h=g+128|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[g+140>>2]=l;l=c[k>>2]|0;Aa=l;Ba=c[l+60>>2]|0}else{Aa=za;Ba=j}j=Aa+44|0;if((c[j>>2]|0)<(Ba|0)){l=Aa+32|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=Aa+48|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[k>>2]|0;l=j+48|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+60>>2]=g;Ca=c[k>>2]|0}else Ca=Aa;g=Ca+140|0;if((c[g>>2]|0)<(c[Ca+156>>2]|0)){j=Ca+128|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=Ca+144|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+144|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+156>>2]=l;Da=c[k>>2]|0}else Da=Ca;l=Da+44|0;if((c[l>>2]|0)<(c[Da+76>>2]|0)){g=Da+32|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[l>>2]|0;l=Da+64|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+76>>2]=j;Ea=c[k>>2]|0}else Ea=Da;j=Ea+124|0;if((c[j>>2]|0)<(c[Ea+156>>2]|0)){l=Ea+112|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=Ea+144|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[k>>2]|0;l=j+144|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+156>>2]=g;Fa=c[k>>2]|0}else Fa=Ea;g=Fa+60|0;if((c[g>>2]|0)<(c[Fa+92>>2]|0)){j=Fa+48|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=Fa+80|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+80|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+92>>2]=l;Ga=c[k>>2]|0}else Ga=Fa;l=Ga+108|0;if((c[l>>2]|0)<(c[Ga+140>>2]|0)){g=Ga+96|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[l>>2]|0;l=Ga+128|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+128|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+140>>2]=j;Ha=c[k>>2]|0}else Ha=Ga;j=Ha+60|0;if((c[j>>2]|0)<(c[Ha+76>>2]|0)){l=Ha+48|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=Ha+64|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[k>>2]|0;l=j+64|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+76>>2]=g;Ia=c[k>>2]|0}else Ia=Ha;g=Ia+92|0;if((c[g>>2]|0)<(c[Ia+108>>2]|0)){j=Ia+80|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=Ia+96|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+96|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+108>>2]=l;Ja=c[k>>2]|0}else Ja=Ia;l=Ja+124|0;if((c[l>>2]|0)>=(c[Ja+140>>2]|0)){i=b;return}g=Ja+112|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[l>>2]|0;l=Ja+128|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;k=l+128|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+140>>2]=j;i=b;return}case 10:{j=a+14964|0;l=c[j>>2]|0;k=l+28|0;if((c[k>>2]|0)<(c[l+140>>2]|0)){g=l+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];h=c[k>>2]|0;k=l+128|0;c[g>>2]=c[k>>2];c[g+4>>2]=c[k+4>>2];c[g+8>>2]=c[k+8>>2];c[g+12>>2]=c[k+12>>2];k=c[j>>2]|0;g=k+128|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[k+140>>2]=h;Ka=c[j>>2]|0}else Ka=l;l=Ka+12|0;if((c[l>>2]|0)<(c[Ka+76>>2]|0)){c[d>>2]=c[Ka>>2];c[d+4>>2]=c[Ka+4>>2];c[d+8>>2]=c[Ka+8>>2];h=c[l>>2]|0;l=Ka+64|0;c[Ka>>2]=c[l>>2];c[Ka+4>>2]=c[l+4>>2];c[Ka+8>>2]=c[l+8>>2];c[Ka+12>>2]=c[l+12>>2];l=c[j>>2]|0;k=l+64|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+76>>2]=h;La=c[j>>2]|0}else La=Ka;h=La+92|0;if((c[h>>2]|0)<(c[La+156>>2]|0)){l=La+80|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];k=c[h>>2]|0;h=La+144|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[j>>2]|0;l=h+144|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[h+156>>2]=k;Ma=c[j>>2]|0}else Ma=La;k=Ma+44|0;if((c[k>>2]|0)<(c[Ma+108>>2]|0)){h=Ma+32|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];l=c[k>>2]|0;k=Ma+96|0;c[h>>2]=c[k>>2];c[h+4>>2]=c[k+4>>2];c[h+8>>2]=c[k+8>>2];c[h+12>>2]=c[k+12>>2];k=c[j>>2]|0;h=k+96|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+108>>2]=l;Na=c[j>>2]|0}else Na=Ma;l=Na+60|0;k=c[l>>2]|0;if((k|0)<(c[Na+124>>2]|0)){h=Na+48|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];g=c[l>>2]|0;l=Na+112|0;c[h>>2]=c[l>>2];c[h+4>>2]=c[l+4>>2];c[h+8>>2]=c[l+8>>2];c[h+12>>2]=c[l+12>>2];l=c[j>>2]|0;h=l+112|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[l+124>>2]=g;g=c[j>>2]|0;Oa=g;Pa=c[g+60>>2]|0}else{Oa=Na;Pa=k}k=Oa+12|0;if((c[k>>2]|0)<(Pa|0)){c[d>>2]=c[Oa>>2];c[d+4>>2]=c[Oa+4>>2];c[d+8>>2]=c[Oa+8>>2];g=c[k>>2]|0;k=Oa+48|0;c[Oa>>2]=c[k>>2];c[Oa+4>>2]=c[k+4>>2];c[Oa+8>>2]=c[k+8>>2];c[Oa+12>>2]=c[k+12>>2];k=c[j>>2]|0;l=k+48|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+60>>2]=g;Qa=c[j>>2]|0}else Qa=Oa;g=Qa+108|0;if((c[g>>2]|0)<(c[Qa+156>>2]|0)){k=Qa+96|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[g>>2]|0;g=Qa+144|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[j>>2]|0;k=g+144|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+156>>2]=l;Ra=c[j>>2]|0}else Ra=Qa;l=Ra+44|0;if((c[l>>2]|0)<(c[Ra+92>>2]|0)){g=Ra+32|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];k=c[l>>2]|0;l=Ra+80|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[j>>2]|0;g=l+80|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+92>>2]=k;Sa=c[j>>2]|0}else Sa=Ra;k=Sa+12|0;if((c[k>>2]|0)<(c[Sa+28>>2]|0)){c[d>>2]=c[Sa>>2];c[d+4>>2]=c[Sa+4>>2];c[d+8>>2]=c[Sa+8>>2];l=c[k>>2]|0;k=Sa+16|0;c[Sa>>2]=c[k>>2];c[Sa+4>>2]=c[k+4>>2];c[Sa+8>>2]=c[k+8>>2];c[Sa+12>>2]=c[k+12>>2];k=c[j>>2]|0;g=k+16|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[k+28>>2]=l;Ta=c[j>>2]|0}else Ta=Sa;l=Ta+60|0;if((c[l>>2]|0)<(c[Ta+108>>2]|0)){k=Ta+48|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];g=c[l>>2]|0;l=Ta+96|0;c[k>>2]=c[l>>2];c[k+4>>2]=c[l+4>>2];c[k+8>>2]=c[l+8>>2];c[k+12>>2]=c[l+12>>2];l=c[j>>2]|0;k=l+96|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+108>>2]=g;Ua=c[j>>2]|0}else Ua=Ta;g=Ua+140|0;if((c[g>>2]|0)<(c[Ua+156>>2]|0)){l=Ua+128|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];k=c[g>>2]|0;g=Ua+144|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[j>>2]|0;l=g+144|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+156>>2]=k;Va=c[j>>2]|0}else Va=Ua;k=Va+76|0;if((c[k>>2]|0)<(c[Va+124>>2]|0)){g=Va+64|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[k>>2]|0;k=Va+112|0;c[g>>2]=c[k>>2];c[g+4>>2]=c[k+4>>2];c[g+8>>2]=c[k+8>>2];c[g+12>>2]=c[k+12>>2];k=c[j>>2]|0;g=k+112|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[k+124>>2]=l;Wa=c[j>>2]|0}else Wa=Va;l=Wa+12|0;if((c[l>>2]|0)<(c[Wa+44>>2]|0)){c[d>>2]=c[Wa>>2];c[d+4>>2]=c[Wa+4>>2];c[d+8>>2]=c[Wa+8>>2];k=c[l>>2]|0;l=Wa+32|0;c[Wa>>2]=c[l>>2];c[Wa+4>>2]=c[l+4>>2];c[Wa+8>>2]=c[l+8>>2];c[Wa+12>>2]=c[l+12>>2];l=c[j>>2]|0;g=l+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+44>>2]=k;Xa=c[j>>2]|0}else Xa=Wa;k=Xa+76|0;if((c[k>>2]|0)<(c[Xa+140>>2]|0)){l=Xa+64|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[k>>2]|0;k=Xa+128|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[j>>2]|0;l=k+128|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+140>>2]=g;Ya=c[j>>2]|0}else Ya=Xa;g=Ya+28|0;if((c[g>>2]|0)<(c[Ya+92>>2]|0)){k=Ya+16|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[g>>2]|0;g=Ya+80|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[j>>2]|0;k=g+80|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+92>>2]=l;Za=c[j>>2]|0}else Za=Ya;l=Za+124|0;if((c[l>>2]|0)<(c[Za+156>>2]|0)){g=Za+112|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];k=c[l>>2]|0;l=Za+144|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[j>>2]|0;g=l+144|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+156>>2]=k;_a=c[j>>2]|0}else _a=Za;k=_a+28|0;if((c[k>>2]|0)<(c[_a+44>>2]|0)){l=_a+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[k>>2]|0;k=_a+32|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[j>>2]|0;l=k+32|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+44>>2]=g;$a=c[j>>2]|0}else $a=_a;g=$a+60|0;if((c[g>>2]|0)<(c[$a+76>>2]|0)){k=$a+48|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[g>>2]|0;g=$a+64|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[j>>2]|0;k=g+64|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+76>>2]=l;ab=c[j>>2]|0}else ab=$a;l=ab+92|0;if((c[l>>2]|0)<(c[ab+108>>2]|0)){g=ab+80|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];k=c[l>>2]|0;l=ab+96|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[j>>2]|0;g=l+96|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+108>>2]=k;bb=c[j>>2]|0}else bb=ab;k=bb+124|0;if((c[k>>2]|0)<(c[bb+140>>2]|0)){l=bb+112|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[k>>2]|0;k=bb+128|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[j>>2]|0;l=k+128|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+140>>2]=g;cb=c[j>>2]|0}else cb=bb;g=cb+28|0;if((c[g>>2]|0)<(c[cb+60>>2]|0)){k=cb+16|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[g>>2]|0;g=cb+48|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[j>>2]|0;k=g+48|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+60>>2]=l;db=c[j>>2]|0}else db=cb;l=db+108|0;if((c[l>>2]|0)<(c[db+140>>2]|0)){g=db+96|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];k=c[l>>2]|0;l=db+128|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[j>>2]|0;g=l+128|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+140>>2]=k;eb=c[j>>2]|0}else eb=db;k=eb+44|0;if((c[k>>2]|0)<(c[eb+76>>2]|0)){l=eb+32|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[k>>2]|0;k=eb+64|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[j>>2]|0;l=k+64|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+76>>2]=g;fb=c[j>>2]|0}else fb=eb;g=fb+92|0;if((c[g>>2]|0)<(c[fb+124>>2]|0)){k=fb+80|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[g>>2]|0;g=fb+112|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[j>>2]|0;k=g+112|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+124>>2]=l;gb=c[j>>2]|0}else gb=fb;l=gb+44|0;if((c[l>>2]|0)<(c[gb+60>>2]|0)){g=gb+32|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];k=c[l>>2]|0;l=gb+48|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[j>>2]|0;g=l+48|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+60>>2]=k;hb=c[j>>2]|0}else hb=gb;k=hb+108|0;if((c[k>>2]|0)<(c[hb+124>>2]|0)){l=hb+96|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[k>>2]|0;k=hb+112|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[j>>2]|0;l=k+112|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+124>>2]=g;ib=c[j>>2]|0}else ib=hb;g=ib+60|0;if((c[g>>2]|0)<(c[ib+92>>2]|0)){k=ib+48|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[g>>2]|0;g=ib+80|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[j>>2]|0;k=g+80|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+92>>2]=l;jb=c[j>>2]|0}else jb=ib;l=jb+76|0;g=c[l>>2]|0;if((g|0)<(c[jb+108>>2]|0)){k=jb+64|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];h=c[l>>2]|0;l=jb+96|0;c[k>>2]=c[l>>2];c[k+4>>2]=c[l+4>>2];c[k+8>>2]=c[l+8>>2];c[k+12>>2]=c[l+12>>2];l=c[j>>2]|0;k=l+96|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+108>>2]=h;h=c[j>>2]|0;kb=h;lb=c[h+76>>2]|0}else{kb=jb;lb=g}if((lb|0)>=(c[kb+92>>2]|0)){i=b;return}g=kb+64|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];h=c[kb+76>>2]|0;l=kb+80|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[j>>2]|0;j=l+80|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[l+92>>2]=h;i=b;return}case 9:{h=a+14964|0;l=c[h>>2]|0;j=l+12|0;if((c[j>>2]|0)<(c[l+28>>2]|0)){c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=l+16|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[h>>2]|0;k=j+16|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+28>>2]=g;mb=c[h>>2]|0}else mb=l;l=mb+60|0;if((c[l>>2]|0)<(c[mb+76>>2]|0)){g=mb+48|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[l>>2]|0;l=mb+64|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[h>>2]|0;g=l+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+76>>2]=j;nb=c[h>>2]|0}else nb=mb;j=nb+108|0;if((c[j>>2]|0)<(c[nb+124>>2]|0)){l=nb+96|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=nb+112|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[h>>2]|0;l=j+112|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+124>>2]=g;ob=c[h>>2]|0}else ob=nb;g=ob+28|0;if((c[g>>2]|0)<(c[ob+44>>2]|0)){j=ob+16|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=ob+32|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[h>>2]|0;j=g+32|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+44>>2]=l;pb=c[h>>2]|0}else pb=ob;l=pb+76|0;if((c[l>>2]|0)<(c[pb+92>>2]|0)){g=pb+64|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[l>>2]|0;l=pb+80|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[h>>2]|0;g=l+80|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+92>>2]=j;qb=c[h>>2]|0}else qb=pb;j=qb+124|0;if((c[j>>2]|0)<(c[qb+140>>2]|0)){l=qb+112|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[j>>2]|0;j=qb+128|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[h>>2]|0;l=j+128|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+140>>2]=g;rb=c[h>>2]|0}else rb=qb;g=rb+12|0;if((c[g>>2]|0)<(c[rb+28>>2]|0)){c[d>>2]=c[rb>>2];c[d+4>>2]=c[rb+4>>2];c[d+8>>2]=c[rb+8>>2];j=c[g>>2]|0;g=rb+16|0;c[rb>>2]=c[g>>2];c[rb+4>>2]=c[g+4>>2];c[rb+8>>2]=c[g+8>>2];c[rb+12>>2]=c[g+12>>2];g=c[h>>2]|0;l=g+16|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+28>>2]=j;sb=c[h>>2]|0}else sb=rb;j=sb+60|0;if((c[j>>2]|0)<(c[sb+76>>2]|0)){g=sb+48|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[j>>2]|0;j=sb+64|0;c[g>>2]=c[j>>2];c[g+4>>2]=c[j+4>>2];c[g+8>>2]=c[j+8>>2];c[g+12>>2]=c[j+12>>2];j=c[h>>2]|0;g=j+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[j+76>>2]=l;tb=c[h>>2]|0}else tb=sb;l=tb+108|0;if((c[l>>2]|0)<(c[tb+124>>2]|0)){j=tb+96|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];g=c[l>>2]|0;l=tb+112|0;c[j>>2]=c[l>>2];c[j+4>>2]=c[l+4>>2];c[j+8>>2]=c[l+8>>2];c[j+12>>2]=c[l+12>>2];l=c[h>>2]|0;j=l+112|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[l+124>>2]=g;ub=c[h>>2]|0}else ub=tb;g=ub+12|0;l=c[ub+60>>2]|0;if((c[g>>2]|0)<(l|0)){c[d>>2]=c[ub>>2];c[d+4>>2]=c[ub+4>>2];c[d+8>>2]=c[ub+8>>2];j=c[g>>2]|0;g=ub+48|0;c[ub>>2]=c[g>>2];c[ub+4>>2]=c[g+4>>2];c[ub+8>>2]=c[g+8>>2];c[ub+12>>2]=c[g+12>>2];g=c[h>>2]|0;k=g+48|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+60>>2]=j;j=c[h>>2]|0;vb=j;wb=c[j+60>>2]|0}else{vb=ub;wb=l}if((wb|0)<(c[vb+108>>2]|0)){l=vb+48|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[vb+60>>2]|0;g=vb+96|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[h>>2]|0;l=g+96|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+108>>2]=j;j=c[h>>2]|0;xb=j;yb=c[j+60>>2]|0}else{xb=vb;yb=wb}j=xb+12|0;if((c[j>>2]|0)<(yb|0)){c[d>>2]=c[xb>>2];c[d+4>>2]=c[xb+4>>2];c[d+8>>2]=c[xb+8>>2];g=c[j>>2]|0;j=xb+48|0;c[xb>>2]=c[j>>2];c[xb+4>>2]=c[j+4>>2];c[xb+8>>2]=c[j+8>>2];c[xb+12>>2]=c[j+12>>2];j=c[h>>2]|0;l=j+48|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+60>>2]=g;zb=c[h>>2]|0}else zb=xb;g=zb+28|0;j=c[zb+76>>2]|0;if((c[g>>2]|0)<(j|0)){l=zb+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];k=c[g>>2]|0;g=zb+64|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[h>>2]|0;l=g+64|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+76>>2]=k;k=c[h>>2]|0;Ab=k;Bb=c[k+76>>2]|0}else{Ab=zb;Bb=j}if((Bb|0)<(c[Ab+124>>2]|0)){j=Ab+64|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[Ab+76>>2]|0;g=Ab+112|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[h>>2]|0;j=g+112|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+124>>2]=k;k=c[h>>2]|0;Cb=k;Db=c[k+76>>2]|0}else{Cb=Ab;Db=Bb}k=Cb+28|0;if((c[k>>2]|0)<(Db|0)){g=Cb+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[k>>2]|0;k=Cb+64|0;c[g>>2]=c[k>>2];c[g+4>>2]=c[k+4>>2];c[g+8>>2]=c[k+8>>2];c[g+12>>2]=c[k+12>>2];k=c[h>>2]|0;g=k+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[k+76>>2]=j;Eb=c[h>>2]|0}else Eb=Cb;j=Eb+44|0;k=c[Eb+92>>2]|0;if((c[j>>2]|0)<(k|0)){g=Eb+32|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[j>>2]|0;j=Eb+80|0;c[g>>2]=c[j>>2];c[g+4>>2]=c[j+4>>2];c[g+8>>2]=c[j+8>>2];c[g+12>>2]=c[j+12>>2];j=c[h>>2]|0;g=j+80|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[j+92>>2]=l;l=c[h>>2]|0;Fb=l;Gb=c[l+92>>2]|0}else{Fb=Eb;Gb=k}if((Gb|0)<(c[Fb+140>>2]|0)){k=Fb+80|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[Fb+92>>2]|0;j=Fb+128|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[h>>2]|0;k=j+128|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+140>>2]=l;l=c[h>>2]|0;Hb=l;Ib=c[l+92>>2]|0}else{Hb=Fb;Ib=Gb}l=Hb+44|0;if((c[l>>2]|0)<(Ib|0)){j=Hb+32|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];k=c[l>>2]|0;l=Hb+80|0;c[j>>2]=c[l>>2];c[j+4>>2]=c[l+4>>2];c[j+8>>2]=c[l+8>>2];c[j+12>>2]=c[l+12>>2];l=c[h>>2]|0;j=l+80|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[l+92>>2]=k;Jb=c[h>>2]|0}else Jb=Hb;k=Jb+28|0;if((c[k>>2]|0)<(c[Jb+60>>2]|0)){l=Jb+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[k>>2]|0;k=Jb+48|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[h>>2]|0;l=k+48|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[k+60>>2]=j;Kb=c[h>>2]|0}else Kb=Jb;j=Kb+92|0;if((c[j>>2]|0)<(c[Kb+124>>2]|0)){k=Kb+80|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[j>>2]|0;j=Kb+112|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[h>>2]|0;k=j+112|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+124>>2]=l;Lb=c[h>>2]|0}else Lb=Kb;l=Lb+44|0;j=c[Lb+108>>2]|0;if((c[l>>2]|0)<(j|0)){k=Lb+32|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];g=c[l>>2]|0;l=Lb+96|0;c[k>>2]=c[l>>2];c[k+4>>2]=c[l+4>>2];c[k+8>>2]=c[l+8>>2];c[k+12>>2]=c[l+12>>2];l=c[h>>2]|0;k=l+96|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+108>>2]=g;g=c[h>>2]|0;Mb=g;Nb=c[g+108>>2]|0}else{Mb=Lb;Nb=j}j=Mb+76|0;g=c[j>>2]|0;if((g|0)<(Nb|0)){l=Mb+64|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];k=c[j>>2]|0;j=Mb+96|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[h>>2]|0;l=j+96|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[j+108>>2]=k;k=c[h>>2]|0;Ob=k;Pb=c[k+76>>2]|0}else{Ob=Mb;Pb=g}g=Ob+44|0;k=c[g>>2]|0;if((k|0)<(Pb|0)){j=Ob+32|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];l=c[g>>2]|0;g=Ob+64|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[h>>2]|0;j=g+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+76>>2]=l;l=c[h>>2]|0;Qb=l;Rb=c[l+44>>2]|0}else{Qb=Ob;Rb=k}if((Rb|0)<(c[Qb+60>>2]|0)){k=Qb+32|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];l=c[Qb+44>>2]|0;g=Qb+48|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[h>>2]|0;k=g+48|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+60>>2]=l;Sb=c[h>>2]|0}else Sb=Qb;l=Sb+92|0;if((c[l>>2]|0)>=(c[Sb+108>>2]|0)){i=b;return}g=Sb+80|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];k=c[l>>2]|0;l=Sb+96|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[h>>2]|0;h=l+96|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[l+108>>2]=k;i=b;return}case 8:{k=a+14964|0;l=c[k>>2]|0;h=l+12|0;if((c[h>>2]|0)<(c[l+28>>2]|0)){c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[h>>2]|0;h=l+16|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[k>>2]|0;j=h+16|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[h+28>>2]=g;Tb=c[k>>2]|0}else Tb=l;l=Tb+44|0;if((c[l>>2]|0)<(c[Tb+60>>2]|0)){g=Tb+32|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];h=c[l>>2]|0;l=Tb+48|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+48|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+60>>2]=h;Ub=c[k>>2]|0}else Ub=Tb;h=Ub+76|0;if((c[h>>2]|0)<(c[Ub+92>>2]|0)){l=Ub+64|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[h>>2]|0;h=Ub+80|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[k>>2]|0;l=h+80|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[h+92>>2]=g;Vb=c[k>>2]|0}else Vb=Ub;g=Vb+108|0;if((c[g>>2]|0)<(c[Vb+124>>2]|0)){h=Vb+96|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];l=c[g>>2]|0;g=Vb+112|0;c[h>>2]=c[g>>2];c[h+4>>2]=c[g+4>>2];c[h+8>>2]=c[g+8>>2];c[h+12>>2]=c[g+12>>2];g=c[k>>2]|0;h=g+112|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[g+124>>2]=l;Wb=c[k>>2]|0}else Wb=Vb;l=Wb+12|0;if((c[l>>2]|0)<(c[Wb+44>>2]|0)){c[d>>2]=c[Wb>>2];c[d+4>>2]=c[Wb+4>>2];c[d+8>>2]=c[Wb+8>>2];g=c[l>>2]|0;l=Wb+32|0;c[Wb>>2]=c[l>>2];c[Wb+4>>2]=c[l+4>>2];c[Wb+8>>2]=c[l+8>>2];c[Wb+12>>2]=c[l+12>>2];l=c[k>>2]|0;h=l+32|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[l+44>>2]=g;Xb=c[k>>2]|0}else Xb=Wb;g=Xb+76|0;if((c[g>>2]|0)<(c[Xb+108>>2]|0)){l=Xb+64|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];h=c[g>>2]|0;g=Xb+96|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[k>>2]|0;l=g+96|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+108>>2]=h;Yb=c[k>>2]|0}else Yb=Xb;h=Yb+28|0;if((c[h>>2]|0)<(c[Yb+60>>2]|0)){g=Yb+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[h>>2]|0;h=Yb+48|0;c[g>>2]=c[h>>2];c[g+4>>2]=c[h+4>>2];c[g+8>>2]=c[h+8>>2];c[g+12>>2]=c[h+12>>2];h=c[k>>2]|0;g=h+48|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[h+60>>2]=l;Zb=c[k>>2]|0}else Zb=Yb;l=Zb+92|0;if((c[l>>2]|0)<(c[Zb+124>>2]|0)){h=Zb+80|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];g=c[l>>2]|0;l=Zb+112|0;c[h>>2]=c[l>>2];c[h+4>>2]=c[l+4>>2];c[h+8>>2]=c[l+8>>2];c[h+12>>2]=c[l+12>>2];l=c[k>>2]|0;h=l+112|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[l+124>>2]=g;_b=c[k>>2]|0}else _b=Zb;g=_b+28|0;if((c[g>>2]|0)<(c[_b+44>>2]|0)){l=_b+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];h=c[g>>2]|0;g=_b+32|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[k>>2]|0;l=g+32|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+44>>2]=h;$b=c[k>>2]|0}else $b=_b;h=$b+92|0;if((c[h>>2]|0)<(c[$b+108>>2]|0)){g=$b+80|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[h>>2]|0;h=$b+96|0;c[g>>2]=c[h>>2];c[g+4>>2]=c[h+4>>2];c[g+8>>2]=c[h+8>>2];c[g+12>>2]=c[h+12>>2];h=c[k>>2]|0;g=h+96|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[h+108>>2]=l;ac=c[k>>2]|0}else ac=$b;l=ac+12|0;if((c[l>>2]|0)<(c[ac+76>>2]|0)){c[d>>2]=c[ac>>2];c[d+4>>2]=c[ac+4>>2];c[d+8>>2]=c[ac+8>>2];h=c[l>>2]|0;l=ac+64|0;c[ac>>2]=c[l>>2];c[ac+4>>2]=c[l+4>>2];c[ac+8>>2]=c[l+8>>2];c[ac+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+76>>2]=h;bc=c[k>>2]|0}else bc=ac;h=bc+28|0;if((c[h>>2]|0)<(c[bc+92>>2]|0)){l=bc+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[h>>2]|0;h=bc+80|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[k>>2]|0;l=h+80|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[h+92>>2]=g;cc=c[k>>2]|0}else cc=bc;g=cc+44|0;if((c[g>>2]|0)<(c[cc+108>>2]|0)){h=cc+32|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];l=c[g>>2]|0;g=cc+96|0;c[h>>2]=c[g>>2];c[h+4>>2]=c[g+4>>2];c[h+8>>2]=c[g+8>>2];c[h+12>>2]=c[g+12>>2];g=c[k>>2]|0;h=g+96|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[g+108>>2]=l;dc=c[k>>2]|0}else dc=cc;l=dc+60|0;if((c[l>>2]|0)<(c[dc+124>>2]|0)){g=dc+48|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];h=c[l>>2]|0;l=dc+112|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+112|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+124>>2]=h;ec=c[k>>2]|0}else ec=dc;h=ec+44|0;if((c[h>>2]|0)<(c[ec+76>>2]|0)){l=ec+32|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[h>>2]|0;h=ec+64|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[k>>2]|0;l=h+64|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[h+76>>2]=g;fc=c[k>>2]|0}else fc=ec;g=fc+60|0;if((c[g>>2]|0)<(c[fc+92>>2]|0)){h=fc+48|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];l=c[g>>2]|0;g=fc+80|0;c[h>>2]=c[g>>2];c[h+4>>2]=c[g+4>>2];c[h+8>>2]=c[g+8>>2];c[h+12>>2]=c[g+12>>2];g=c[k>>2]|0;h=g+80|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[g+92>>2]=l;gc=c[k>>2]|0}else gc=fc;l=gc+28|0;if((c[l>>2]|0)<(c[gc+44>>2]|0)){g=gc+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];h=c[l>>2]|0;l=gc+32|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+44>>2]=h;hc=c[k>>2]|0}else hc=gc;h=hc+60|0;if((c[h>>2]|0)<(c[hc+76>>2]|0)){l=hc+48|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[h>>2]|0;h=hc+64|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[k>>2]|0;l=h+64|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[h+76>>2]=g;ic=c[k>>2]|0}else ic=hc;g=ic+92|0;if((c[g>>2]|0)>=(c[ic+108>>2]|0)){i=b;return}h=ic+80|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];l=c[g>>2]|0;g=ic+96|0;c[h>>2]=c[g>>2];c[h+4>>2]=c[g+4>>2];c[h+8>>2]=c[g+8>>2];c[h+12>>2]=c[g+12>>2];g=c[k>>2]|0;k=g+96|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+108>>2]=l;i=b;return}case 7:{l=a+14964|0;g=c[l>>2]|0;k=g+12|0;if((c[k>>2]|0)<(c[g+28>>2]|0)){c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];h=c[k>>2]|0;k=g+16|0;c[g>>2]=c[k>>2];c[g+4>>2]=c[k+4>>2];c[g+8>>2]=c[k+8>>2];c[g+12>>2]=c[k+12>>2];k=c[l>>2]|0;j=k+16|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[k+28>>2]=h;jc=c[l>>2]|0}else jc=g;g=jc+44|0;if((c[g>>2]|0)<(c[jc+60>>2]|0)){h=jc+32|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];k=c[g>>2]|0;g=jc+48|0;c[h>>2]=c[g>>2];c[h+4>>2]=c[g+4>>2];c[h+8>>2]=c[g+8>>2];c[h+12>>2]=c[g+12>>2];g=c[l>>2]|0;h=g+48|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[g+60>>2]=k;kc=c[l>>2]|0}else kc=jc;k=kc+76|0;if((c[k>>2]|0)<(c[kc+92>>2]|0)){g=kc+64|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];h=c[k>>2]|0;k=kc+80|0;c[g>>2]=c[k>>2];c[g+4>>2]=c[k+4>>2];c[g+8>>2]=c[k+8>>2];c[g+12>>2]=c[k+12>>2];k=c[l>>2]|0;g=k+80|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[k+92>>2]=h;lc=c[l>>2]|0}else lc=kc;h=lc+12|0;if((c[h>>2]|0)<(c[lc+44>>2]|0)){c[d>>2]=c[lc>>2];c[d+4>>2]=c[lc+4>>2];c[d+8>>2]=c[lc+8>>2];k=c[h>>2]|0;h=lc+32|0;c[lc>>2]=c[h>>2];c[lc+4>>2]=c[h+4>>2];c[lc+8>>2]=c[h+8>>2];c[lc+12>>2]=c[h+12>>2];h=c[l>>2]|0;g=h+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[h+44>>2]=k;mc=c[l>>2]|0}else mc=lc;k=mc+76|0;if((c[k>>2]|0)<(c[mc+108>>2]|0)){h=mc+64|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];g=c[k>>2]|0;k=mc+96|0;c[h>>2]=c[k>>2];c[h+4>>2]=c[k+4>>2];c[h+8>>2]=c[k+8>>2];c[h+12>>2]=c[k+12>>2];k=c[l>>2]|0;h=k+96|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+108>>2]=g;nc=c[l>>2]|0}else nc=mc;g=nc+28|0;k=c[g>>2]|0;if((k|0)<(c[nc+60>>2]|0)){h=nc+16|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];j=c[g>>2]|0;g=nc+48|0;c[h>>2]=c[g>>2];c[h+4>>2]=c[g+4>>2];c[h+8>>2]=c[g+8>>2];c[h+12>>2]=c[g+12>>2];g=c[l>>2]|0;h=g+48|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[g+60>>2]=j;j=c[l>>2]|0;oc=j;pc=c[j+28>>2]|0}else{oc=nc;pc=k}if((pc|0)<(c[oc+44>>2]|0)){k=oc+16|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];j=c[oc+28>>2]|0;g=oc+32|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[l>>2]|0;k=g+32|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+44>>2]=j;qc=c[l>>2]|0}else qc=oc;j=qc+92|0;if((c[j>>2]|0)<(c[qc+108>>2]|0)){g=qc+80|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];k=c[j>>2]|0;j=qc+96|0;c[g>>2]=c[j>>2];c[g+4>>2]=c[j+4>>2];c[g+8>>2]=c[j+8>>2];c[g+12>>2]=c[j+12>>2];j=c[l>>2]|0;g=j+96|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[j+108>>2]=k;rc=c[l>>2]|0}else rc=qc;k=rc+12|0;if((c[k>>2]|0)<(c[rc+76>>2]|0)){c[d>>2]=c[rc>>2];c[d+4>>2]=c[rc+4>>2];c[d+8>>2]=c[rc+8>>2];j=c[k>>2]|0;k=rc+64|0;c[rc>>2]=c[k>>2];c[rc+4>>2]=c[k+4>>2];c[rc+8>>2]=c[k+8>>2];c[rc+12>>2]=c[k+12>>2];k=c[l>>2]|0;g=k+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[k+76>>2]=j;sc=c[l>>2]|0}else sc=rc;j=sc+28|0;if((c[j>>2]|0)<(c[sc+92>>2]|0)){k=sc+16|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];g=c[j>>2]|0;j=sc+80|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];c[k+12>>2]=c[j+12>>2];j=c[l>>2]|0;k=j+80|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[j+92>>2]=g;tc=c[l>>2]|0}else tc=sc;g=tc+44|0;j=c[g>>2]|0;if((j|0)<(c[tc+108>>2]|0)){k=tc+32|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];h=c[g>>2]|0;g=tc+96|0;c[k>>2]=c[g>>2];c[k+4>>2]=c[g+4>>2];c[k+8>>2]=c[g+8>>2];c[k+12>>2]=c[g+12>>2];g=c[l>>2]|0;k=g+96|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[g+108>>2]=h;h=c[l>>2]|0;uc=h;vc=c[h+44>>2]|0}else{uc=tc;vc=j}if((vc|0)<(c[uc+76>>2]|0)){j=uc+32|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];h=c[uc+44>>2]|0;g=uc+64|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[l>>2]|0;j=g+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+76>>2]=h;wc=c[l>>2]|0}else wc=uc;h=wc+60|0;if((c[h>>2]|0)<(c[wc+92>>2]|0)){g=wc+48|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[h>>2]|0;h=wc+80|0;c[g>>2]=c[h>>2];c[g+4>>2]=c[h+4>>2];c[g+8>>2]=c[h+8>>2];c[g+12>>2]=c[h+12>>2];h=c[l>>2]|0;g=h+80|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[h+92>>2]=j;xc=c[l>>2]|0}else xc=wc;j=xc+28|0;if((c[j>>2]|0)<(c[xc+44>>2]|0)){h=xc+16|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];g=c[j>>2]|0;j=xc+32|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=c[l>>2]|0;h=j+32|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[j+44>>2]=g;yc=c[l>>2]|0}else yc=xc;g=yc+60|0;if((c[g>>2]|0)<(c[yc+76>>2]|0)){j=yc+48|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];h=c[g>>2]|0;g=yc+64|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[l>>2]|0;j=g+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+76>>2]=h;zc=c[l>>2]|0}else zc=yc;h=zc+92|0;if((c[h>>2]|0)>=(c[zc+108>>2]|0)){i=b;return}g=zc+80|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[h>>2]|0;h=zc+96|0;c[g>>2]=c[h>>2];c[g+4>>2]=c[h+4>>2];c[g+8>>2]=c[h+8>>2];c[g+12>>2]=c[h+12>>2];h=c[l>>2]|0;l=h+96|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[h+108>>2]=j;i=b;return}case 6:{j=a+14964|0;h=c[j>>2]|0;l=h+12|0;if((c[l>>2]|0)<(c[h+28>>2]|0)){c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];g=c[l>>2]|0;l=h+16|0;c[h>>2]=c[l>>2];c[h+4>>2]=c[l+4>>2];c[h+8>>2]=c[l+8>>2];c[h+12>>2]=c[l+12>>2];l=c[j>>2]|0;k=l+16|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+28>>2]=g;Ac=c[j>>2]|0}else Ac=h;h=Ac+44|0;if((c[h>>2]|0)<(c[Ac+60>>2]|0)){g=Ac+32|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[h>>2]|0;h=Ac+48|0;c[g>>2]=c[h>>2];c[g+4>>2]=c[h+4>>2];c[g+8>>2]=c[h+8>>2];c[g+12>>2]=c[h+12>>2];h=c[j>>2]|0;g=h+48|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[h+60>>2]=l;Bc=c[j>>2]|0}else Bc=Ac;l=Bc+76|0;if((c[l>>2]|0)<(c[Bc+92>>2]|0)){h=Bc+64|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];g=c[l>>2]|0;l=Bc+80|0;c[h>>2]=c[l>>2];c[h+4>>2]=c[l+4>>2];c[h+8>>2]=c[l+8>>2];c[h+12>>2]=c[l+12>>2];l=c[j>>2]|0;h=l+80|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[l+92>>2]=g;Cc=c[j>>2]|0}else Cc=Bc;g=Cc+12|0;if((c[g>>2]|0)<(c[Cc+44>>2]|0)){c[d>>2]=c[Cc>>2];c[d+4>>2]=c[Cc+4>>2];c[d+8>>2]=c[Cc+8>>2];l=c[g>>2]|0;g=Cc+32|0;c[Cc>>2]=c[g>>2];c[Cc+4>>2]=c[g+4>>2];c[Cc+8>>2]=c[g+8>>2];c[Cc+12>>2]=c[g+12>>2];g=c[j>>2]|0;h=g+32|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[g+44>>2]=l;Dc=c[j>>2]|0}else Dc=Cc;l=Dc+28|0;g=c[l>>2]|0;if((g|0)<(c[Dc+60>>2]|0)){h=Dc+16|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];k=c[l>>2]|0;l=Dc+48|0;c[h>>2]=c[l>>2];c[h+4>>2]=c[l+4>>2];c[h+8>>2]=c[l+8>>2];c[h+12>>2]=c[l+12>>2];l=c[j>>2]|0;h=l+48|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[l+60>>2]=k;k=c[j>>2]|0;Ec=k;Fc=c[k+28>>2]|0}else{Ec=Dc;Fc=g}if((Fc|0)<(c[Ec+44>>2]|0)){g=Ec+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];k=c[Ec+28>>2]|0;l=Ec+32|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[j>>2]|0;g=l+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+44>>2]=k;Gc=c[j>>2]|0}else Gc=Ec;k=Gc+12|0;if((c[k>>2]|0)<(c[Gc+76>>2]|0)){c[d>>2]=c[Gc>>2];c[d+4>>2]=c[Gc+4>>2];c[d+8>>2]=c[Gc+8>>2];l=c[k>>2]|0;k=Gc+64|0;c[Gc>>2]=c[k>>2];c[Gc+4>>2]=c[k+4>>2];c[Gc+8>>2]=c[k+8>>2];c[Gc+12>>2]=c[k+12>>2];k=c[j>>2]|0;g=k+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[k+76>>2]=l;Hc=c[j>>2]|0}else Hc=Gc;l=Hc+28|0;if((c[l>>2]|0)<(c[Hc+92>>2]|0)){k=Hc+16|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];g=c[l>>2]|0;l=Hc+80|0;c[k>>2]=c[l>>2];c[k+4>>2]=c[l+4>>2];c[k+8>>2]=c[l+8>>2];c[k+12>>2]=c[l+12>>2];l=c[j>>2]|0;k=l+80|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+92>>2]=g;Ic=c[j>>2]|0}else Ic=Hc;g=Ic+44|0;if((c[g>>2]|0)<(c[Ic+76>>2]|0)){l=Ic+32|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];k=c[g>>2]|0;g=Ic+64|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[j>>2]|0;l=g+64|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[g+76>>2]=k;Jc=c[j>>2]|0}else Jc=Ic;k=Jc+60|0;if((c[k>>2]|0)<(c[Jc+92>>2]|0)){g=Jc+48|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[k>>2]|0;k=Jc+80|0;c[g>>2]=c[k>>2];c[g+4>>2]=c[k+4>>2];c[g+8>>2]=c[k+8>>2];c[g+12>>2]=c[k+12>>2];k=c[j>>2]|0;g=k+80|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[k+92>>2]=l;Kc=c[j>>2]|0}else Kc=Jc;l=Kc+28|0;if((c[l>>2]|0)<(c[Kc+44>>2]|0)){k=Kc+16|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];g=c[l>>2]|0;l=Kc+32|0;c[k>>2]=c[l>>2];c[k+4>>2]=c[l+4>>2];c[k+8>>2]=c[l+8>>2];c[k+12>>2]=c[l+12>>2];l=c[j>>2]|0;k=l+32|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+44>>2]=g;Lc=c[j>>2]|0}else Lc=Kc;g=Lc+60|0;if((c[g>>2]|0)>=(c[Lc+76>>2]|0)){i=b;return}l=Lc+48|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];k=c[g>>2]|0;g=Lc+64|0;c[l>>2]=c[g>>2];c[l+4>>2]=c[g+4>>2];c[l+8>>2]=c[g+8>>2];c[l+12>>2]=c[g+12>>2];g=c[j>>2]|0;j=g+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+76>>2]=k;i=b;return}case 5:{k=a+14964|0;g=c[k>>2]|0;j=g+12|0;if((c[j>>2]|0)<(c[g+28>>2]|0)){c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];l=c[j>>2]|0;j=g+16|0;c[g>>2]=c[j>>2];c[g+4>>2]=c[j+4>>2];c[g+8>>2]=c[j+8>>2];c[g+12>>2]=c[j+12>>2];j=c[k>>2]|0;h=j+16|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[j+28>>2]=l;Mc=c[k>>2]|0}else Mc=g;g=Mc+44|0;l=c[g>>2]|0;if((l|0)<(c[Mc+60>>2]|0)){j=Mc+32|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];h=c[g>>2]|0;g=Mc+48|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;j=g+48|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[g+60>>2]=h;h=c[k>>2]|0;Nc=h;Oc=c[h+44>>2]|0}else{Nc=Mc;Oc=l}l=Nc+12|0;if((c[l>>2]|0)<(Oc|0)){c[d>>2]=c[Nc>>2];c[d+4>>2]=c[Nc+4>>2];c[d+8>>2]=c[Nc+8>>2];h=c[l>>2]|0;l=Nc+32|0;c[Nc>>2]=c[l>>2];c[Nc+4>>2]=c[l+4>>2];c[Nc+8>>2]=c[l+8>>2];c[Nc+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+44>>2]=h;Pc=c[k>>2]|0}else Pc=Nc;h=Pc+28|0;l=c[h>>2]|0;if((l|0)<(c[Pc+60>>2]|0)){g=Pc+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];j=c[h>>2]|0;h=Pc+48|0;c[g>>2]=c[h>>2];c[g+4>>2]=c[h+4>>2];c[g+8>>2]=c[h+8>>2];c[g+12>>2]=c[h+12>>2];h=c[k>>2]|0;g=h+48|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[h+60>>2]=j;j=c[k>>2]|0;Qc=j;Rc=c[j+28>>2]|0}else{Qc=Pc;Rc=l}if((Rc|0)<(c[Qc+44>>2]|0)){l=Qc+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[Qc+28>>2]|0;h=Qc+32|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[k>>2]|0;l=h+32|0;c[l>>2]=c[d>>2];c[l+4>>2]=c[d+4>>2];c[l+8>>2]=c[d+8>>2];c[h+44>>2]=j;Sc=c[k>>2]|0}else Sc=Qc;j=Sc+12|0;h=c[Sc+76>>2]|0;if((c[j>>2]|0)<(h|0)){c[d>>2]=c[Sc>>2];c[d+4>>2]=c[Sc+4>>2];c[d+8>>2]=c[Sc+8>>2];l=c[j>>2]|0;j=Sc+64|0;c[Sc>>2]=c[j>>2];c[Sc+4>>2]=c[j+4>>2];c[Sc+8>>2]=c[j+8>>2];c[Sc+12>>2]=c[j+12>>2];j=c[k>>2]|0;g=j+64|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[j+76>>2]=l;l=c[k>>2]|0;Tc=l;Uc=c[l+76>>2]|0}else{Tc=Sc;Uc=h}h=Tc+44|0;l=c[h>>2]|0;if((l|0)<(Uc|0)){j=Tc+32|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];g=c[h>>2]|0;h=Tc+64|0;c[j>>2]=c[h>>2];c[j+4>>2]=c[h+4>>2];c[j+8>>2]=c[h+8>>2];c[j+12>>2]=c[h+12>>2];h=c[k>>2]|0;j=h+64|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[h+76>>2]=g;g=c[k>>2]|0;Vc=g;Wc=c[g+44>>2]|0}else{Vc=Tc;Wc=l}l=Vc+28|0;if((c[l>>2]|0)<(Wc|0)){g=Vc+16|0;c[d>>2]=c[g>>2];c[d+4>>2]=c[g+4>>2];c[d+8>>2]=c[g+8>>2];h=c[l>>2]|0;l=Vc+32|0;c[g>>2]=c[l>>2];c[g+4>>2]=c[l+4>>2];c[g+8>>2]=c[l+8>>2];c[g+12>>2]=c[l+12>>2];l=c[k>>2]|0;g=l+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[l+44>>2]=h;Xc=c[k>>2]|0}else Xc=Vc;h=Xc+60|0;if((c[h>>2]|0)>=(c[Xc+76>>2]|0)){i=b;return}l=Xc+48|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];g=c[h>>2]|0;h=Xc+64|0;c[l>>2]=c[h>>2];c[l+4>>2]=c[h+4>>2];c[l+8>>2]=c[h+8>>2];c[l+12>>2]=c[h+12>>2];h=c[k>>2]|0;k=h+64|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[h+76>>2]=g;i=b;return}case 4:{g=a+14964|0;h=c[g>>2]|0;k=h+12|0;if((c[k>>2]|0)<(c[h+28>>2]|0)){c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];l=c[k>>2]|0;k=h+16|0;c[h>>2]=c[k>>2];c[h+4>>2]=c[k+4>>2];c[h+8>>2]=c[k+8>>2];c[h+12>>2]=c[k+12>>2];k=c[g>>2]|0;j=k+16|0;c[j>>2]=c[d>>2];c[j+4>>2]=c[d+4>>2];c[j+8>>2]=c[d+8>>2];c[k+28>>2]=l;Yc=c[g>>2]|0}else Yc=h;h=Yc+44|0;l=c[h>>2]|0;if((l|0)<(c[Yc+60>>2]|0)){k=Yc+32|0;c[d>>2]=c[k>>2];c[d+4>>2]=c[k+4>>2];c[d+8>>2]=c[k+8>>2];j=c[h>>2]|0;h=Yc+48|0;c[k>>2]=c[h>>2];c[k+4>>2]=c[h+4>>2];c[k+8>>2]=c[h+8>>2];c[k+12>>2]=c[h+12>>2];h=c[g>>2]|0;k=h+48|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[h+60>>2]=j;j=c[g>>2]|0;Zc=j;_c=c[j+44>>2]|0}else{Zc=Yc;_c=l}l=Zc+12|0;if((c[l>>2]|0)<(_c|0)){c[d>>2]=c[Zc>>2];c[d+4>>2]=c[Zc+4>>2];c[d+8>>2]=c[Zc+8>>2];j=c[l>>2]|0;l=Zc+32|0;c[Zc>>2]=c[l>>2];c[Zc+4>>2]=c[l+4>>2];c[Zc+8>>2]=c[l+8>>2];c[Zc+12>>2]=c[l+12>>2];l=c[g>>2]|0;h=l+32|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[l+44>>2]=j;$c=c[g>>2]|0}else $c=Zc;j=$c+28|0;l=c[j>>2]|0;if((l|0)<(c[$c+60>>2]|0)){h=$c+16|0;c[d>>2]=c[h>>2];c[d+4>>2]=c[h+4>>2];c[d+8>>2]=c[h+8>>2];k=c[j>>2]|0;j=$c+48|0;c[h>>2]=c[j>>2];c[h+4>>2]=c[j+4>>2];c[h+8>>2]=c[j+8>>2];c[h+12>>2]=c[j+12>>2];j=c[g>>2]|0;h=j+48|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[j+60>>2]=k;k=c[g>>2]|0;ad=k;bd=c[k+28>>2]|0}else{ad=$c;bd=l}if((bd|0)>=(c[ad+44>>2]|0)){i=b;return}l=ad+16|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];k=c[ad+28>>2]|0;j=ad+32|0;c[l>>2]=c[j>>2];c[l+4>>2]=c[j+4>>2];c[l+8>>2]=c[j+8>>2];c[l+12>>2]=c[j+12>>2];j=c[g>>2]|0;g=j+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[j+44>>2]=k;i=b;return}case 3:{k=a+14964|0;j=c[k>>2]|0;g=j+12|0;l=c[g>>2]|0;if((l|0)<(c[j+28>>2]|0)){c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];h=c[g>>2]|0;g=j+16|0;c[j>>2]=c[g>>2];c[j+4>>2]=c[g+4>>2];c[j+8>>2]=c[g+8>>2];c[j+12>>2]=c[g+12>>2];g=c[k>>2]|0;cd=g+16|0;c[cd>>2]=c[d>>2];c[cd+4>>2]=c[d+4>>2];c[cd+8>>2]=c[d+8>>2];c[g+28>>2]=h;h=c[k>>2]|0;dd=h;ed=c[h+12>>2]|0}else{dd=j;ed=l}l=c[dd+44>>2]|0;if((ed|0)<(l|0)){c[d>>2]=c[dd>>2];c[d+4>>2]=c[dd+4>>2];c[d+8>>2]=c[dd+8>>2];j=c[dd+12>>2]|0;h=dd+32|0;c[dd>>2]=c[h>>2];c[dd+4>>2]=c[h+4>>2];c[dd+8>>2]=c[h+8>>2];c[dd+12>>2]=c[h+12>>2];h=c[k>>2]|0;g=h+32|0;c[g>>2]=c[d>>2];c[g+4>>2]=c[d+4>>2];c[g+8>>2]=c[d+8>>2];c[h+44>>2]=j;j=c[k>>2]|0;fd=j;gd=c[j+44>>2]|0}else{fd=dd;gd=l}l=fd+28|0;if((c[l>>2]|0)>=(gd|0)){i=b;return}j=fd+16|0;c[d>>2]=c[j>>2];c[d+4>>2]=c[j+4>>2];c[d+8>>2]=c[j+8>>2];h=c[l>>2]|0;l=fd+32|0;c[j>>2]=c[l>>2];c[j+4>>2]=c[l+4>>2];c[j+8>>2]=c[l+8>>2];c[j+12>>2]=c[l+12>>2];l=c[k>>2]|0;k=l+32|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];c[l+44>>2]=h;i=b;return}case 2:{h=a+14964|0;l=c[h>>2]|0;k=l+12|0;if((c[k>>2]|0)>=(c[l+28>>2]|0)){i=b;return};c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];j=c[k>>2]|0;k=l+16|0;c[l>>2]=c[k>>2];c[l+4>>2]=c[k+4>>2];c[l+8>>2]=c[k+8>>2];c[l+12>>2]=c[k+12>>2];k=c[h>>2]|0;h=k+16|0;c[h>>2]=c[d>>2];c[h+4>>2]=c[d+4>>2];c[h+8>>2]=c[d+8>>2];c[k+28>>2]=j;i=b;return}default:{if((f|0)<=1){i=b;return}j=a+14964|0;k=1;do{h=c[j>>2]|0;l=h+(k<<4)|0;c[d>>2]=c[l>>2];c[d+4>>2]=c[l+4>>2];c[d+8>>2]=c[l+8>>2];l=c[h+(k<<4)+12>>2]|0;g=h;h=k;while(1){cd=h;h=h+-1|0;hd=g+(cd<<4)|0;if((l|0)<=(c[g+(h<<4)+12>>2]|0)){id=hd;jd=g;kd=cd;break}cd=g+(h<<4)|0;c[hd>>2]=c[cd>>2];c[hd+4>>2]=c[cd+4>>2];c[hd+8>>2]=c[cd+8>>2];c[hd+12>>2]=c[cd+12>>2];cd=c[j>>2]|0;if(!h){id=cd;jd=cd;kd=0;break}else g=cd}c[id>>2]=c[d>>2];c[id+4>>2]=c[d+4>>2];c[id+8>>2]=c[d+8>>2];c[jd+(kd<<4)+12>>2]=l;k=k+1|0}while((k|0)<(c[e>>2]|0));i=b;return}}while(0)}function cc(a,d,e,f){a=a|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0;g=a+36+(d*220|0)|0;h=a+2896|0;c[h>>2]=g;i=c[g>>2]|0;c[a>>2]=i;j=a+8|0;c[j>>2]=i+e&3;c[a+16>>2]=d;i=a+4|0;c[i>>2]=c[a+36+(d*220|0)+4>>2];k=a+14964|0;c[k>>2]=a+2900+(d*928|0)+(e*232|0);l=g+((e<<4)+140)|0;c[l>>2]=0;c[l+4>>2]=0;c[l+8>>2]=0;c[l+12>>2]=0;l=a+28|0;c[l>>2]=0;g=c[a+20>>2]|0;if((g|0)==4)m=0;else m=(c[f+1480+(g<<3)>>2]|0)!=0;g=m&1;m=c[i>>2]|0;i=c[j>>2]|0;n=b[f+(i<<3)+(m<<1)>>1]|0;if(n<<16>>16){o=n&65535;n=c[1895748+(o*116|0)>>2]|0;p=c[(c[h>>2]|0)+204+(m<<2)>>2]|0;if((n|0)>-1){q=c[k>>2]|0;r=0;s=n;while(1){n=c[1895748+(o*116|0)+4+(s<<2)>>2]|0;t=c[1895748+(o*116|0)+32+(s<<2)>>2]|0;a:do if((s|0)>0){u=s;v=t;while(1){w=c[1895748+(o*116|0)+88+(u<<2)>>2]|0;if((w&p|0)!=(w|0)){x=u;y=v;break a}w=u+-1|0;z=c[1895748+(o*116|0)+60+(w<<2)>>2]|v;if((u|0)>1){u=w;v=z}else{x=w;y=z;break}}}else{x=s;y=t}while(0);c[q+(r<<4)+8>>2]=y;c[q+(r<<4)>>2]=m;c[q+(r<<4)+4>>2]=n;r=r+1|0;c[l>>2]=r;if((x|0)<=0)break;else s=x+-1|0}}c[a+2900+(d*928|0)+(e*232|0)+224>>2]=0;x=c[l>>2]|0;c[a+2900+(d*928|0)+(e*232|0)+228>>2]=x+-1;if((x|0)==1){A=1;return A|0}x=a+39916+((g|e<<2)<<3)|0;s=c[x>>2]|0;r=c[x+4>>2]|0;x=a+(r>>1)|0;if(!(r&1))B=s;else B=c[(c[x>>2]|0)+s>>2]|0;ab[B&15](x,f);bc(a);A=c[l>>2]|0;return A|0}x=a+39916+((e<<2|g|2)<<3)|0;g=c[x>>2]|0;B=c[x+4>>2]|0;x=a+24|0;c[x>>2]=0;s=a+32|0;r=a+(B>>1)|0;q=g;b:do if(!(B&1)){m=i;y=0;while(1){o=b[f+(m<<3)+(y<<1)>>1]|0;if(!(o<<16>>16))C=y;else{p=o&65535;o=c[l>>2]|0;c[s>>2]=o;t=c[1895748+(p*116|0)>>2]|0;v=c[(c[h>>2]|0)+204+(y<<2)>>2]|0;if((t|0)>-1){u=c[k>>2]|0;z=o;o=t;while(1){t=c[1895748+(p*116|0)+4+(o<<2)>>2]|0;w=c[1895748+(p*116|0)+32+(o<<2)>>2]|0;c:do if((o|0)>0){D=o;E=w;while(1){F=c[1895748+(p*116|0)+88+(D<<2)>>2]|0;if((F&v|0)!=(F|0)){G=D;H=E;break c}F=D+-1|0;I=c[1895748+(p*116|0)+60+(F<<2)>>2]|E;if((D|0)>1){D=F;E=I}else{G=F;H=I;break}}}else{G=o;H=w}while(0);c[u+(z<<4)+8>>2]=H;c[u+(z<<4)>>2]=y;c[u+(z<<4)+4>>2]=t;z=z+1|0;c[l>>2]=z;if((G|0)<=0)break;else o=G+-1|0}}ab[q&15](r,f);C=c[x>>2]|0}o=C+1|0;c[x>>2]=o;if((o|0)>=4)break b;m=c[j>>2]|0;y=o}}else{y=i;m=0;while(1){n=b[f+(y<<3)+(m<<1)>>1]|0;if(!(n<<16>>16))J=m;else{o=n&65535;n=c[l>>2]|0;c[s>>2]=n;z=c[1895748+(o*116|0)>>2]|0;u=c[(c[h>>2]|0)+204+(m<<2)>>2]|0;if((z|0)>-1){p=c[k>>2]|0;v=n;n=z;while(1){z=c[1895748+(o*116|0)+4+(n<<2)>>2]|0;w=c[1895748+(o*116|0)+32+(n<<2)>>2]|0;d:do if((n|0)>0){E=n;D=w;while(1){I=c[1895748+(o*116|0)+88+(E<<2)>>2]|0;if((I&u|0)!=(I|0)){K=E;L=D;break d}I=E+-1|0;F=c[1895748+(o*116|0)+60+(I<<2)>>2]|D;if((E|0)>1){E=I;D=F}else{K=I;L=F;break}}}else{K=n;L=w}while(0);c[p+(v<<4)+8>>2]=L;c[p+(v<<4)>>2]=m;c[p+(v<<4)+4>>2]=z;v=v+1|0;c[l>>2]=v;if((K|0)<=0)break;else n=K+-1|0}}ab[c[(c[r>>2]|0)+g>>2]&15](r,f);J=c[x>>2]|0}n=J+1|0;c[x>>2]=n;if((n|0)>=4)break b;y=c[j>>2]|0;m=n}}while(0);c[a+2900+(d*928|0)+(e*232|0)+224>>2]=0;j=c[l>>2]|0;c[a+2900+(d*928|0)+(e*232|0)+228>>2]=j+-1;if((j|0)==1){A=1;return A|0}bc(a);A=c[l>>2]|0;return A|0}function dc(a,b,d){a=a|0;b=b|0;d=d|0;return (c[a+2900+(b*928|0)+(d*232|0)+228>>2]|0)+1|0}function ec(a,b,d,f){a=a|0;b=b|0;d=d|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;g=a+2896|0;c[g>>2]=a+36+(d*220|0);h=c[b>>2]|0;do if(f){i=f+-1|0;j=a+36+(d*220|0)+76+(i*12|0)|0;if((h|0)==(c[j>>2]|0)){k=b+4|0;l=c[k>>2]|0;if((l|0)>(c[a+36+(d*220|0)+76+(i*12|0)+4>>2]|0)){c[a+36+(d*220|0)+76+(f*12|0)>>2]=h;m=c[g>>2]|0;c[m+76+(f*12|0)+4>>2]=l;c[m+76+(f*12|0)+8>>2]=c[b+8>>2];c[m+124+(f<<2)>>2]=f;n=k;o=m;break}else{m=a+36+(d*220|0)+76+(f*12|0)|0;c[m>>2]=c[j>>2];c[m+4>>2]=c[j+4>>2];c[m+8>>2]=c[j+8>>2];m=c[g>>2]|0;c[m+124+(f<<2)>>2]=c[m+124+(i<<2)>>2];n=k;o=m;break}}else if((h|0)==(c[a+20>>2]|0)){c[a+36+(d*220|0)+76+(f*12|0)>>2]=h;m=b+4|0;k=c[g>>2]|0;c[k+76+(f*12|0)+4>>2]=c[m>>2];c[k+76+(f*12|0)+8>>2]=c[b+8>>2];c[k+124+(f<<2)>>2]=f;n=m;o=k;break}else{k=a+36+(d*220|0)+76+(f*12|0)|0;c[k>>2]=c[j>>2];c[k+4>>2]=c[j+4>>2];c[k+8>>2]=c[j+8>>2];j=c[g>>2]|0;c[j+124+(f<<2)>>2]=c[j+124+(i<<2)>>2];n=b+4|0;o=j;break}}else{c[a+36+(d*220|0)+76>>2]=h;j=b+4|0;i=c[g>>2]|0;c[i+80>>2]=c[j>>2];c[i+84>>2]=c[b+8>>2];c[i+124>>2]=0;c[i+4>>2]=c[b>>2];n=j;o=i}while(0);c[o+8+(f<<2)>>2]=c[b>>2];c[o+24+(f<<2)>>2]=c[n>>2];if((f|0)!=3)return;f=d+-1|0;c[a+36+(f*220|0)>>2]=((c[o+136>>2]|0)+(c[o>>2]|0)|0)%4|0;c[a+36+(f*220|0)+204>>2]=c[(c[g>>2]|0)+204>>2];c[a+36+(f*220|0)+208>>2]=c[(c[g>>2]|0)+208>>2];c[a+36+(f*220|0)+212>>2]=c[(c[g>>2]|0)+212>>2];c[a+36+(f*220|0)+216>>2]=c[(c[g>>2]|0)+216>>2];o=c[g>>2]|0;d=a+36+(f*220|0)+204+(c[o+8>>2]<<2)|0;c[d>>2]=c[d>>2]|(e[1224+(c[o+24>>2]<<1)>>1]|0);o=c[g>>2]|0;d=a+36+(f*220|0)+204+(c[o+12>>2]<<2)|0;c[d>>2]=c[d>>2]|(e[1224+(c[o+28>>2]<<1)>>1]|0);o=c[g>>2]|0;d=a+36+(f*220|0)+204+(c[o+16>>2]<<2)|0;c[d>>2]=c[d>>2]|(e[1224+(c[o+32>>2]<<1)>>1]|0);o=c[g>>2]|0;g=a+36+(f*220|0)+204+(c[o+20>>2]<<2)|0;c[g>>2]=c[g>>2]|(e[1224+(c[o+36>>2]<<1)>>1]|0);return}
function fc(a,b,d,f){a=a|0;b=b|0;d=d|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;g=a+2896|0;c[g>>2]=a+36+(b*220|0);h=a+2900+(b*928|0)+(d*232|0)+228|0;i=c[h>>2]|0;if((i|0)==-1){j=0;return j|0}k=a+2900+(b*928|0)+(d*232|0)+224|0;l=c[k>>2]|0;a:do if(!l)m=a+2900+(b*928|0)+(d*232|0)|0;else{n=l+-1|0;o=c[a+2900+(b*928|0)+(d*232|0)+(n<<4)>>2]|0;p=a+36+(b*220|0)+140+(d<<4)+(o<<2)|0;if((c[p>>2]|0)==0?(q=c[1830212+((e[f+(o<<1)>>1]|0)<<2)>>2]|0,o=(q|0)==0?15:q,(c[a+2900+(b*928|0)+(d*232|0)+(n<<4)+4>>2]|0)<(o|0)):0){c[p>>2]=o;r=c[k>>2]|0;s=c[h>>2]|0}else{r=l;s=i}if((r|0)>(s|0)){j=0;return j|0}else t=r;while(1){o=a+2900+(b*928|0)+(d*232|0)+(t<<4)|0;if((c[a+2900+(b*928|0)+(d*232|0)+(t<<4)+4>>2]|0)>=(c[a+36+(b*220|0)+140+(d<<4)+(c[o>>2]<<2)>>2]|0)){m=o;break a}o=t;t=t+1|0;c[k>>2]=t;if((o|0)>=(s|0)){j=0;break}}return j|0}while(0);s=c[m>>2]|0;do if(d){t=d+-1|0;r=c[g>>2]|0;i=r+76+(t*12|0)|0;if((s|0)==(c[i>>2]|0)){l=m+4|0;h=c[l>>2]|0;if((h|0)>(c[r+76+(t*12|0)+4>>2]|0)){c[r+76+(d*12|0)>>2]=s;c[r+76+(d*12|0)+4>>2]=h;c[r+76+(d*12|0)+8>>2]=c[m+8>>2];c[r+124+(d<<2)>>2]=d;u=l;v=r;break}else{h=r+76+(d*12|0)|0;c[h>>2]=c[i>>2];c[h+4>>2]=c[i+4>>2];c[h+8>>2]=c[i+8>>2];h=c[g>>2]|0;c[h+124+(d<<2)>>2]=c[h+124+(t<<2)>>2];u=l;v=h;break}}else if((s|0)==(c[a+20>>2]|0)){c[r+76+(d*12|0)>>2]=s;h=m+4|0;c[r+76+(d*12|0)+4>>2]=c[h>>2];c[r+76+(d*12|0)+8>>2]=c[m+8>>2];c[r+124+(d<<2)>>2]=d;u=h;v=r;break}else{h=r+76+(d*12|0)|0;c[h>>2]=c[i>>2];c[h+4>>2]=c[i+4>>2];c[h+8>>2]=c[i+8>>2];i=c[g>>2]|0;c[i+124+(d<<2)>>2]=c[i+124+(t<<2)>>2];u=m+4|0;v=i;break}}else{i=c[g>>2]|0;c[i+76>>2]=s;t=m+4|0;c[i+80>>2]=c[t>>2];c[i+84>>2]=c[m+8>>2];c[i+124>>2]=0;c[i+4>>2]=c[m>>2];u=t;v=i}while(0);c[v+8+(d<<2)>>2]=c[m>>2];c[v+24+(d<<2)>>2]=c[u>>2];if((d|0)==3){d=b+-1|0;c[a+36+(d*220|0)>>2]=((c[v+136>>2]|0)+(c[v>>2]|0)|0)%4|0;c[a+36+(d*220|0)+204>>2]=c[(c[g>>2]|0)+204>>2];c[a+36+(d*220|0)+208>>2]=c[(c[g>>2]|0)+208>>2];c[a+36+(d*220|0)+212>>2]=c[(c[g>>2]|0)+212>>2];c[a+36+(d*220|0)+216>>2]=c[(c[g>>2]|0)+216>>2];v=c[g>>2]|0;b=a+36+(d*220|0)+204+(c[v+8>>2]<<2)|0;c[b>>2]=c[b>>2]|(e[1224+(c[v+24>>2]<<1)>>1]|0);v=c[g>>2]|0;b=a+36+(d*220|0)+204+(c[v+12>>2]<<2)|0;c[b>>2]=c[b>>2]|(e[1224+(c[v+28>>2]<<1)>>1]|0);v=c[g>>2]|0;b=a+36+(d*220|0)+204+(c[v+16>>2]<<2)|0;c[b>>2]=c[b>>2]|(e[1224+(c[v+32>>2]<<1)>>1]|0);v=c[g>>2]|0;g=a+36+(d*220|0)+204+(c[v+20>>2]<<2)|0;c[g>>2]=c[g>>2]|(e[1224+(c[v+36>>2]<<1)>>1]|0)}c[k>>2]=(c[k>>2]|0)+1;j=m;return j|0}function gc(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;e=a+2900+(b*928|0)+(d*232|0)+224|0;f=c[e>>2]|0;if((f|0)>(c[a+2900+(b*928|0)+(d*232|0)+228>>2]|0)){g=0;return g|0}h=a+2900+(b*928|0)+(d*232|0)+(f<<4)|0;i=a+2896|0;c[i>>2]=a+36+(b*220|0);j=c[h>>2]|0;do if(d){k=d+-1|0;l=a+36+(b*220|0)+76+(k*12|0)|0;if((j|0)==(c[l>>2]|0)){m=a+2900+(b*928|0)+(d*232|0)+(f<<4)+4|0;if((c[m>>2]|0)>(c[a+36+(b*220|0)+76+(k*12|0)+4>>2]|0)){c[a+36+(b*220|0)+76+(d*12|0)>>2]=j;n=c[i>>2]|0;c[n+76+(d*12|0)+4>>2]=c[m>>2];c[n+76+(d*12|0)+8>>2]=c[a+2900+(b*928|0)+(d*232|0)+(f<<4)+8>>2];c[n+124+(d<<2)>>2]=d;o=n;break}else{n=a+36+(b*220|0)+76+(d*12|0)|0;c[n>>2]=c[l>>2];c[n+4>>2]=c[l+4>>2];c[n+8>>2]=c[l+8>>2];n=c[i>>2]|0;c[n+124+(d<<2)>>2]=c[n+124+(k<<2)>>2];o=n;break}}else if((j|0)==(c[a+20>>2]|0)){c[a+36+(b*220|0)+76+(d*12|0)>>2]=j;n=c[i>>2]|0;c[n+76+(d*12|0)+4>>2]=c[a+2900+(b*928|0)+(d*232|0)+(f<<4)+4>>2];c[n+76+(d*12|0)+8>>2]=c[a+2900+(b*928|0)+(d*232|0)+(f<<4)+8>>2];c[n+124+(d<<2)>>2]=d;o=n;break}else{n=a+36+(b*220|0)+76+(d*12|0)|0;c[n>>2]=c[l>>2];c[n+4>>2]=c[l+4>>2];c[n+8>>2]=c[l+8>>2];l=c[i>>2]|0;c[l+124+(d<<2)>>2]=c[l+124+(k<<2)>>2];o=l;break}}else{c[a+36+(b*220|0)+76>>2]=j;l=c[i>>2]|0;c[l+80>>2]=c[a+2900+(b*928|0)+(f<<4)+4>>2];c[l+84>>2]=c[a+2900+(b*928|0)+(f<<4)+8>>2];c[l+124>>2]=0;c[l+4>>2]=c[h>>2];o=l}while(0);c[o+8+(d<<2)>>2]=c[h>>2];c[o+24+(d<<2)>>2]=c[a+2900+(b*928|0)+(d*232|0)+(f<<4)+4>>2];if((d|0)==3)c[a+36+((b+-1|0)*220|0)>>2]=((c[o+136>>2]|0)+(c[o>>2]|0)|0)%4|0;c[e>>2]=(c[e>>2]|0)+1;g=h;return g|0}function hc(a,b,d){a=a|0;b=b|0;d=d|0;c[a+2900+(b*928|0)+(d*232|0)+224>>2]=0;return}function ic(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;f=a+2900+(b*928|0)+(d*232|0)+228|0;g=1;do{h=c[e+(g<<4)>>2]|0;i=c[e+(g<<4)+4>>2]|0;if((i|0)!=0?(j=c[f>>2]|0,(j|0)>=0):0){k=j;j=0;while(1){if((h|0)==(c[a+2900+(b*928|0)+(d*232|0)+(j<<4)>>2]|0)?(i|0)==(c[a+2900+(b*928|0)+(d*232|0)+(j<<4)+4>>2]|0):0){if((j|0)>(k|0))l=k;else{m=j;do{n=a+2900+(b*928|0)+(d*232|0)+(m<<4)|0;o=m;m=m+1|0;p=a+2900+(b*928|0)+(d*232|0)+(m<<4)|0;c[n>>2]=c[p>>2];c[n+4>>2]=c[p+4>>2];c[n+8>>2]=c[p+8>>2];c[n+12>>2]=c[p+12>>2];p=c[f>>2]|0}while((o|0)<(p|0));l=p}m=l+-1|0;c[f>>2]=m;q=m}else q=k;if((j|0)<(q|0)){k=q;j=j+1|0}else break}}g=g+1|0}while((g|0)!=14);return}function jc(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0;d=i;i=i+16|0;e=d;f=a+((b*220|0)+76)|0;c[f>>2]=0;c[f+4>>2]=0;c[f+8>>2]=0;c[f+12>>2]=0;f=a+2896|0;g=a+36+(b*220|0)+40+(c[(c[f>>2]|0)+8>>2]<<2)|0;c[g>>2]=(c[g>>2]|0)+1;g=a+36+(b*220|0)+40+(c[(c[f>>2]|0)+12>>2]<<2)|0;c[g>>2]=(c[g>>2]|0)+1;g=a+36+(b*220|0)+40+(c[(c[f>>2]|0)+16>>2]<<2)|0;c[g>>2]=(c[g>>2]|0)+1;g=a+36+(b*220|0)+40+(c[(c[f>>2]|0)+20>>2]<<2)|0;c[g>>2]=(c[g>>2]|0)+1;g=(c[a+36+(b*220|0)+52>>2]|0)+((c[a+36+(b*220|0)+48>>2]|0)+((c[a+36+(b*220|0)+44>>2]|0)+(c[a+36+(b*220|0)+40>>2]|0)))|0;if((g|0)==4){c[a+36+(b*220|0)+56>>2]=c[(c[f>>2]|0)+116>>2];c[a+36+(b*220|0)+60>>2]=c[(c[f>>2]|0)+112>>2];c[a+36+(b*220|0)+64>>2]=c[(c[f>>2]|0)+120>>2];c[a+36+(b*220|0)+68>>2]=c[(c[f>>2]|0)+136>>2];i=d;return a+36+(b*220|0)+40|0}else{c[e>>2]=g;ne(2248,e)|0;Va(1)}return 0}function kc(b,d){b=b|0;d=d|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;f=d;g=f+64|0;do{c[f>>2]=0;f=f+4|0}while((f|0)<(g|0));f=0;a:while(1){g=a[b+f>>0]|0;switch(g<<24>>24){case 115:case 101:case 110:case 119:case 83:case 69:case 78:case 87:{h=g;i=f;break a;break}default:{}}if((f|0)>=3){j=0;k=68;break}f=f+1|0}if((k|0)==68)return j|0;if((i|0)>2){j=0;return j|0}switch(h<<24>>24){case 110:case 78:{l=0;break}case 101:case 69:{l=1;break}case 115:case 83:{l=2;break}default:l=3}h=i+2|0;if((h|0)>=80){j=1;return j|0}if(!l){i=h;f=0;g=0;while(1){m=a[b+i>>0]|0;if(!(m<<24>>24)){j=1;k=68;break}b:do switch(m<<24>>24|0){case 50:{n=2;k=31;break}case 51:{n=3;k=31;break}case 52:{n=4;k=31;break}case 53:{n=5;k=31;break}case 54:{n=6;k=31;break}case 55:{n=7;k=31;break}case 56:{n=8;k=31;break}case 57:{n=9;k=31;break}case 84:{n=10;k=31;break}case 74:{n=11;k=31;break}case 81:{n=12;k=31;break}case 75:{n=13;k=31;break}case 65:{n=14;k=31;break}case 116:{n=10;k=31;break}case 106:{n=11;k=31;break}case 113:{n=12;k=31;break}case 107:{n=13;k=31;break}case 97:{n=14;k=31;break}default:switch(m<<24>>24){case 46:{o=f;p=g+1|0;break b;break}case 32:{o=f+1|0;p=0;break b;break}default:{o=f;p=g;break b}}}while(0);if((k|0)==31){k=0;m=d+(f<<4)+(g<<2)|0;c[m>>2]=(e[1224+(n<<1)>>1]|0)<<2|c[m>>2];o=f;p=g}i=i+1|0;if((i|0)>=80){j=1;k=68;break}else{f=o;g=p}}if((k|0)==68)return j|0}else{q=h;r=0;s=0}while(1){h=a[b+q>>0]|0;if(!(h<<24>>24)){j=1;k=68;break}c:do switch(h<<24>>24|0){case 50:{t=2;k=55;break}case 51:{t=3;k=55;break}case 52:{t=4;k=55;break}case 53:{t=5;k=55;break}case 54:{t=6;k=55;break}case 55:{t=7;k=55;break}case 56:{t=8;k=55;break}case 57:{t=9;k=55;break}case 84:{t=10;k=55;break}case 74:{t=11;k=55;break}case 81:{t=12;k=55;break}case 75:{t=13;k=55;break}case 65:{t=14;k=55;break}case 116:{t=10;k=55;break}case 106:{t=11;k=55;break}case 113:{t=12;k=55;break}case 107:{t=13;k=55;break}case 97:{t=14;k=55;break}default:switch(h<<24>>24){case 46:{u=r;v=s+1|0;break c;break}case 32:{u=r+1|0;v=0;break c;break}default:{u=r;v=s;break c}}}while(0);if((k|0)==55){k=0;d:do switch(l|0){case 2:{switch(r|0){case 0:{w=2;break d;break}case 1:{w=3;break d;break}default:{w=r+-2|0;break d}}break}case 1:{switch(r|0){case 0:{w=1;break d;break}case 3:{w=0;break d;break}default:{w=r+1|0;break d}}break}default:w=(r|0)==0?3:r+-1|0}while(0);h=d+(w<<4)+(s<<2)|0;c[h>>2]=(e[1224+(t<<1)>>1]|0)<<2|c[h>>2];u=r;v=s}q=q+1|0;if((q|0)>=80){j=1;k=68;break}else{r=u;s=v}}if((k|0)==68)return j|0;return 0}function lc(f,g,h,j,k,l,m){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;var n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0,Qa=0,Ra=0,Sa=0,Ta=0,Ua=0,Va=0,Wa=0,Xa=0,Ya=0,Za=0,_a=0,$a=0,ab=0,bb=0,cb=0,db=0,eb=0,fb=0,gb=0,hb=0,ib=0,jb=0,kb=0,lb=0,mb=0,nb=0,ob=0,pb=0,qb=0,rb=0,sb=0,tb=0,ub=0,vb=0,wb=0,xb=0,yb=0,zb=0,Ab=0,Bb=0,Cb=0,Db=0,Eb=0,Fb=0,Gb=0,Hb=0,Ib=0,Jb=0,Kb=0,Lb=0,Mb=0,Nb=0,Ob=0,Pb=0,Qb=0,Rb=0,Sb=0,Tb=0,Ub=0,Vb=0,Wb=0;n=i;i=i+16|0;o=n+8|0;p=n+4|0;q=n;c[p>>2]=0;c[q>>2]=0;a[l>>0]=1;r=m+(g<<2)|0;s=f+1476|0;t=c[s>>2]|0;if((c[r>>2]|0)==1)u=j-t|0;else u=2-j+(h>>2)+t|0;t=(k|0)==4;v=472+(g<<2)|0;w=440+(g<<2)|0;x=456+(g<<2)|0;a:do if(t){y=c[f+1484>>2]|0;z=c[v>>2]|0;if((y|0)==(z|0))if(!(b[f+(g<<3)>>1]|0))A=10;else{B=0;A=27}else if((((y|0)==(g|0)?(c[f+1516>>2]|0)==(z|0):0)?(d[f+40+(g<<2)>>0]|0)>1:0)?(d[f+40+(z<<2)>>0]|0)>1:0){C=0;A=28}else A=10;do if((A|0)==10){y=c[f+1492>>2]|0;if((y|0)==(z|0)){if(b[f+(g<<3)+2>>1]|0){B=1;A=27;break}}else if((((y|0)==(g|0)?(c[f+1524>>2]|0)==(z|0):0)?(d[f+40+(g<<2)+1>>0]|0)>1:0)?(d[f+40+(z<<2)+1>>0]|0)>1:0){C=1;A=28;break}y=c[f+1500>>2]|0;if((y|0)==(z|0)){if(b[f+(g<<3)+4>>1]|0){B=2;A=27;break}}else if((((y|0)==(g|0)?(c[f+1532>>2]|0)==(z|0):0)?(d[f+40+(g<<2)+2>>0]|0)>1:0)?(d[f+40+(z<<2)+2>>0]|0)>1:0){C=2;A=28;break}y=c[f+1508>>2]|0;if((y|0)==(z|0))if(!(b[f+(g<<3)+6>>1]|0)){D=1;E=0;F=0;G=0;H=-1;I=0;break a}else{B=3;A=27;break}if(!((y|0)==(g|0)?(c[f+1540>>2]|0)==(z|0):0)){D=1;E=0;F=0;G=0;H=-1;I=0;break a}if((d[f+40+(g<<2)+3>>0]|0)<=1){D=1;E=0;F=0;G=0;H=-1;I=0;break a}if((d[f+40+(z<<2)+3>>0]|0)>1){C=3;A=28}else{D=1;E=0;F=0;G=0;H=-1;I=0;break a}}while(0);if((A|0)==27){J=f+1480+(B<<3)|0;K=B}else if((A|0)==28){J=f+1512+(C<<3)|0;K=C}D=1;E=0;F=1;G=c[J>>2]|0;H=K;I=0}else{z=0;b:while(1){do if((z|0)!=(k|0)){y=c[f+1480+(z<<3)+4>>2]|0;L=c[v>>2]|0;if((y|0)==(L|0)){if(!(b[f+(g<<3)+(z<<1)>>1]|0))break;M=c[w>>2]|0;if((b[f+(M<<3)+(z<<1)>>1]|0)==0?(b[f+(M<<3)+(k<<1)>>1]|0)!=0:0)break;M=c[x>>2]|0;if(b[f+(M<<3)+(z<<1)>>1]|0){N=z;A=18;break b}if(!(b[f+(M<<3)+(k<<1)>>1]|0)){N=z;A=18;break b}else break}if((((y|0)==(g|0)?(c[f+1512+(z<<3)+4>>2]|0)==(L|0):0)?(d[f+40+(g<<2)+z>>0]|0)>1:0)?(d[f+40+(L<<2)+z>>0]|0)>1:0){L=c[w>>2]|0;if((b[f+(L<<3)+(z<<1)>>1]|0)==0?(b[f+(L<<3)+(k<<1)>>1]|0)!=0:0)break;L=c[x>>2]|0;if(b[f+(L<<3)+(z<<1)>>1]|0){O=z;A=26;break b}if(!(b[f+(L<<3)+(k<<1)>>1]|0)){O=z;A=26;break b}}}while(0);z=z+1|0;if((z|0)>=4){A=32;break}}if((A|0)==18){P=f+1480+(N<<3)|0;Q=N;A=31}else if((A|0)==26){P=f+1512+(O<<3)|0;Q=O;A=31}else if((A|0)==32){z=(k|0)!=4;L=z^1;if(!z){D=L;E=0;F=0;G=0;H=-1;I=0;break}if((b[f+(g<<3)+(k<<1)>>1]|0)!=0?(c[f+1480+(k<<3)+4>>2]|0)==(c[v>>2]|0):0){R=L;S=1;T=c[f+1480+(k<<3)>>2]|0;U=k}else{R=L;S=0;T=0;U=-1}}if((A|0)==31){R=0;S=1;T=c[P>>2]|0;U=Q}c[p>>2]=d[f+40+(c[w>>2]<<2)+k>>0];c[q>>2]=d[f+40+(c[x>>2]<<2)+k>>0];D=R;E=1;F=S;G=T;H=U;I=k}while(0);U=(k|0)==0;T=U&1;S=f+1480+(k<<3)+4|0;R=1224+(G<<1)|0;Q=f+72+(h<<3)+(H<<1)|0;P=(u|0)<2;O=f+72+(h<<3)+(k<<1)|0;N=f+40+(g<<2)+1|0;K=(k|0)==2;J=f+40+(g<<2)+3|0;C=f+40+(g<<2)+2|0;B=(k|0)==3;L=f+40+(g<<2)|0;z=(k|0)==1;y=(k|0)==2;M=f+40+(g<<2)+1|0;V=f+40+(g<<2)+3|0;W=(k|0)==3;X=f+40+(g<<2)+2|0;Y=(k|0)==1;Z=f+40+(g<<2)|0;_=F;F=0;$=0;aa=I;c:while(1){I=_^1;ba=F;ca=$;da=aa;while(1){ea=(ba|0)==0;fa=ca;ga=da;d:while(1){ha=c[w>>2]|0;ia=c[x>>2]|0;ja=c[v>>2]|0;ka=c[p>>2]|0;la=c[q>>2]|0;ma=la|ka;na=(ma|0)!=0;oa=f+40+(ja<<2)+1|0;pa=f+40+(ja<<2)+3|0;qa=f+40+(ja<<2)+2|0;ra=f+40+(ja<<2)|0;sa=f+40+(ja<<2)+1|0;ta=f+40+(ja<<2)+3|0;ua=f+40+(ja<<2)+2|0;va=f+40+(ja<<2)|0;wa=ga;while(1){xa=a[f+40+(g<<2)+wa>>0]|0;ya=xa&255;za=a[f+40+(ha<<2)+wa>>0]|0;Aa=a[f+40+(ia<<2)+wa>>0]|0;Ba=a[f+40+(ja<<2)+wa>>0]|0;Ca=Ba&255;Da=Aa|za;if(!((Ba|Da)<<24>>24)){Ea=(wa|0)==(k|0);Fa=D|Ea;if(xa<<24>>24){A=47;break}if(!Fa){Ga=fa;Ha=wa;A=44;break d}if(E&Ea){wa=T;continue}else{Ia=fa;Ja=wa;A=46;break d}}Ka=Da<<24>>24==0;La=(wa|0)==(k|0);Ma=La&(E&Ka);do if(Ma){Na=(xa&255)>(Ba&255)?xa:Ba;Oa=Na&255;e:do if((xa&255)<(Ba&255)|Na<<24>>24==0)Pa=Oa;else{do if(!U){if((a[L>>0]|0)!=0?(a[ra>>0]|0)==0:0)break;if(z)A=167;else A=164}else A=164;while(0);do if((A|0)==164){A=0;if((a[N>>0]|0)!=0?(a[oa>>0]|0)==0:0)break;if(K)A=170;else A=167}while(0);do if((A|0)==167){A=0;if((a[C>>0]|0)!=0?(a[qa>>0]|0)==0:0)break;if(B){Pa=Oa;break e}else A=170}while(0);if((A|0)==170){A=0;if(!(a[J>>0]|0)){Pa=Oa;break}if(a[pa>>0]|0){Pa=Oa;break}}Pa=Oa+1|0}while(0);if((Pa|0)>=(u|0)){Qa=Pa;A=154;break c}}else if(Ka){Oa=ya>>>0<=Ca>>>0?ya:Ca;if(t)if((Oa|0)<(u|0))break;else{Qa=Oa;A=154;break c}else if((Oa|0)<(u|0)|(La|na))break;else{Qa=Oa;A=154;break c}}while(0);do if(_){if(!((Da|xa)<<24>>24)){Ra=ma;Sa=Ca;Ta=La;Ua=fa;Va=wa;A=69;break d}if(!Ma){if(!Ka)break;Oa=ya>>>0<=Ca>>>0?ya:Ca;if(t)if((Oa|0)<(u|0))break;else{Qa=Oa;A=154;break c}else if((Oa|0)<(u|0)|(La|na))break;else{Qa=Oa;A=154;break c}}Oa=ya>>>0>=Ca>>>0?ya:Ca;f:do if((xa&255)>(Ba&255)|(Oa|0)<1)Wa=Oa;else{do if(!U){if((a[va>>0]|0)!=0?(a[Z>>0]|0)==0:0)break;if(Y)A=175;else A=172}else A=172;while(0);do if((A|0)==172){A=0;if((a[sa>>0]|0)!=0?(a[M>>0]|0)==0:0)break;if(y)A=178;else A=175}while(0);do if((A|0)==175){A=0;if((a[ua>>0]|0)!=0?(a[X>>0]|0)==0:0)break;if(W){Wa=Oa;break f}else A=178}while(0);if((A|0)==178){A=0;if(!(a[ta>>0]|0)){Wa=Oa;break}if(a[V>>0]|0){Wa=Oa;break}}Wa=Oa+1|0}while(0);if((Wa|0)>=(u|0)){Xa=Wa;A=85;break c}}while(0);if(c[f+1480+(wa<<3)>>2]|0){Ya=xa;Za=ya;_a=za;$a=Aa;ab=Ba;bb=Ca;cb=Ka;db=La;eb=wa;break}if(E&La)wa=T;else{fb=fa;gb=wa;A=92;break d}}if((A|0)==47){A=0;if(!Fa){hb=ma;ib=ya;jb=fa;kb=wa;A=48;break}ta=ya+fa|0;if((ta|0)>=(u|0)){Qa=ta;A=154;break c}if(E&Ea){fa=ta;ga=T;continue}else{lb=ta;mb=wa;A=54;break}}ta=_a&255;ua=$a&255;sa=ab<<24>>24==0;va=f+1480+(eb<<3)+4|0;na=c[va>>2]|0;g:do if((na|0)==(g|0))if(D|db){pa=nc(g,f,u,h,ta,ua,p,q,_,H,Za,bb,eb,fa,k,o)|0;switch(c[o>>2]|0){case 1:{Qa=pa;A=154;break c;break}case 2:{nb=pa;ob=db;pb=eb;A=98;break d;break}default:{qb=pa;break g}}}else{pa=mc(g,f,u,h,ta,ua,ka,la,Za,bb,eb,fa,o)|0;switch(c[o>>2]|0){case 1:{Qa=pa;A=154;break c;break}case 2:{rb=pa;sb=eb;A=96;break d;break}default:{qb=pa;break g}}}else if(!((na|0)!=(ja|0)|I))if(D|db){pa=pc(g,f,u,h,ta,ua,Za,bb,eb,fa,H,G,o,m)|0;switch(c[o>>2]|0){case 1:{Qa=pa;A=154;break c;break}case 2:{tb=pa;ub=db;vb=eb;A=104;break d;break}default:{qb=pa;break g}}}else{pa=oc(g,f,u,h,ta,ua,ka,la,Za,bb,eb,fa,H,G,o,m)|0;switch(c[o>>2]|0){case 1:{Qa=pa;A=154;break c;break}case 2:{wb=pa;xb=eb;A=102;break d;break}default:{qb=pa;break g}}}else qb=fa;while(0);do if(E?ea&((eb|0)!=(k|0)&Ya<<24>>24!=0):0){if(qb){la=c[va>>2]|0;if((la|0)==(g|0)){yb=eb;break}ka=c[v>>2]|0;if((la|0)==(ka|0)){yb=eb;break}la=c[S>>2]|0;if((la|0)!=(g|0)&(sa&(la|0)!=(ka|0)))zb=ka;else{yb=eb;break}}else{if(!sa){yb=eb;break}zb=c[v>>2]|0}if(a[f+40+(zb<<2)+k>>0]|0){ka=$a<<24>>24==0;if(!(ka?(a[f+40+(c[x>>2]<<2)+k>>0]|0)!=0:0)){if(_a<<24>>24){Ab=qb;Bb=eb;A=118;break d}if(!(a[f+40+(c[w>>2]<<2)+k>>0]|0)){Ab=qb;Bb=eb;A=118;break d}}if(cb){Cb=zb;Db=qb;Eb=eb;A=121;break d}if(!(_a<<24>>24)){Fb=zb;Gb=qb;Hb=eb;A=126;break d}if(ka){Ib=zb;Jb=qb;Kb=eb;A=133;break d}else yb=eb}else yb=eb}else yb=eb;while(0);if((qb|0)>=(u|0)){Qa=qb;A=154;break c}if(E&db){fa=qb;ga=T}else{Lb=qb;Mb=yb;A=141;break}}switch(A|0){case 44:{A=0;ga=Ha+1|0;Nb=ba;Ob=Ga;Pb=(ga|0)==(k|0)?Ha+2|0:ga;break}case 46:{A=0;Nb=ba;Ob=Ia;Pb=Ja+1|0;break}case 48:{A=0;if(hb){ga=kb+1|0;Nb=ba;Ob=jb;Pb=(ga|0)==(k|0)?kb+2|0:ga;break}ga=ib+jb|0;if((ga|0)>=(u|0)){Qa=ga;A=154;break c}fa=kb+1|0;Nb=ba;Ob=ga;Pb=(fa|0)==(k|0)?kb+2|0:fa;break}case 54:{A=0;fa=mb+1|0;Nb=ba;Ob=lb;Pb=E&(fa|0)==(k|0)?mb+2|0:fa;break}case 69:{A=0;if(D|Ta){fa=Sa+Ua|0;b[Q>>1]=b[Q>>1]|b[R>>1];if((fa|0)>=(u|0)){Qa=fa;A=154;break c}if(E&Ta){_=1;F=ba;$=fa;aa=T;continue c}ga=Va+1|0;Nb=ba;Ob=fa;Pb=E&(ga|0)==(k|0)?Va+2|0:ga;break}if(Ra){ga=Va+1|0;Nb=ba;Ob=Ua;Pb=(ga|0)==(k|0)?Va+2|0:ga;break}ga=Sa+Ua|0;b[Q>>1]=b[Q>>1]|b[R>>1];if((ga|0)>=(u|0)){Qa=ga;A=154;break c}fa=Va+1|0;Nb=ba;Ob=ga;Pb=(fa|0)==(k|0)?Va+2|0:fa;break}case 92:{A=0;fa=gb+1|0;Nb=ba;Ob=fb;Pb=E&(fa|0)==(k|0)?gb+2|0:fa;break}case 96:{A=0;fa=sb+1|0;Nb=ba;Ob=rb;Pb=(fa|0)==(k|0)?sb+2|0:fa;break}case 98:{A=0;Nb=ba;Ob=nb;Pb=E&ob?T:pb+1|0;break}case 102:{A=0;fa=xb+1|0;Nb=ba;Ob=wb;Pb=(fa|0)==(k|0)?xb+2|0:fa;break}case 104:{A=0;Nb=ba;Ob=tb;Pb=E&ub?T:vb+1|0;break}case 118:{A=0;if(P){Qa=1;A=154;break c}fa=Bb+1|0;Nb=1;Ob=Ab;Pb=(fa|0)==(k|0)?Bb+2|0:fa;break}case 121:{A=0;fa=b[f+(Cb<<3)+(k<<1)>>1]|0;if(((b[f+(c[x>>2]<<3)+(k<<1)>>1]|b[f+(c[w>>2]<<3)+(k<<1)>>1])&65535)<(fa&65535)){ga=c[1797444+((fa&65535)<<2)>>2]|0;if((ga|0)!=0?(b[O>>1]=b[O>>1]|b[1224+(ga<<1)>>1],P):0){Qa=1;A=154;break c}else Qb=1}else Qb=0;ga=Eb+1|0;Nb=Qb;Ob=Db;Pb=(ga|0)==(k|0)?Eb+2|0:ga;break}case 126:{A=0;ga=b[f+(Fb<<3)+(k<<1)>>1]|0;if((e[f+(c[w>>2]<<3)+(k<<1)>>1]|0)<(ga&65535)){fa=b[626]|0;if(!((fa&ga)<<16>>16)){ea=b[625]|0;if(!((ea&ga)<<16>>16)){sa=b[624]|0;if(!((sa&ga)<<16>>16)){va=b[623]|0;if(!((va&ga)<<16>>16)){ka=b[622]|0;if(!((ka&ga)<<16>>16)){la=b[621]|0;if(!((la&ga)<<16>>16)){ua=b[620]|0;if(!((ua&ga)<<16>>16)){ta=b[619]|0;if(!((ta&ga)<<16>>16)){ja=b[618]|0;if(!((ja&ga)<<16>>16)){na=b[617]|0;if(!((na&ga)<<16>>16)){wa=b[616]|0;if(!((wa&ga)<<16>>16)){ma=b[615]|0;if(!((ma&ga)<<16>>16)){pa=b[614]|0;if((pa&ga)<<16>>16){Rb=pa;A=129}}else{Rb=ma;A=129}}else{Rb=wa;A=129}}else{Rb=na;A=129}}else{Rb=ja;A=129}}else{Rb=ta;A=129}}else{Rb=ua;A=129}}else{Rb=la;A=129}}else{Rb=ka;A=129}}else{Rb=va;A=129}}else{Rb=sa;A=129}}else{Rb=ea;A=129}}else{Rb=fa;A=129}if((A|0)==129){A=0;b[O>>1]=b[O>>1]|Rb}if(P){Qa=1;A=154;break c}else Sb=1}else Sb=0;fa=Hb+1|0;Nb=Sb;Ob=Gb;Pb=(fa|0)==(k|0)?Hb+2|0:fa;break}case 133:{A=0;fa=b[f+(Ib<<3)+(k<<1)>>1]|0;if((e[f+(c[x>>2]<<3)+(k<<1)>>1]|0)<(fa&65535)){ea=b[626]|0;if(!((ea&fa)<<16>>16)){sa=b[625]|0;if(!((sa&fa)<<16>>16)){va=b[624]|0;if(!((va&fa)<<16>>16)){ka=b[623]|0;if(!((ka&fa)<<16>>16)){la=b[622]|0;if(!((la&fa)<<16>>16)){ua=b[621]|0;if(!((ua&fa)<<16>>16)){ta=b[620]|0;if(!((ta&fa)<<16>>16)){ja=b[619]|0;if(!((ja&fa)<<16>>16)){na=b[618]|0;if(!((na&fa)<<16>>16)){wa=b[617]|0;if(!((wa&fa)<<16>>16)){ma=b[616]|0;if(!((ma&fa)<<16>>16)){pa=b[615]|0;if(!((pa&fa)<<16>>16)){ga=b[614]|0;if((ga&fa)<<16>>16){Tb=ga;A=136}}else{Tb=pa;A=136}}else{Tb=ma;A=136}}else{Tb=wa;A=136}}else{Tb=na;A=136}}else{Tb=ja;A=136}}else{Tb=ta;A=136}}else{Tb=ua;A=136}}else{Tb=la;A=136}}else{Tb=ka;A=136}}else{Tb=va;A=136}}else{Tb=sa;A=136}}else{Tb=ea;A=136}if((A|0)==136){A=0;b[O>>1]=b[O>>1]|Tb}if(P){Qa=1;A=154;break c}else Ub=1}else Ub=0;ea=Kb+1|0;Nb=Ub;Ob=Jb;Pb=(ea|0)==(k|0)?Kb+2|0:ea;break}case 141:{A=0;ea=Mb+1|0;Nb=ba;Ob=Lb;Pb=E&(ea|0)==(k|0)?Mb+2|0:ea;break}}if((Pb|0)<4){ba=Nb;ca=Ob;da=Pb}else{Vb=Ob;A=143;break c}}}if((A|0)==85){b[Q>>1]=b[Q>>1]|b[R>>1];Qa=Xa;i=n;return Qa|0}else if((A|0)==143){do if(!Vb){if(!t?(c[S>>2]|0)!=-1:0)break;if((c[f+1484>>2]|0)!=-1?(a[f+40+(g<<2)>>0]|0)!=0:0)b[f+72+(h<<3)>>1]=b[1224+(c[f+1480>>2]<<1)>>1]|0;if((c[f+1492>>2]|0)!=-1?(a[f+40+(g<<2)+1>>0]|0)!=0:0)b[f+72+(h<<3)+2>>1]=b[1224+(c[f+1488>>2]<<1)>>1]|0;if((c[f+1500>>2]|0)!=-1?(a[f+40+(g<<2)+2>>0]|0)!=0:0)b[f+72+(h<<3)+4>>1]=b[1224+(c[f+1496>>2]<<1)>>1]|0;if((c[f+1508>>2]|0)!=-1?(a[f+40+(g<<2)+3>>0]|0)!=0:0)b[f+72+(h<<3)+6>>1]=b[1224+(c[f+1504>>2]<<1)>>1]|0;Xa=c[s>>2]|0;if((c[r>>2]|0)==1)Wb=2-j+(h>>2)+Xa|0;else Wb=j-Xa|0;if((Wb|0)<2){Qa=0;i=n;return Qa|0}}while(0);a[l>>0]=0;Qa=Vb;i=n;return Qa|0}else if((A|0)==154){i=n;return Qa|0}return 0}function mc(a,d,e,f,g,h,i,j,k,l,m,n,o){a=a|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;var p=0,q=0,r=0,s=0,t=0,u=0;c[o>>2]=1;if((g|0)!=0|(i|0)==0?(h|0)!=0|(j|0)==0:0){p=d+72+(f<<3)+(m<<1)|0;b[p>>1]=b[p>>1]|b[1224+(c[d+1480+(m<<3)>>2]<<1)>>1];p=n+1|0;if((p|0)>=(e|0)){q=p;return q|0}if((j|i|0)==0&((g|0)<2&(h|0)<2&(l|0)<2)){r=n+k|0;if((r|0)>=(e|0)){q=r;return q|0}c[o>>2]=2;q=r;return q|0}else s=p}else s=n;n=d+1512+(m<<3)|0;p=c[d+1512+(m<<3)+4>>2]|0;if((p|0)==(a|0))if(!(j|i)){r=d+72+(f<<3)+(m<<1)|0;b[r>>1]=b[r>>1]|b[1224+(c[n>>2]<<1)>>1];r=s+1|0;if((r|0)>=(e|0)){q=r;return q|0}if((g|0)<3&(h|0)<3&(l|0)<3){t=k+-2+r|0;if((t|0)>=(e|0)){q=t;return q|0}c[o>>2]=2;q=t;return q|0}else u=r}else u=s;else if((j|i|0)==0&((l|0)>1&((k|0)>1?(p|0)==(c[472+(a<<2)>>2]|0):0))){a=d+72+(f<<3)+(m<<1)|0;b[a>>1]=b[a>>1]|b[1224+(c[n>>2]<<1)>>1];n=s+1|0;if((n|0)>=(e|0)){q=n;return q|0}if((g|0)<3&(h|0)<3?(k|0)<3|(l|0)<3:0){h=((k|0)>=(l|0)?k:l)+-2+n|0;if((h|0)>=(e|0)){q=h;return q|0}c[o>>2]=2;q=h;return q|0}else u=n}else u=s;c[o>>2]=0;q=u;return q|0}function nc(a,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r){a=a|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;var s=0,t=0,u=0,v=0,w=0;c[r>>2]=1;s=d+72+(f<<3)+(o<<1)|0;f=b[s>>1]|b[1224+(c[d+1480+(o<<3)>>2]<<1)>>1];b[s>>1]=f;t=p+1|0;if((t|0)>=(e|0)){u=t;return u|0}v=(o|0)==(l|0)&k|(q|0)!=(o|0);if(!v){q=c[i>>2]|0;c[i>>2]=(q|0)<2?0:q+-1|0;q=c[j>>2]|0;c[j>>2]=(q|0)<2?0:q+-1|0}if((g|0)<2&(h|0)<2&(n|0)<2){q=p+m|0;if((q|0)>=(e|0)){u=q;return u|0}c[r>>2]=2;u=q;return u|0}q=d+1512+(o<<3)|0;k=c[d+1512+(o<<3)+4>>2]|0;if((k|0)==(a|0)){b[s>>1]=f|b[1224+(c[q>>2]<<1)>>1];o=p+2|0;if((o|0)>=(e|0)){u=o;return u|0}if(!v){d=c[i>>2]|0;c[i>>2]=(d|0)<2?0:d+-1|0;d=c[j>>2]|0;c[j>>2]=(d|0)<2?0:d+-1|0}if((g|0)<3&(h|0)<3&(n|0)<3){d=p+m|0;if((d|0)>=(e|0)){u=d;return u|0}c[r>>2]=2;u=d;return u|0}else w=o}else if((n|0)>1&((m|0)>1?(k|0)==(c[472+(a<<2)>>2]|0):0)){b[s>>1]=f|b[1224+(c[q>>2]<<1)>>1];q=p+2|0;if((q|0)>=(e|0)){u=q;return u|0}if(!v){v=c[i>>2]|0;c[i>>2]=(v|0)<2?0:v+-1|0;v=c[j>>2]|0;c[j>>2]=(v|0)<2?0:v+-1|0}if((g|0)<3&(h|0)<3?(m|0)<3|(n|0)<3:0){h=((m|0)>=(n|0)?m:n)+p|0;if((h|0)>=(e|0)){u=h;return u|0}c[r>>2]=2;u=h;return u|0}else w=q}else w=t;c[r>>2]=0;u=w;return u|0}function oc(d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;r=r|0;s=s|0;var t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0;c[r>>2]=1;t=(j|0)==0;if((h|0)!=0|t?(i|0)!=0|(k|0)==0:0){u=e+72+(g<<3)+(n<<1)|0;b[u>>1]=b[u>>1]|b[1224+(c[e+1480+(n<<3)>>2]<<1)>>1];u=e+72+(g<<3)+(p<<1)|0;b[u>>1]=b[u>>1]|b[1224+(q<<1)>>1];u=o+1|0;if((u|0)>=(f|0)){v=u;return v|0}if((k|j|0)==0&((h|0)<2&(i|0)<2&(l|0)<2)){w=o+m|0;if((w|0)>=(f|0)){v=w;return v|0}c[r>>2]=2;v=w;return v|0}else x=u}else x=o;o=e+1512+(n<<3)|0;u=c[e+1512+(n<<3)+4>>2]|0;w=c[472+(d<<2)>>2]|0;do if((u|0)==(w|0))if(!(k|j)){y=e+72+(g<<3)+(n<<1)|0;b[y>>1]=b[y>>1]|b[1224+(c[o>>2]<<1)>>1];y=e+72+(g<<3)+(p<<1)|0;b[y>>1]=b[y>>1]|b[1224+(q<<1)>>1];y=x+1|0;if((y|0)>=(f|0)){v=y;return v|0}if((h|0)<3&(i|0)<3&(l|0)<3){z=m+-2+y|0;if((z|0)>=(f|0)){v=z;return v|0}c[r>>2]=2;v=z;return v|0}else A=y}else A=x;else if((l|0)>1&((m|0)>1&(u|0)==(d|0))){if(k|j){A=x;break}y=e+72+(g<<3)+(n<<1)|0;b[y>>1]=b[y>>1]|b[1224+(c[o>>2]<<1)>>1];y=e+72+(g<<3)+(p<<1)|0;b[y>>1]=b[y>>1]|b[1224+(q<<1)>>1];y=x+1|0;if((y|0)>=(f|0)){v=y;return v|0}if(!((h|0)<3&(i|0)<3)){A=y;break}if(!((l|0)<3|(m|0)<3)){A=y;break}z=((m|0)>=(l|0)?m:l)+-2+y|0;if((z|0)>=(f|0)){v=z;return v|0}c[r>>2]=2;v=z;return v|0}else{if((n|0)!=(p|0)){A=x;break}if(!(((h|0)>1|t)&(u|0)==(c[440+(d<<2)>>2]|0))){A=x;break}if(!((i|0)>1|(k|0)==0)){A=x;break}z=(b[e+24+(n<<1)>>1]|(b[e+16+(n<<1)>>1]|(b[e+8+(n<<1)>>1]|b[e+(n<<1)>>1])))&65535;if((a[s+4944+(z*120|0)+24+(n<<1)+1>>0]|0)!=(w|0)){A=x;break}y=e+72+(g<<3)+(n<<1)|0;B=b[y>>1]|b[1224+(a[s+4944+(z*120|0)+24+(n<<1)>>0]<<1)>>1];b[y>>1]=B;b[y>>1]=B|b[1224+(q<<1)>>1];B=x+1|0;if((B|0)>=(f|0)){v=B;return v|0}if(!((k|j|0)==0&((i|0)<3&((h|0)<3&(l|0)<3)))){A=B;break}y=m+-2+B|0;if((y|0)<(f|0)){A=y;break}else v=y;return v|0}while(0);c[r>>2]=0;v=A;return v|0}function pc(d,e,f,g,h,i,j,k,l,m,n,o,p,q){d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;l=l|0;m=m|0;n=n|0;o=o|0;p=p|0;q=q|0;var r=0,s=0,t=0,u=0,v=0,w=0,x=0;c[p>>2]=1;r=e+72+(g<<3)+(l<<1)|0;b[r>>1]=b[r>>1]|b[1224+(c[e+1480+(l<<3)>>2]<<1)>>1];s=e+72+(g<<3)+(n<<1)|0;b[s>>1]=b[s>>1]|b[1224+(o<<1)>>1];o=m+1|0;if((o|0)>=(f|0)){t=o;return t|0}if((h|0)<2&(i|0)<2&(j|0)<2){s=m+k|0;if((s|0)>=(f|0)){t=s;return t|0}c[p>>2]=2;t=s;return t|0}s=e+1512+(l<<3)|0;g=c[e+1512+(l<<3)+4>>2]|0;u=c[472+(d<<2)>>2]|0;do if((g|0)==(u|0)){b[r>>1]=b[r>>1]|b[1224+(c[s>>2]<<1)>>1];v=m+2|0;if((v|0)>=(f|0)){t=v;return t|0}if((h|0)<3&(i|0)<3&(j|0)<3){w=m+k|0;if((w|0)>=(f|0)){t=w;return t|0}c[p>>2]=2;t=w;return t|0}else x=v}else{if(!((j|0)>1&((k|0)>1&(g|0)==(d|0)))){if((l|0)!=(n|0)){x=o;break}if((g|0)!=(c[440+(d<<2)>>2]|0)){x=o;break}v=(b[e+24+(l<<1)>>1]|(b[e+16+(l<<1)>>1]|(b[e+8+(l<<1)>>1]|b[e+(l<<1)>>1])))&65535;if((a[q+4944+(v*120|0)+24+(l<<1)+1>>0]|0)!=(u|0)){x=o;break}b[r>>1]=b[r>>1]|b[1224+(a[q+4944+(v*120|0)+24+(l<<1)>>0]<<1)>>1];v=m+2|0;if((v|0)<(f|0)){x=v;break}else t=v;return t|0}b[r>>1]=b[r>>1]|b[1224+(c[s>>2]<<1)>>1];v=m+2|0;if((v|0)>=(f|0)){t=v;return t|0}if((h|0)<3&(i|0)<3?(j|0)<3|(k|0)<3:0){w=((k|0)>=(j|0)?k:j)+m|0;if((w|0)>=(f|0)){t=w;return t|0}c[p>>2]=2;t=w;return t|0}else x=v}while(0);c[p>>2]=0;t=x;return t|0}function qc(d,f,g,h,i,j){d=d|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;if((c[j+16>>2]|0)==(g|0)){k=0;return k|0}l=g+1|0;m=c[d+672+(l<<4)>>2]|0;n=d+(f<<3)+(m<<1)|0;o=b[n>>1]|0;p=472+(f<<2)|0;q=b[d+(c[p>>2]<<3)+(m<<1)>>1]|0;r=d+(g+9<<3)|0;s=r;b[s>>1]=0;b[s+2>>1]=0>>>16;s=r+4|0;b[s>>1]=0;b[s+2>>1]=0>>>16;s=q|o;o=(i|0)!=4;q=(m|0)==(i|0)|o^1;do if(!q){if(!((b[n>>1]|0)==0?(b[d+(f<<3)+(i<<1)>>1]|0)!=0:0)){r=c[p>>2]|0;if(b[d+(r<<3)+(m<<1)>>1]|0){t=9;break}if(!(b[d+(r<<3)+(i<<1)>>1]|0)){t=9;break}}r=c[440+(f<<2)>>2]|0;if((b[d+(r<<3)+(m<<1)>>1]|0)==0?(b[d+(r<<3)+(i<<1)>>1]|0)!=0:0){k=0;return k|0}}else t=9;while(0);if((t|0)==9){r=s&65535;u=c[440+(f<<2)>>2]|0;v=b[d+(u<<3)+(m<<1)>>1]|0;if((s&65535)<=((v|b[1224+(c[d+672+(l<<4)+4>>2]<<1)>>1])&65535)){k=0;return k|0}if(!q?(v<<16>>16==0?(b[d+(u<<3)+(i<<1)>>1]|0)!=0:0):0){k=0;return k|0}b[d+72+(g<<3)+(m<<1)>>1]=b[1224+(c[1797444+(r<<2)>>2]<<1)>>1]|0}r=c[d+1476>>2]|0;if((c[j+(f<<2)>>2]|0)==1)w=h-r|0;else w=3-h+(g>>2)+r|0;r=(w|0)<2;if(o|r){k=r;return k|0}r=c[p>>2]|0;p=(e[n>>1]|0)>(e[d+(r<<3)+(m<<1)>>1]|0)?f:r;if(((c[d+1480+(m<<3)+4>>2]|0)==(p|0)?(r=c[d+1512+(m<<3)>>2]|0,(r|0)!=0):0)?(c[d+1512+(m<<3)+4>>2]|0)==(p|0):0){f=d+72+(g<<3)+(m<<1)|0;b[f>>1]=b[f>>1]|b[1224+(r<<1)>>1];if((w|0)<3){k=1;return k|0}else x=2}else x=1;r=440+(p<<2)|0;f=456+(p<<2)|0;n=472+(p<<2)|0;o=x;x=0;a:while(1){do if((x|0)!=(m|0)?(a[d+40+(p<<2)+x>>0]|0)!=0:0){if(((a[d+40+(c[r>>2]<<2)+x>>0]|0)==0?(a[d+40+(c[f>>2]<<2)+x>>0]|0)==0:0)?(a[d+40+(c[n>>2]<<2)+x>>0]|0)==0:0){h=(c[1862980+(e[d+(p<<3)+(x<<1)>>1]<<2)>>2]|0)+o|0;if((h|0)<(w|0)){y=h;break}else{k=1;t=32;break a}}h=c[d+1480+(x<<3)>>2]|0;if((h|0)!=0?(c[d+1480+(x<<3)+4>>2]|0)==(p|0):0){j=o+1|0;i=d+72+(g<<3)+(x<<1)|0;b[i>>1]=b[i>>1]|b[1224+(h<<1)>>1];if((j|0)<(w|0))y=j;else{k=1;t=32;break a}}else y=o}else y=o;while(0);x=x+1|0;if((x|0)>=4){k=0;t=32;break}else o=y}if((t|0)==32)return k|0;return 0}function rc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0;b=0;do{d=(b&4096|0)==0?0:13;e=(b&2048|0)==0?d:d+8|0;d=(b&1024|0)==0;f=e+4|0;g=d?e:f;h=b&512;c[a+38048+(b<<2)>>2]=(b&256|0)==0?((h|0)==0?(d?e:f):g|2):(h>>>8|g)+1|0;b=b+1|0}while((b|0)!=8192);c[a+38044>>2]=0;return}function sc(a){a=a|0;return}function tc(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;e=0;do{c[a+(e*112|0)>>2]=-1;e=e+1|0}while((e|0)!=200);c[a+27200>>2]=0;c[a+27204>>2]=0;e=0;do{c[a+27212+(e*12|0)>>2]=-1;e=e+1|0}while((e|0)!=128);f=0;do{c[a+28748+(f*12|0)>>2]=-1;f=f+1|0}while((f|0)!=128);g=0;do{c[a+30284+(g*12|0)>>2]=-1;g=g+1|0}while((g|0)!=128);h=0;do{c[a+31820+(h*12|0)>>2]=-1;h=h+1|0}while((h|0)!=128);i=0;do{c[a+33356+(i*12|0)>>2]=-1;i=i+1|0}while((i|0)!=128);j=0;do{c[a+34892+(j*12|0)>>2]=-1;j=j+1|0}while((j|0)!=128);c[a+38032>>2]=-1;c[a+38036>>2]=-1;c[a+27208>>2]=-1;c[a+38044>>2]=c[b>>2];uc(a,b);vc(a);switch(d|0){case 1:{wc(a);return}case 2:{xc(a);return}case 3:{yc(a);return}default:return}}function uc(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;d=a+38044|0;if((c[d>>2]|0)<=0)return;e=a+27200|0;f=0;do{g=c[b+4+(f*96|0)>>2]|0;h=b+4+(f*96|0)+32|0;i=b+4+(f*96|0)+52|0;j=b+4+(f*96|0)+72|0;k=b+4+(f*96|0)+92|0;l=c[i>>2]^c[h>>2]^c[j>>2]^c[k>>2];m=b+4+(f*96|0)+48|0;n=b+4+(f*96|0)+68|0;o=b+4+(f*96|0)+88|0;p=b+4+(f*96|0)+44|0;c[a+(f*112|0)+4>>2]=c[n>>2]<<11^c[m>>2]<<17^c[o>>2]<<5^(c[p>>2]|0)>>>2;c[a+(f*112|0)+8>>2]=c[h>>2];q=b+4+(f*96|0)+36|0;c[a+(f*112|0)+12>>2]=c[q>>2];r=b+4+(f*96|0)+40|0;c[a+(f*112|0)+16>>2]=c[r>>2];c[a+(f*112|0)+20>>2]=c[p>>2];c[a+(f*112|0)+24>>2]=c[m>>2];c[a+(f*112|0)+28>>2]=c[i>>2];s=b+4+(f*96|0)+56|0;c[a+(f*112|0)+32>>2]=c[s>>2];t=b+4+(f*96|0)+60|0;c[a+(f*112|0)+36>>2]=c[t>>2];u=b+4+(f*96|0)+64|0;c[a+(f*112|0)+40>>2]=c[u>>2];c[a+(f*112|0)+44>>2]=c[n>>2];c[a+(f*112|0)+48>>2]=c[j>>2];v=b+4+(f*96|0)+76|0;c[a+(f*112|0)+52>>2]=c[v>>2];w=b+4+(f*96|0)+80|0;c[a+(f*112|0)+56>>2]=c[w>>2];x=b+4+(f*96|0)+84|0;c[a+(f*112|0)+60>>2]=c[x>>2];c[a+(f*112|0)+64>>2]=c[o>>2];c[a+(f*112|0)+68>>2]=c[k>>2];c[a+(f*112|0)+72>>2]=(g|0)==4&1;c[a+(f*112|0)+76>>2]=c[b+4+(f*96|0)+4>>2];c[a+(f*112|0)+80>>2]=g;y=(c[h>>2]|0)>>>2;h=(c[q>>2]|0)>>>2;q=(c[r>>2]|0)>>>2;r=(c[p>>2]|0)>>>2;p=(c[1895748+(y*116|0)>>2]|0)+4+(c[1895748+(h*116|0)>>2]|0)+(c[1895748+(q*116|0)>>2]|0)+(c[1895748+(r*116|0)>>2]|0)|0;z=p+($(((h|0)==0&1)+((y|0)==0&1)+((q|0)==0&1)+((r|0)==0&1)|0,p)|0)|0;p=(c[m>>2]|0)>>>2;m=(c[i>>2]|0)>>>2;i=(c[s>>2]|0)>>>2;s=(c[t>>2]|0)>>>2;t=(c[1895748+(p*116|0)>>2]|0)+4+(c[1895748+(m*116|0)>>2]|0)+(c[1895748+(i*116|0)>>2]|0)+(c[1895748+(s*116|0)>>2]|0)|0;r=t+z+($(((m|0)==0&1)+((p|0)==0&1)+((i|0)==0&1)+((s|0)==0&1)|0,t)|0)|0;t=(c[u>>2]|0)>>>2;u=(c[n>>2]|0)>>>2;n=(c[j>>2]|0)>>>2;j=(c[v>>2]|0)>>>2;v=(c[1895748+(t*116|0)>>2]|0)+4+(c[1895748+(u*116|0)>>2]|0)+(c[1895748+(n*116|0)>>2]|0)+(c[1895748+(j*116|0)>>2]|0)|0;s=v+r+($(((u|0)==0&1)+((t|0)==0&1)+((n|0)==0&1)+((j|0)==0&1)|0,v)|0)|0;v=(c[w>>2]|0)>>>2;w=(c[x>>2]|0)>>>2;x=(c[o>>2]|0)>>>2;o=(c[k>>2]|0)>>>2;k=(c[1895748+(v*116|0)>>2]|0)+4+(c[1895748+(w*116|0)>>2]|0)+(c[1895748+(x*116|0)>>2]|0)+(c[1895748+(o*116|0)>>2]|0)|0;j=(l>>>2^l>>>6)&127;c[a+(f*112|0)+96>>2]=k+s+($(((w|0)==0&1)+((v|0)==0&1)+((x|0)==0&1)+((o|0)==0&1)|0,k)|0);k=a+27212+(g*1536|0)+(j*12|0)|0;if((c[k>>2]|0)==-1){c[k>>2]=f;c[a+27212+(g*1536|0)+(j*12|0)+4>>2]=f;c[a+27212+(g*1536|0)+(j*12|0)+8>>2]=1;c[a+22400+((c[e>>2]|0)*24|0)>>2]=g;k=c[e>>2]|0;c[a+22400+(k*24|0)+4>>2]=j;c[e>>2]=k+1}else{k=a+27212+(g*1536|0)+(j*12|0)+4|0;c[a+((c[k>>2]|0)*112|0)>>2]=f;c[k>>2]=f;k=a+27212+(g*1536|0)+(j*12|0)+8|0;c[k>>2]=(c[k>>2]|0)+1}f=f+1|0}while((f|0)<(c[d>>2]|0));return}function vc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0;b=a+27200|0;d=c[b>>2]|0;if((d|0)<=0)return;e=a+38028|0;f=a+36428|0;g=a+27204|0;h=0;do{i=c[a+22400+(h*24|0)>>2]|0;j=c[a+22400+(h*24|0)+4>>2]|0;k=a+27212+(i*1536|0)+(j*12|0)|0;l=a+27212+(i*1536|0)+(j*12|0)+8|0;m=c[l>>2]|0;a:do switch(m|0){case 1:break;case 2:{n=c[k>>2]|0;o=a+(n*112|0)|0;p=c[o>>2]|0;if((c[a+(n*112|0)+4>>2]|0)==(c[a+(p*112|0)+4>>2]|0)){q=0;r=1;while(1){if(!(r&1))s=r;else{t=r;u=0;while(1){v=(c[a+(n*112|0)+8+(q<<4)+(u<<2)>>2]|0)==(c[a+(p*112|0)+8+(q<<4)+(u<<2)>>2]|0)?t:0;u=u+1|0;if(!((u|0)<4&(v&1)!=0)){s=v;break}else t=v}}q=q+1|0;w=(s&1)!=0;if(!((q|0)<4&w))break;else r=s}if(w)break a}c[o>>2]=-1;c[a+27212+(i*1536|0)+(j*12|0)+4>>2]=c[k>>2];c[l>>2]=1;r=c[g>>2]|0;c[a+34892+(r*12|0)>>2]=p;c[a+34892+(r*12|0)+4>>2]=p;c[a+34892+(r*12|0)+8>>2]=1;c[a+22400+((c[b>>2]|0)*24|0)>>2]=5;r=c[b>>2]|0;c[a+22400+(r*24|0)+4>>2]=c[g>>2];c[b>>2]=r+1;c[g>>2]=(c[g>>2]|0)+1;break}default:{c[e>>2]=m;if((m|0)>0){r=0;q=k;while(1){n=c[q>>2]|0;c[a+36428+(r<<3)>>2]=n;c[a+36428+(r<<3)+4>>2]=c[a+(n*112|0)+4>>2];r=r+1|0;t=c[e>>2]|0;if((r|0)>=(t|0)){x=t;break}else q=a+(n*112|0)|0}if((x|0)>1){q=1;do{r=c[a+36428+(q<<3)>>2]|0;p=c[a+36428+(q<<3)+4>>2]|0;o=q;while(1){n=o;o=o+-1|0;if((p|0)<=(c[a+36428+(o<<3)+4>>2]|0)){y=n;break}t=a+36428+(o<<3)|0;u=c[t+4>>2]|0;v=a+36428+(n<<3)|0;c[v>>2]=c[t>>2];c[v+4>>2]=u;if(!o){y=0;break}}c[a+36428+(y<<3)>>2]=r;c[a+36428+(y<<3)+4>>2]=p;q=q+1|0;o=c[e>>2]|0}while((q|0)<(o|0));z=o}else z=x}else z=m;q=z+-1|0;o=0;while(1){if((o|0)>=(q|0)){A=o;break}u=o+1|0;if(zc(a,c[a+36428+(o<<3)>>2]|0,c[a+36428+(u<<3)>>2]|0)|0)o=u;else{A=o;break}}if((A|0)!=(q|0)){o=c[f>>2]|0;c[k>>2]=o;c[a+27212+(i*1536|0)+(j*12|0)+4>>2]=c[a+36428+(A<<3)>>2];u=A+1|0;c[l>>2]=u;if((A|0)>0){v=0;t=o;while(1){v=v+1|0;n=c[a+36428+(v<<3)>>2]|0;c[a+(t*112|0)>>2]=n;if((v|0)==(A|0)){B=n;break}else t=n}}else B=o;c[a+(B*112|0)>>2]=-1;t=c[e>>2]|0;if((u|0)<(t|0)){v=t;t=u;q=k;while(1){n=c[a+36428+(t<<3)>>2]|0;C=c[a+36428+(t+-1<<3)>>2]|0;if(zc(a,n,C)|0){c[a+(C*112|0)>>2]=n;c[a+(n*112|0)>>2]=-1;c[q+4>>2]=n;C=q+8|0;c[C>>2]=(c[C>>2]|0)+1;D=v;E=q}else{c[a+(n*112|0)>>2]=-1;C=c[g>>2]|0;F=a+34892+(C*12|0)|0;c[F>>2]=n;c[a+34892+(C*12|0)+4>>2]=n;c[a+34892+(C*12|0)+8>>2]=1;c[a+22400+((c[b>>2]|0)*24|0)>>2]=5;C=c[b>>2]|0;c[a+22400+(C*24|0)+4>>2]=c[g>>2];c[b>>2]=C+1;c[g>>2]=(c[g>>2]|0)+1;D=c[e>>2]|0;E=F}t=t+1|0;if((t|0)>=(D|0))break;else{v=D;q=E}}}}}}while(0);h=h+1|0}while((h|0)!=(d|0));return}function wc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0.0,x=0.0,y=0.0,z=0.0,A=0,B=0,C=0;b=i;i=i+16|0;d=b;e=a+27200|0;if((c[e>>2]|0)>0)f=0;else{g=d;i=b;return}do{j=c[a+27212+((c[a+22400+(f*24|0)>>2]|0)*1536|0)+((c[a+22400+(f*24|0)+4>>2]|0)*12|0)>>2]|0;k=a+22400+(f*24|0)+8|0;c[k>>2]=0;l=a+(j*112|0)+72|0;m=0;n=-1;o=j;p=0;while(1){q=c[a+(o*112|0)+76>>2]|0;if((q|0)==(n|0)){r=m;s=n;t=p}else{u=m+(c[608+(c[l>>2]<<5)+(p<<2)>>2]|0)|0;c[k>>2]=u;r=u;s=q;t=((p|0)<7&1)+p|0}o=c[a+(o*112|0)>>2]|0;if((o|0)==-1){v=r;break}else{m=r;n=s;p=t}}p=c[l>>2]|0;w=+(c[a+(j*112|0)+96>>2]|0);x=+h[8+(p*40|0)>>3];do if(!(w<x)){y=+h[8+(p*40|0)+8>>3];if(w<y){z=(w-x)*+h[8+(p*40|0)+16>>3];break}else{z=+h[8+(p*40|0)+24>>3]*+Y(+((w-y)/+h[8+(p*40|0)+32>>3]));break}}else z=0.0;while(0);c[k>>2]=~~(z*+(v|0));f=f+1|0;p=c[e>>2]|0}while((f|0)<(p|0));A=p;if((A|0)<=0){g=d;i=b;return}A=a+22400|0;f=0;while(1){v=a+22400+(f*24|0)|0;t=c[v>>2]|0;s=c[v+4>>2]|0;v=c[a+22400+(f*24|0)+8>>2]|0;r=a+22400+(f*24|0)+12|0;c[d>>2]=c[r>>2];c[d+4>>2]=c[r+4>>2];c[d+8>>2]=c[r+8>>2];a:do if(!f){B=A;C=0}else{r=f;while(1){p=r;r=r+-1|0;j=a+22400+(p*24|0)|0;if((v|0)<=(c[a+22400+(r*24|0)+8>>2]|0)){B=j;C=p;break a}p=a+22400+(r*24|0)|0;c[j>>2]=c[p>>2];c[j+4>>2]=c[p+4>>2];c[j+8>>2]=c[p+8>>2];c[j+12>>2]=c[p+12>>2];c[j+16>>2]=c[p+16>>2];c[j+20>>2]=c[p+20>>2];if(!r){B=A;C=0;break}}}while(0);k=B;c[k>>2]=t;c[k+4>>2]=s;c[a+22400+(C*24|0)+8>>2]=v;k=a+22400+(C*24|0)+12|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];f=f+1|0;if((f|0)>=(c[e>>2]|0)){g=d;break}}i=b;return}function xc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,j=0,k=0,l=0,m=0.0,n=0.0,o=0.0,p=0.0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;b=i;i=i+16|0;d=b;e=a+27200|0;if((c[e>>2]|0)>0)f=0;else{g=d;i=b;return}do{j=c[a+27212+((c[a+22400+(f*24|0)>>2]|0)*1536|0)+((c[a+22400+(f*24|0)+4>>2]|0)*12|0)>>2]|0;k=a+22400+(f*24|0)+8|0;c[k>>2]=272e3;l=c[a+(j*112|0)+72>>2]|0;m=+(c[a+(j*112|0)+96>>2]|0);n=+h[88+(l*40|0)>>3];do if(!(m<n)){o=+h[88+(l*40|0)+8>>3];if(m<o){p=(m-n)*+h[88+(l*40|0)+16>>3];break}else{p=+h[88+(l*40|0)+24>>3]*+Y(+((m-o)/+h[88+(l*40|0)+32>>3]));break}}else p=0.0;while(0);c[k>>2]=~~(p*272.0e3);f=f+1|0;l=c[e>>2]|0}while((f|0)<(l|0));q=l;if((q|0)<=0){g=d;i=b;return}q=a+22400|0;f=0;while(1){l=a+22400+(f*24|0)|0;j=c[l>>2]|0;r=c[l+4>>2]|0;l=c[a+22400+(f*24|0)+8>>2]|0;s=a+22400+(f*24|0)+12|0;c[d>>2]=c[s>>2];c[d+4>>2]=c[s+4>>2];c[d+8>>2]=c[s+8>>2];a:do if(!f){t=q;u=0}else{s=f;while(1){v=s;s=s+-1|0;w=a+22400+(v*24|0)|0;if((l|0)<=(c[a+22400+(s*24|0)+8>>2]|0)){t=w;u=v;break a}v=a+22400+(s*24|0)|0;c[w>>2]=c[v>>2];c[w+4>>2]=c[v+4>>2];c[w+8>>2]=c[v+8>>2];c[w+12>>2]=c[v+12>>2];c[w+16>>2]=c[v+16>>2];c[w+20>>2]=c[v+20>>2];if(!s){t=q;u=0;break}}}while(0);k=t;c[k>>2]=j;c[k+4>>2]=r;c[a+22400+(u*24|0)+8>>2]=l;k=a+22400+(u*24|0)+12|0;c[k>>2]=c[d>>2];c[k+4>>2]=c[d+4>>2];c[k+8>>2]=c[d+8>>2];f=f+1|0;if((f|0)>=(c[e>>2]|0)){g=d;break}}i=b;return}function yc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0.0,x=0.0,y=0.0,z=0.0,A=0.0,B=0,C=0,D=0;b=i;i=i+16|0;d=b;e=a+27200|0;if((c[e>>2]|0)>0)f=0;else{g=d;i=b;return}do{j=c[a+27212+((c[a+22400+(f*24|0)>>2]|0)*1536|0)+((c[a+22400+(f*24|0)+4>>2]|0)*12|0)>>2]|0;k=a+22400+(f*24|0)+8|0;c[k>>2]=0;l=a+(j*112|0)+72|0;m=0;n=-1;o=j;p=0;while(1){q=c[a+(o*112|0)+76>>2]|0;if((q|0)==(n|0)){r=m;s=n;t=p}else{u=m+(c[672+(c[l>>2]<<5)+(p<<2)>>2]|0)|0;c[k>>2]=u;r=u;s=q;t=((p|0)<7&1)+p|0}o=c[a+(o*112|0)>>2]|0;if((o|0)==-1){v=r;break}else{m=r;n=s;p=t}}p=c[a+(j*112|0)+88>>2]|0;n=c[l>>2]|0;do if((p|0)>=2){if((p|0)<16){w=+h[168+(n<<5)+8>>3];break}if((p|0)>48){w=+h[168+(n<<5)+24>>3];break}else{w=+h[168+(n<<5)+8>>3]+ +(p+-15|0)*+h[168+(n<<5)+16>>3];break}}else w=+h[168+(n<<5)>>3];while(0);n=~~(w*+(v|0));c[k>>2]=n;p=c[l>>2]|0;x=+(c[a+(j*112|0)+96>>2]|0);y=+h[232+(p*40|0)>>3];do if(!(x<y)){z=+h[232+(p*40|0)+8>>3];if(x<z){A=(x-y)*+h[232+(p*40|0)+16>>3];break}else{A=+h[232+(p*40|0)+24>>3]*+Y(+((x-z)/+h[232+(p*40|0)+32>>3]));break}}else A=0.0;while(0);c[k>>2]=~~(A*+(n|0));f=f+1|0;p=c[e>>2]|0}while((f|0)<(p|0));B=p;if((B|0)<=0){g=d;i=b;return}B=a+22400|0;f=0;while(1){v=a+22400+(f*24|0)|0;t=c[v>>2]|0;s=c[v+4>>2]|0;v=c[a+22400+(f*24|0)+8>>2]|0;r=a+22400+(f*24|0)+12|0;c[d>>2]=c[r>>2];c[d+4>>2]=c[r+4>>2];c[d+8>>2]=c[r+8>>2];a:do if(!f){C=B;D=0}else{r=f;while(1){p=r;r=r+-1|0;j=a+22400+(p*24|0)|0;if((v|0)<=(c[a+22400+(r*24|0)+8>>2]|0)){C=j;D=p;break a}p=a+22400+(r*24|0)|0;c[j>>2]=c[p>>2];c[j+4>>2]=c[p+4>>2];c[j+8>>2]=c[p+8>>2];c[j+12>>2]=c[p+12>>2];c[j+16>>2]=c[p+16>>2];c[j+20>>2]=c[p+20>>2];if(!r){C=B;D=0;break}}}while(0);n=C;c[n>>2]=t;c[n+4>>2]=s;c[a+22400+(D*24|0)+8>>2]=v;n=a+22400+(D*24|0)+12|0;c[n>>2]=c[d>>2];c[n+4>>2]=c[d+4>>2];c[n+8>>2]=c[d+8>>2];f=f+1|0;if((f|0)>=(c[e>>2]|0)){g=d;break}}i=b;return}function zc(a,b,d){a=a|0;b=b|0;d=d|0;if((c[a+(b*112|0)+8>>2]|0)!=(c[a+(d*112|0)+8>>2]|0))return 0;if((c[a+(b*112|0)+12>>2]|0)!=(c[a+(d*112|0)+12>>2]|0))return 0;if((c[a+(b*112|0)+16>>2]|0)!=(c[a+(d*112|0)+16>>2]|0))return 0;if((c[a+(b*112|0)+20>>2]|0)!=(c[a+(d*112|0)+20>>2]|0))return 0;if((c[a+(b*112|0)+24>>2]|0)!=(c[a+(d*112|0)+24>>2]|0))return 0;if((c[a+(b*112|0)+28>>2]|0)!=(c[a+(d*112|0)+28>>2]|0))return 0;if((c[a+(b*112|0)+32>>2]|0)!=(c[a+(d*112|0)+32>>2]|0))return 0;if((c[a+(b*112|0)+36>>2]|0)!=(c[a+(d*112|0)+36>>2]|0))return 0;if((c[a+(b*112|0)+40>>2]|0)!=(c[a+(d*112|0)+40>>2]|0))return 0;if((c[a+(b*112|0)+44>>2]|0)!=(c[a+(d*112|0)+44>>2]|0))return 0;if((c[a+(b*112|0)+48>>2]|0)!=(c[a+(d*112|0)+48>>2]|0))return 0;if((c[a+(b*112|0)+52>>2]|0)!=(c[a+(d*112|0)+52>>2]|0))return 0;if((c[a+(b*112|0)+56>>2]|0)!=(c[a+(d*112|0)+56>>2]|0))return 0;if((c[a+(b*112|0)+60>>2]|0)!=(c[a+(d*112|0)+60>>2]|0))return 0;if((c[a+(b*112|0)+64>>2]|0)==(c[a+(d*112|0)+64>>2]|0))return (c[a+(b*112|0)+68>>2]|0)==(c[a+(d*112|0)+68>>2]|0)|0;else return 0;return 0}function Ac(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;e=b+38032+(d<<2)|0;f=c[e>>2]|0;do if((f|0)==-1){g=b+27208|0;h=b+27200|0;if((c[g>>2]|0)>=((c[h>>2]|0)+-1|0)){c[a>>2]=-1;return}i=(c[g>>2]|0)+1|0;c[g>>2]=i;if((i|0)<(c[h>>2]|0)){c[e>>2]=i;c[b+38036+(d<<2)>>2]=i;c[b+22400+(i*24|0)+20>>2]=0;c[b+22400+(i*24|0)+12>>2]=0;j=i;break}c[a>>2]=-1;return}else j=f;while(0);f=b+27212+((c[b+22400+(j*24|0)>>2]|0)*1536|0)+((c[b+22400+(j*24|0)+4>>2]|0)*12|0)|0;i=c[f>>2]|0;c[a>>2]=i;c[f>>2]=c[b+(i*112|0)>>2];h=b+22400+(j*24|0)+20|0;g=c[h>>2]|0;k=b+22400+(j*24|0)+16|0;do if(g){j=c[k>>2]|0;c[a+4>>2]=j;if((c[b+(i*112|0)+76>>2]|0)==(c[b+(j*112|0)+76>>2]|0)){c[b+(i*112|0)+104>>2]=0;break}j=b+(i*112|0)+104|0;if((c[b+(i*112|0)+80>>2]|0)==4){c[j>>2]=1;break}else{c[j>>2]=0;break}}else{c[k>>2]=i;c[a+4>>2]=-1;c[b+(i*112|0)+104>>2]=(c[b+(i*112|0)+80>>2]|0)==4&1}while(0);c[h>>2]=g+1;c[b+(i*112|0)+84>>2]=g;c[b+38040+(d<<2)>>2]=i;if((c[f>>2]|0)!=-1)return;c[e>>2]=-1;return}function Bc(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;f=i;i=i+43504|0;g=f+43408|0;h=f+104|0;j=f+43400|0;k=f+43304|0;l=f+96|0;m=f;if((c[a>>2]|0)>200){n=-101;i=f;return n|0}else o=0;do{c[b+4+(o*216|0)+4>>2]=0;o=o+1|0}while((o|0)!=200);if(!e)tc(1726592,a,1);else tc(1726592,a,2);a:do if((d|0)==1){Ac(j,1726592,0);e=c[j>>2]|0;o=j+4|0;if((e|0)!=-1){p=e;e=c[o>>2]|0;q=1;while(1){r=e;s=p;while(1){if((r|0)!=-1?(c[a+4+(s*96|0)+4>>2]|0)==(c[a+4+(r*96|0)+4>>2]|0):0)Ue(b+4+(s*216|0)|0,h+(r*216|0)|0,216)|0;else{t=k;u=a+4+(s*96|0)|0;v=t+96|0;do{c[t>>2]=c[u>>2];t=t+4|0;u=u+4|0}while((t|0)<(v|0));w=c[a+19204+(s<<2)>>2]|0;x=c[a+20004+(s<<2)>>2]|0;y=c[a+20804+(s<<2)>>2]|0;z=h+(s*216|0)|0;t=g;u=k;v=t+96|0;do{c[t>>2]=c[u>>2];t=t+4|0;u=u+4|0}while((t|0)<(v|0));A=Cc(g,w,x,y,z,0)|0;if((A|0)!=1){B=A;break}Ue(b+4+(s*216|0)|0,z|0,216)|0}Ac(j,1726592,0);s=c[j>>2]|0;if((s|0)==-1){C=q;D=34;break a}else r=c[o>>2]|0}Ac(j,1726592,0);p=c[j>>2]|0;if((p|0)==-1){n=B;break}else{e=c[o>>2]|0;q=B}}i=f;return n|0}}else{Ac(l,1726592,0);q=c[l>>2]|0;o=l+4|0;if((q|0)!=-1){e=(d|0)>0;p=(d|0)>1;r=c[o>>2]|0;s=q;q=1;while(1){b:do if(e){A=r;E=s;while(1){if((A|0)==-1){F=E;break b}G=a+4+(E*96|0)+4|0;H=0;do{c[G>>2]=H;c[b+4+(E*216|0)+164+(H<<2)>>2]=c[b+4+(A*216|0)+164+(H<<2)>>2];H=H+1|0}while((H|0)!=(d|0));Ac(l,1726592,0);E=c[l>>2]|0;if((E|0)==-1){C=q;D=34;break a}else A=c[o>>2]|0}}else{A=s;E=r;while(1){if((E|0)==-1){F=A;break b}Ac(l,1726592,0);A=c[l>>2]|0;if((A|0)==-1){C=q;D=34;break a}else E=c[o>>2]|0}}while(0);E=a+4+(F*96|0)|0;A=a+4+(F*96|0)+4|0;c[A>>2]=0;t=m;u=E;v=t+96|0;do{c[t>>2]=c[u>>2];t=t+4|0;u=u+4|0}while((t|0)<(v|0));z=c[a+19204+(F<<2)>>2]|0;y=c[a+20004+(F<<2)>>2]|0;x=c[a+20804+(F<<2)>>2]|0;w=h+(F*216|0)|0;t=g;u=m;v=t+96|0;do{c[t>>2]=c[u>>2];t=t+4|0;u=u+4|0}while((t|0)<(v|0));H=Cc(g,z,y,x,w,0)|0;if((H|0)==1){c[b+4+(F*216|0)+164>>2]=c[h+(F*216|0)+164>>2];I=q}else I=H;if(p){H=h+(F*216|0)+164|0;G=I;J=1;while(1){K=c[H>>2]|0;L=(J|0)==2?K:13-K|0;c[A>>2]=J;t=g;u=E;v=t+96|0;do{c[t>>2]=c[u>>2];t=t+4|0;u=u+4|0}while((t|0)<(v|0));K=Gc(g,w,L,0)|0;if((K|0)==1){c[b+4+(F*216|0)+164+(J<<2)>>2]=c[H>>2];M=G}else M=K;J=J+1|0;if((J|0)==(d|0)){N=M;break}else G=M}}else N=I;Ac(l,1726592,0);s=c[l>>2]|0;if((s|0)==-1){C=N;D=34;break}else{r=c[o>>2]|0;q=N}}}}while(0);if((D|0)==34?(C|0)!=1:0){n=C;i=f;return n|0}c[b>>2]=0;C=0;D=0;while(1){if(!(c[b+4+(D*216|0)+4>>2]|0))O=C;else{N=C+1|0;c[b>>2]=N;O=N}D=D+1|0;if((D|0)==200){n=1;break}else C=O}i=f;return n|0}function Cc(d,f,g,j,k,l){d=d|0;f=f|0;g=g|0;j=j|0;k=k|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0,Qa=0,Ra=0,Sa=0,Ta=0,Ua=0,Va=0,Wa=0.0,Xa=0;m=i;i=i+32|0;n=m+16|0;o=m+8|0;p=m+4|0;q=m;r=6192+($(l,1688368)|0)|0;s=Dc(d,f,g,j,l)|0;if((s|0)==1){t=0;u=0;v=0;w=0;x=0}else{y=s;i=m;return y|0}while(1){s=(c[d+32+(w<<4)>>2]|0)>>>2;z=(c[1862980+(s<<2)>>2]|0)+u|0;A=6192+($(l,1688368)|0)+22+(w<<3)|0;B=e[A>>1]|0;if((B|0)==(s|0))C=x;else{b[A>>1]=s;C=1}A=(c[d+32+(w<<4)+4>>2]|0)>>>2;D=(c[1862980+(A<<2)>>2]|0)+z|0;z=6192+($(l,1688368)|0)+22+(w<<3)+2|0;E=e[z>>1]|0;if((E|0)==(A|0))F=C;else{b[z>>1]=A;F=1}z=(c[d+32+(w<<4)+8>>2]|0)>>>2;G=(c[1862980+(z<<2)>>2]|0)+D|0;D=6192+($(l,1688368)|0)+22+(w<<3)+4|0;H=e[D>>1]|0;if((H|0)==(z|0))I=F;else{b[D>>1]=z;I=1}D=(c[d+32+(w<<4)+12>>2]|0)>>>2;J=(c[1862980+(D<<2)>>2]|0)+G|0;G=6192+($(l,1688368)|0)+22+(w<<3)+6|0;K=e[G>>1]|0;L=(K^D)+((H^z)+((E^A)+((B^s)+v)))|0;B=D+(z+(A+(s+t)))|0;if((K|0)==(D|0))M=I;else{b[G>>1]=D;M=1}w=w+1|0;if((w|0)==4){N=J;O=L;P=B;Q=M;break}else{t=B;u=J;v=L;x=M}}if(Q)if(!O)R=1;else R=((P>>>0)/(O>>>0)|0)>>>0>5;else R=0;O=c[d>>2]|0;P=6192+($(l,1688368)|0)+56|0;M=c[P>>2]|0;c[P>>2]=O;x=N+-4|0;c[6192+($(l,1688368)|0)+16>>2]=x;v=(52-N|0)%4|0;u=c[d+4>>2]|0;t=u+v|0;w=6192+($(l,1688368)|0)+4940|0;c[w>>2]=0;c[6192+($(l,1688368)|0)+1532>>2]=v;I=6192+($(l,1688368)|0)+532+(x<<2)|0;c[I>>2]=u;c[6192+($(l,1688368)|0)+1536>>2]=0;c[n>>2]=0;c[n+4>>2]=0;c[n+8>>2]=0;c[n+12>>2]=0;u=6192+($(l,1688368)|0)+3104|0;c[u>>2]=0;F=6192+($(l,1688368)|0)+3100|0;c[F>>2]=0;C=6192+($(l,1688368)|0)+3120|0;c[C>>2]=0;L=6192+($(l,1688368)|0)+3116|0;c[L>>2]=0;J=6192+($(l,1688368)|0)+3136|0;c[J>>2]=0;B=6192+($(l,1688368)|0)+3132|0;c[B>>2]=0;D=6192+($(l,1688368)|0)+3152|0;c[D>>2]=0;G=6192+($(l,1688368)|0)+3148|0;c[G>>2]=0;K=6192+($(l,1688368)|0)+3168|0;c[K>>2]=0;s=6192+($(l,1688368)|0)+3164|0;c[s>>2]=0;A=6192+($(l,1688368)|0)+3184|0;c[A>>2]=0;z=6192+($(l,1688368)|0)+3180|0;c[z>>2]=0;E=6192+($(l,1688368)|0)+3200|0;c[E>>2]=0;H=6192+($(l,1688368)|0)+3196|0;c[H>>2]=0;S=6192+($(l,1688368)|0)+3216|0;c[S>>2]=0;T=6192+($(l,1688368)|0)+3212|0;c[T>>2]=0;U=6192+($(l,1688368)|0)+3232|0;c[U>>2]=0;V=6192+($(l,1688368)|0)+3228|0;c[V>>2]=0;W=6192+($(l,1688368)|0)+3248|0;c[W>>2]=0;X=6192+($(l,1688368)|0)+3244|0;c[X>>2]=0;Y=6192+($(l,1688368)|0)+3264|0;c[Y>>2]=0;Z=6192+($(l,1688368)|0)+3260|0;c[Z>>2]=0;_=6192+($(l,1688368)|0)+3280|0;c[_>>2]=0;aa=6192+($(l,1688368)|0)+3276|0;c[aa>>2]=0;ba=6192+($(l,1688368)|0)+3296|0;c[ba>>2]=0;ca=6192+($(l,1688368)|0)+3292|0;c[ca>>2]=0;da=6192+($(l,1688368)|0)+3312|0;c[da>>2]=0;ea=6192+($(l,1688368)|0)+3308|0;c[ea>>2]=0;fa=(O|0)!=(M|0);M=N+-1>>2;O=t&3;ga=6192+($(l,1688368)|0)+60|0;ha=Ec(d,f,g,j,r)|0;if((ha|0)==1){a:do if((N|0)<5){Fc(d,r,O,v,o,p,q);c[k>>2]=0;c[k+4>>2]=1;c[k+8>>2]=c[p>>2];c[k+60>>2]=c[o>>2];c[k+112>>2]=0;c[k+164>>2]=(f|0)==0&(g|0)<3?0:c[q>>2]|0;ia=k}else{do if((j|0)!=2){if(!(Q&(R^1)|fa)?(c[6192+($(l,1688368)|0)+4936>>2]|0)<=7e5:0)break;Tc(6192+($(l,1688368)|0)+987984|0)}while(0);if(!Q){ja=6192+($(l,1688368)|0)+1604|0;if(!(a[ja>>0]|0))ka=ja;else{Ab(r);ka=ja}}else{Ab(r);Bb(r);ka=6192+($(l,1688368)|0)+1604|0}a[ka>>0]=0;ja=t&1;c[r>>2]=ja^1;c[6192+($(l,1688368)|0)+4>>2]=ja;c[6192+($(l,1688368)|0)+8>>2]=ja^1;c[6192+($(l,1688368)|0)+12>>2]=ja;if((v|0)>0){la=n+4|0;ma=n+8|0;na=6192+($(l,1688368)|0)+1648324|0;oa=d+20|0;pa=d+8|0;qa=6192+($(l,1688368)|0)+3324+(x<<4)|0;ra=6192+($(l,1688368)|0)+4124+(x<<4)|0;sa=6192+($(l,1688368)|0)+4944|0;ta=v+x|0;ua=0;do{c[la>>2]=c[d+20+(ua<<2)>>2];c[n>>2]=c[d+8+(ua<<2)>>2];c[ma>>2]=0;Yb(na,M,ua,oa,pa,ga,c[P>>2]|0,c[I>>2]|0);if(!ua)_b(na,M,ga,qa,ra,sa)|0;else cc(na,M,ua,ga)|0;va=6192+($(l,1688368)|0)+732+(ta-ua<<4)|0;c[va>>2]=c[n>>2];c[va+4>>2]=c[n+4>>2];c[va+8>>2]=c[n+8>>2];c[va+12>>2]=c[n+12>>2];ec(na,n,M,ua);ua=ua+1|0}while((ua|0)!=(v|0));wa=na;xa=oa;ya=pa;za=ga}else{wa=6192+($(l,1688368)|0)+1648324|0;xa=d+20|0;ya=d+8|0;za=ga}Cb(d,ga,r);Yb(wa,M,v,xa,ya,za,c[P>>2]|0,c[I>>2]|0);if(!v)_b(wa,M,ga,6192+($(l,1688368)|0)+3324+(x<<4)|0,6192+($(l,1688368)|0)+4124+(x<<4)|0,6192+($(l,1688368)|0)+4944|0)|0;else cc(wa,M,v,ga)|0;pa=dc(wa,M,v)|0;if((j|0)==0&(pa|0)==1){oa=gc(wa,M,v)|0;c[k>>2]=0;c[k+4>>2]=1;c[k+8>>2]=c[oa>>2];c[k+60>>2]=c[oa+4>>2];c[k+112>>2]=c[oa+8>>2]<<2;c[k+164>>2]=-2;ia=k;break}b:do if((g|0)==3){c[k+4>>2]=pa;if((pa|0)>0){oa=736+(v<<2)|0;na=6192+($(l,1688368)|0)+20|0;ua=6192+($(l,1688368)|0)+3324+(x<<4)|0;ta=n+4|0;sa=n+8|0;ra=ja^7;qa=0;ma=13;while(1){la=ra;va=0;Aa=ma;while(1){Db(r);Ba=db[c[oa>>2]&7](ga,la,x,r)|0;a[na>>0]=Ba&1;if(Ba){c[n>>2]=c[ua>>2];c[n+4>>2]=c[ua+4>>2];c[n+8>>2]=c[ua+8>>2];c[n+12>>2]=c[ua+12>>2];Ca=la+1|0;Da=la;Ea=Aa}else{Ba=la+-1|0;Ca=Ba;Da=va;Ea=Ba}if((Da|0)<(Ea|0)){la=Ca;va=Da;Aa=Ea}else{Fa=Da;Ga=Ea;break}}if(!Fa){Ha=qa;break};c[ua>>2]=c[n>>2];c[ua+4>>2]=c[n+4>>2];c[ua+8>>2]=c[n+8>>2];c[ua+12>>2]=c[n+12>>2];Aa=c[n>>2]|0;c[k+8+(qa<<2)>>2]=Aa;va=c[ta>>2]|0;c[k+60+(qa<<2)>>2]=va;c[k+112+(qa<<2)>>2]=c[sa>>2]<<2;c[k+164+(qa<<2)>>2]=Fa;la=qa+1|0;c[6192+($(l,1688368)|0)+3100+(la<<4)>>2]=Aa;c[6192+($(l,1688368)|0)+3100+(la<<4)+4>>2]=va;if((la|0)<(pa|0)){ra=Fa;qa=la;ma=Ga}else break b}ma=dc(wa,M,v)|0;hc(wa,M,v);if((ma|0)>0){qa=0;do{ra=gc(wa,M,v)|0;sa=qa+Ha|0;c[k+8+(sa<<2)>>2]=c[ra>>2];c[k+60+(sa<<2)>>2]=c[ra+4>>2];c[k+112+(sa<<2)>>2]=c[ra+8>>2]<<2;c[k+164+(sa<<2)>>2]=0;qa=qa+1|0}while((qa|0)!=(ma|0))}}}else{c:do switch(f|0){case 0:{c[k>>2]=0;c[k+4>>2]=(g|0)==1?1:pa;if((pa|0)>0)Ia=0;else{ia=k;break a}while(1){ma=gc(wa,M,v)|0;c[k+8+(Ia<<2)>>2]=c[ma>>2];c[k+60+(Ia<<2)>>2]=c[ma+4>>2];c[k+112+(Ia<<2)>>2]=c[ma+8>>2]<<2;c[k+164+(Ia<<2)>>2]=0;Ia=Ia+1|0;if((Ia|0)==(pa|0)){ia=k;break a}}break}case -1:{ma=736+(v<<2)|0;qa=6192+($(l,1688368)|0)+20|0;sa=6192+($(l,1688368)|0)+3324+(x<<4)|0;ra=ja^7;ta=0;ua=13;while(1){Db(r);na=db[c[ma>>2]&7](ga,ra,x,r)|0;a[qa>>0]=na&1;if(na){c[n>>2]=c[sa>>2];c[n+4>>2]=c[sa+4>>2];c[n+8>>2]=c[sa+8>>2];c[n+12>>2]=c[sa+12>>2];Ja=ra+1|0;Ka=ra;La=ua}else{na=ra+-1|0;Ja=na;Ka=ta;La=na}if((Ka|0)<(La|0)){ra=Ja;ta=Ka;ua=La}else{Ma=Ka;break}}c[sa>>2]=c[n>>2];c[sa+4>>2]=c[n+4>>2];c[sa+8>>2]=c[n+8>>2];c[sa+12>>2]=c[n+12>>2];if(!Ma){c[k+4>>2]=(g|0)==1?1:pa;hc(wa,M,v);if((pa|0)>0)Na=0;else break b;while(1){ua=gc(wa,M,v)|0;c[k+164+(Na<<2)>>2]=0;c[k+8+(Na<<2)>>2]=c[ua>>2];c[k+60+(Na<<2)>>2]=c[ua+4>>2];c[k+112+(Na<<2)>>2]=c[ua+8>>2]<<2;Na=Na+1|0;if((Na|0)==(pa|0))break b}}else{sa=k+4|0;c[sa>>2]=1;ua=k+164|0;c[ua>>2]=Ma;c[k+8>>2]=c[n>>2];c[k+60>>2]=c[n+4>>2];c[k+112>>2]=c[n+8>>2]<<2;if(!((g|0)==2&(pa|0)>1))break b;Oa=6192+($(l,1688368)|0)+3324+(x<<4)+8|0;Pa=ma;Qa=ua;Ra=qa;Sa=sa;Ta=6192+($(l,1688368)|0)+3324+(x<<4)+4|0;Ua=6192+($(l,1688368)|0)+3324+(x<<4)|0;break c}break}default:{sa=736+(v<<2)|0;ua=db[c[sa>>2]&7](ga,f,x,r)|0;ta=6192+($(l,1688368)|0)+20|0;a[ta>>0]=ua&1;ra=k+4|0;if(ua){c[ra>>2]=1;ua=6192+($(l,1688368)|0)+3324+(x<<4)|0;c[k+8>>2]=c[ua>>2];na=6192+($(l,1688368)|0)+3324+(x<<4)+4|0;c[k+60>>2]=c[na>>2];oa=6192+($(l,1688368)|0)+3324+(x<<4)+8|0;c[k+112>>2]=c[oa>>2]<<2;la=k+164|0;c[la>>2]=f;if((g|0)==2&(pa|0)>1){Oa=oa;Pa=sa;Qa=la;Ra=ta;Sa=ra;Ta=na;Ua=ua;break c}else break b}else{c[ra>>2]=0;c[k+164>>2]=((f|0)>1)<<31>>31;break b}}}while(0);ra=1;ua=1;while(1){hc(wa,M,v);na=dc(wa,M,v)|0;d:do if((na|0)>0){ta=ra;la=0;while(1){sa=gc(wa,M,v)|0;oa=6192+($(l,1688368)|0)+3100+(ta<<4)|0;c[oa>>2]=c[sa>>2];c[oa+4>>2]=c[sa+4>>2];c[oa+8>>2]=c[sa+8>>2];c[oa+12>>2]=c[sa+12>>2];oa=ta+1|0;if((c[Ua>>2]|0)==(c[sa>>2]|0)?(c[Ta>>2]|0)==(c[sa+4>>2]|0):0){Va=oa;break d}la=la+1|0;if((la|0)>=(na|0)){Va=oa;break}else ta=oa}}else Va=ra;while(0);Db(r);na=db[c[Pa>>2]&7](ga,c[Qa>>2]|0,x,r)|0;a[Ra>>0]=na&1;if(!na)break b;na=ua;ua=ua+1|0;c[Sa>>2]=ua;c[k+8+(na<<2)>>2]=c[Ua>>2];c[k+60+(na<<2)>>2]=c[Ta>>2];c[k+112+(na<<2)>>2]=c[Oa>>2]<<2;c[k+164+(na<<2)>>2]=c[Qa>>2];if((ua|0)>=(pa|0))break;else ra=Va}}while(0);c[u>>2]=0;c[F>>2]=0;c[C>>2]=0;c[L>>2]=0;c[J>>2]=0;c[B>>2]=0;c[D>>2]=0;c[G>>2]=0;c[K>>2]=0;c[s>>2]=0;c[A>>2]=0;c[z>>2]=0;c[E>>2]=0;c[H>>2]=0;c[S>>2]=0;c[T>>2]=0;c[U>>2]=0;c[V>>2]=0;c[W>>2]=0;c[X>>2]=0;c[Y>>2]=0;c[Z>>2]=0;c[_>>2]=0;c[aa>>2]=0;c[ba>>2]=0;c[ca>>2]=0;c[da>>2]=0;c[ea>>2]=0;ia=k}while(0);Wa=+Uc(6192+($(l,1688368)|0)+987984|0);h[6192+($(l,1688368)|0)+4928>>3]=Wa+ +Eb();c[ia>>2]=c[w>>2];Xa=1}else Xa=ha;y=Xa;i=m;return y|0}function Dc(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0;if((b|0)<-1){Hc(-5,a,b,d,e)|0;g=-5;return g|0}if((b|0)>13){Hc(-7,a,b,d,e)|0;g=-7;return g|0}if((d|0)<1){Hc(-8,a,b,d,e)|0;g=-8;return g|0}if((d|0)>3){Hc(-9,a,b,d,e)|0;g=-9;return g|0}if((e|0)<0){Hc(-16,a,b,d,e)|0;g=-16;return g|0}if((e|0)>2){Hc(-17,a,b,d,e)|0;g=-17;return g|0}if(!((f|0)>-1&(c[449360]|0)>(f|0))){Hc(-15,a,b,d,e)|0;g=-15;return g|0}if((c[a>>2]|0)>>>0>4){Hc(-18,a,b,d,e)|0;g=-18;return g|0}if((c[a+4>>2]|0)>>>0>3){Hc(-19,a,b,d,e)|0;g=-19;return g|0}f=c[a+20>>2]|0;if(f)if((f+-2|0)>>>0<=12)if((c[a+8>>2]|0)>>>0>3)h=23;else{i=1;h=24}else h=21;else{i=0;h=24}do if((h|0)==24){f=c[a+24>>2]|0;if(f){if((f+-2|0)>>>0>12){h=21;break}if((c[a+12>>2]|0)>>>0>3){h=23;break}else j=1}else j=0;f=c[a+28>>2]|0;if(!f){if((j|0)!=0&(i|0)==0)h=28}else{if((f+-2|0)>>>0>12){h=21;break}if((c[a+16>>2]|0)>>>0>3){h=23;break}if((j|0)==0|(i|0)==0)h=28}if((h|0)==28){Hc(-12,a,b,d,e)|0;g=-12;return g|0}f=c[a+32>>2]|0;do if(((((!((f|0)!=0&(f+-4|0)>>>0>32763)?(k=c[a+36>>2]|0,!((k|0)!=0&(k+-4|0)>>>0>32763)):0)?(k=c[a+40>>2]|0,!((k|0)!=0&(k+-4|0)>>>0>32763)):0)?(k=c[a+44>>2]|0,!((k|0)!=0&(k+-4|0)>>>0>32763)):0)?(k=c[a+48>>2]|0,!((k|0)!=0&(k+-4|0)>>>0>32763)):0)?(k=c[a+52>>2]|0,!((k|0)!=0&(k+-4|0)>>>0>32763)):0){k=c[a+56>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+60>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+64>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+68>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+72>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+76>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+80>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+84>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+88>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;k=c[a+92>>2]|0;if((k|0)!=0&(k+-4|0)>>>0>32763)break;else g=1;return g|0}while(0);Hc(-12,a,b,d,e)|0;g=-12;return g|0}while(0);if((h|0)==21){Hc(-12,a,b,d,e)|0;g=-12;return g|0}else if((h|0)==23){Hc(-12,a,b,d,e)|0;g=-12;return g|0}return 0}function Ec(a,d,f,g,h){a=a|0;d=d|0;f=f|0;g=g|0;h=h|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0;j=i;i=i+16|0;k=j;l=c[h+16>>2]|0;m=l+4|0;if((m|0)<1){Hc(-2,a,d,f,g)|0;n=-2;i=j;return n|0}if((m|0)>52){Hc(-10,a,d,f,g)|0;n=-10;i=j;return n|0}if((((m&3|0)==0?1:2)+(l>>2)|0)<(d|0)){Hc(-3,a,d,f,g)|0;n=-3;i=j;return n|0}l=c[h+1532>>2]|0;c[k>>2]=0;c[k+4>>2]=0;c[k+8>>2]=0;c[k+12>>2]=0;m=(l|0)>0;if(m){o=c[a+4>>2]|0;p=0;do{c[k+((o+p&3)<<2)>>2]=1;p=p+1|0}while((p|0)!=(l|0));p=k+4|0;o=k+8|0;q=k+12|0;r=p;s=o;t=q;u=k;v=c[k>>2]|0;w=c[p>>2]|0;x=c[o>>2]|0;y=c[q>>2]|0}else{r=k+4|0;s=k+8|0;t=k+12|0;u=k;v=0;w=0;x=0;y=0}k=v+(c[1862980+((e[h+22>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+24>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+26>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+28>>1]|0)<<2)>>2]|0)|0;c[u>>2]=k;u=w+(c[1862980+((e[h+30>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+32>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+34>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+36>>1]|0)<<2)>>2]|0)|0;c[r>>2]=u;r=x+(c[1862980+((e[h+38>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+40>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+42>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+44>>1]|0)<<2)>>2]|0)|0;c[s>>2]=r;s=y+(c[1862980+((e[h+46>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+48>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+50>>1]|0)<<2)>>2]|0)+(c[1862980+((e[h+52>>1]|0)<<2)>>2]|0)|0;c[t>>2]=s;a:do if((u|0)==(k|0)&(r|0)==(k|0)&(s|0)==(k|0)){b:do if(m){t=0;while(1){y=c[a+8+(t<<2)>>2]|0;if((b[1224+(c[a+20+(t<<2)>>2]<<1)>>1]&((c[a+80+(y<<2)>>2]|(c[a+64+(y<<2)>>2]|(c[a+48+(y<<2)>>2]|c[a+32+(y<<2)>>2])))>>>2&65535))<<16>>16)break;t=t+1|0;if((t|0)>=(l|0))break b}Hc(-13,a,d,f,g)|0;z=-13;break a}while(0);t=b[h+22>>1]|0;y=b[h+30>>1]|0;x=t|y;w=h+38|0;v=h+46|0;q=2;while(1){o=b[1224+(q<<1)>>1]|0;if(!((o&y)<<16>>16==0|(o&t)<<16>>16==0))break;p=(o&x)<<16>>16!=0;A=(o&b[w>>1])<<16>>16==0;if(!(A|p^1))break;if(!((o&b[v>>1])<<16>>16==0|A&(p^1)))break;q=q+1|0;if((q|0)>=15){B=19;break}}c:do if((B|0)==19){q=b[h+24>>1]|0;v=b[h+32>>1]|0;w=q|v;x=h+40|0;t=h+48|0;y=2;do{p=b[1224+(y<<1)>>1]|0;if(!((p&v)<<16>>16==0|(p&q)<<16>>16==0))break c;A=(p&w)<<16>>16!=0;o=(p&b[x>>1])<<16>>16==0;if(!(o|A^1))break c;if(!((p&b[t>>1])<<16>>16==0|o&(A^1)))break c;y=y+1|0}while((y|0)<15);y=b[h+26>>1]|0;t=b[h+34>>1]|0;x=y|t;w=h+42|0;q=h+50|0;v=2;do{A=b[1224+(v<<1)>>1]|0;if(!((A&t)<<16>>16==0|(A&y)<<16>>16==0))break c;o=(A&x)<<16>>16!=0;p=(A&b[w>>1])<<16>>16==0;if(!(p|o^1))break c;if(!((A&b[q>>1])<<16>>16==0|p&(o^1)))break c;v=v+1|0}while((v|0)<15);v=b[h+28>>1]|0;q=b[h+36>>1]|0;w=v|q;x=h+44|0;y=h+52|0;t=2;while(1){o=b[1224+(t<<1)>>1]|0;if(!((o&q)<<16>>16==0|(o&v)<<16>>16==0))break c;p=(o&w)<<16>>16!=0;A=(o&b[x>>1])<<16>>16==0;if(!(A|p^1))break c;if(!((o&b[y>>1])<<16>>16==0|A&(p^1)))break c;t=t+1|0;if((t|0)>=15){z=1;break a}}}while(0);Hc(-4,a,d,f,g)|0;z=-4}else{Hc(-14,a,d,f,g)|0;z=-14}while(0);n=z;i=j;return n|0}function Fc(a,d,e,f,g,h,j){a=a|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;k=i;i=i+32|0;l=k+16|0;m=k;if((f|0)>0){n=c[a+4>>2]|0;o=0;do{p=n+o&3;c[l+(p<<2)>>2]=c[a+8+(o<<2)>>2];c[m+(p<<2)>>2]=c[a+20+(o<<2)>>2];o=o+1|0}while((o|0)!=(f|0));if((f|0)<4)q=4}else q=4;if((q|0)==4){o=c[a+4>>2]|0;n=f;do{p=o+n&3;r=b[d+22+(p<<3)>>1]|0;if(!(r<<16>>16)){s=b[d+22+(p<<3)+2>>1]|0;if(!(s<<16>>16)){t=b[d+22+(p<<3)+4>>1]|0;if(!(t<<16>>16)){u=b[d+22+(p<<3)+6>>1]|0;if(u<<16>>16){v=u;w=3;q=7}}else{v=t;w=2;q=7}}else{v=s;w=1;q=7}}else{v=r;w=0;q=7}if((q|0)==7){q=0;c[l+(p<<2)>>2]=w;c[m+(p<<2)>>2]=c[1797444+((v&65535)<<2)>>2]}n=n+1|0}while((n|0)!=4)}n=c[a>>2]|0;v=c[l>>2]|0;if((n|0)!=4){if((v|0)==(n|0)){w=c[m>>2]|0;d=(w|0)>0;x=(d^1)<<31>>31;y=d?w:0}else{x=-1;y=0}if((c[l+4>>2]|0)==(n|0)){w=c[m+4>>2]|0;d=(w|0)>(y|0);z=d?1:x;A=d?w:y}else{z=x;A=y}if((c[l+8>>2]|0)==(n|0)){y=c[m+8>>2]|0;x=(y|0)>(A|0);B=x?2:z;C=x?y:A}else{B=z;C=A}if((c[l+12>>2]|0)==(n|0)){n=c[m+12>>2]|0;A=(n|0)>(C|0);D=A?3:B;E=A?n:C}else{D=B;E=C}if(E){F=c[a+4>>2]|0;G=D}else q=14}else q=14;if((q|0)==14){q=c[a+4>>2]|0;a=c[m+(q<<2)>>2]|0;D=c[l+(q<<2)>>2]|0;if((v|0)==(D|0)){v=c[m>>2]|0;E=(v|0)>(a|0);H=E?0:q;I=E?v:a}else{H=q;I=a}if((c[l+4>>2]|0)==(D|0)){a=c[m+4>>2]|0;v=(a|0)>(I|0);J=v?1:H;K=v?a:I}else{J=H;K=I}if((c[l+8>>2]|0)==(D|0)){I=c[m+8>>2]|0;H=(I|0)>(K|0);L=H?2:J;M=H?I:K}else{L=J;M=K}if((c[l+12>>2]|0)==(D|0)){F=q;G=(c[m+12>>2]|0)>(M|0)?3:L}else{F=q;G=L}}L=F+f&3;c[g>>2]=c[m+(L<<2)>>2];c[h>>2]=c[l+(L<<2)>>2];if((G|0)==(e|0)){N=1;O=N&1;c[j>>2]=O;i=k;return}N=(c[472+(e<<2)>>2]|0)==(G|0);O=N&1;c[j>>2]=O;i=k;return}function Gc(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0.0;g=6192+($(f,1688368)|0)|0;i=c[6192+($(f,1688368)|0)+16>>2]|0;j=i+3>>2;k=6192+($(f,1688368)|0)+4940|0;c[k>>2]=0;l=c[b+4>>2]|0;b=6192+($(f,1688368)|0)+60|0;c[6192+($(f,1688368)|0)+532+(i<<2)>>2]=l;switch(l|0){case 2:case 0:{c[g>>2]=1;c[6192+($(f,1688368)|0)+4>>2]=0;c[6192+($(f,1688368)|0)+8>>2]=1;c[6192+($(f,1688368)|0)+12>>2]=0;break}default:{c[g>>2]=0;c[6192+($(f,1688368)|0)+4>>2]=1;c[6192+($(f,1688368)|0)+8>>2]=0;c[6192+($(f,1688368)|0)+12>>2]=1}}Zb(6192+($(f,1688368)|0)+1648324|0,j,l);l=6192+($(f,1688368)|0)+20|0;j=e;e=0;m=13;while(1){Db(g);n=pb(b,j,i,g)|0;a[l>>0]=n&1;o=j+-1|0;p=n?j:e;m=n?m:o;if((p|0)>=(m|0)){q=p;break}else{j=n?j+1|0:o;e=p}}c[d+4>>2]=1;c[d+164>>2]=q;r=+Uc(6192+($(f,1688368)|0)+987984|0);h[6192+($(f,1688368)|0)+4928>>3]=r+ +Eb();c[d>>2]=c[k>>2];return 1}function Hc(a,e,f,g,h){a=a|0;e=e|0;f=f|0;g=g|0;h=h|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0;j=i;i=i+192|0;k=j+144|0;l=j+128|0;m=j+120|0;n=j+112|0;o=j+104|0;p=j+88|0;q=j+72|0;r=j+56|0;s=j+40|0;t=j+24|0;u=j+16|0;v=j+8|0;w=j;x=j+160|0;y=ie(2268,2277)|0;if(!y){z=-1;i=j;return z|0}c[w>>2]=a;je(y,2279,w)|0;ke(10,y)|0;me(2294,11,1,y)|0;w=c[e>>2]|0;if((w|0)==4)me(2316,8,1,y)|0;else{c[v>>2]=d[1369+w>>0];je(y,2306,v)|0}c[u>>2]=d[1374+(c[e+4>>2]|0)>>0];je(y,2325,u)|0;u=c[e+20>>2]|0;if(u){v=d[1369+(c[e+8>>2]|0)>>0]|0;w=d[1353+u>>0]|0;c[t>>2]=0;c[t+4>>2]=v;c[t+8>>2]=w;je(y,2335,t)|0}t=c[e+24>>2]|0;if(t){w=d[1369+(c[e+12>>2]|0)>>0]|0;v=d[1353+t>>0]|0;c[l>>2]=1;c[l+4>>2]=w;c[l+8>>2]=v;je(y,2335,l)|0}l=c[e+28>>2]|0;if(!l)A=0;else{v=d[1369+(c[e+16>>2]|0)>>0]|0;w=d[1353+l>>0]|0;c[k>>2]=2;c[k+4>>2]=v;c[k+8>>2]=w;je(y,2335,k)|0;A=0}do{k=e+32+(A<<4)|0;w=c[k>>2]|0;c[s>>2]=A;c[s+4>>2]=0;c[s+8>>2]=w;je(y,2385,s)|0;b[x+(A<<3)>>1]=(c[k>>2]|0)>>>2;k=e+32+(A<<4)+4|0;w=c[k>>2]|0;c[r>>2]=A;c[r+4>>2]=1;c[r+8>>2]=w;je(y,2385,r)|0;b[x+(A<<3)+2>>1]=(c[k>>2]|0)>>>2;k=e+32+(A<<4)+8|0;w=c[k>>2]|0;c[q>>2]=A;c[q+4>>2]=2;c[q+8>>2]=w;je(y,2385,q)|0;b[x+(A<<3)+4>>1]=(c[k>>2]|0)>>>2;k=e+32+(A<<4)+12|0;w=c[k>>2]|0;c[p>>2]=A;c[p+4>>2]=3;c[p+8>>2]=w;je(y,2385,p)|0;b[x+(A<<3)+6>>1]=(c[k>>2]|0)>>>2;A=A+1|0}while((A|0)!=4);ke(10,y)|0;c[o>>2]=f;je(y,2421,o)|0;c[n>>2]=g;je(y,2432,n)|0;c[m>>2]=h;je(y,2446,m)|0;ke(10,y)|0;Ic(y,x);ge(y)|0;z=0;i=j;return z|0}function Ic(f,g){f=f|0;g=g|0;var h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;h=i;i=i+48|0;j=h+32|0;k=h+24|0;l=h+16|0;m=h+8|0;n=h;o=h+36|0;a[o>>0]=(c[1862980+(e[g+24>>1]<<2)>>2]|0)>5&1;a[o+1>>0]=(c[1862980+(e[g+26>>1]<<2)>>2]|0)>5&1;a[o+2>>0]=(c[1862980+(e[g+28>>1]<<2)>>2]|0)>5&1;a[o+3>>0]=(c[1862980+(e[g+30>>1]<<2)>>2]|0)>5&1;ke(10,f)|0;p=0;while(1){c[n>>2]=d[1369+p>>0];je(f,2455,n)|0;q=g+(p<<1)|0;r=b[q>>1]|0;a:do if(!(r<<16>>16))me(2460,2,1,f)|0;else{s=r;t=14;while(1){if((b[1224+(t<<1)>>1]&s)<<16>>16)ke(d[1353+t>>0]|0,f)|0;u=t+-1|0;if((u|0)<=1)break a;s=b[q>>1]|0;t=u}}while(0);ke(10,f)|0;p=p+1|0;if((p|0)==4){v=0;break}}while(1){p=1369+v|0;c[m>>2]=d[p>>0];je(f,2463,m)|0;n=g+24+(v<<1)|0;q=b[n>>1]|0;b:do if(!(q<<16>>16))me(2460,2,1,f)|0;else{r=q;t=14;while(1){if((b[1224+(t<<1)>>1]&r)<<16>>16)ke(d[1353+t>>0]|0,f)|0;s=t+-1|0;if((s|0)<=1)break b;r=b[n>>1]|0;t=s}}while(0);n=d[p>>0]|0;if(!(a[o+v>>0]|0)){c[k>>2]=n;je(f,2467,k)|0}else{c[l>>2]=n;je(f,2455,l)|0}n=g+8+(v<<1)|0;q=b[n>>1]|0;c:do if(!(q<<16>>16))me(2460,2,1,f)|0;else{t=q;r=14;while(1){if((b[1224+(r<<1)>>1]&t)<<16>>16)ke(d[1353+r>>0]|0,f)|0;s=r+-1|0;if((s|0)<=1)break c;t=b[n>>1]|0;r=s}}while(0);ke(10,f)|0;v=v+1|0;if((v|0)==4){w=0;break}}do{c[j>>2]=d[1369+w>>0];je(f,2455,j)|0;v=g+16+(w<<1)|0;l=b[v>>1]|0;d:do if(!(l<<16>>16))me(2460,2,1,f)|0;else{k=l;o=14;while(1){if((b[1224+(o<<1)>>1]&k)<<16>>16)ke(d[1353+o>>0]|0,f)|0;m=o+-1|0;if((m|0)<=1)break d;k=b[v>>1]|0;o=m}}while(0);ke(10,f)|0;w=w+1|0}while((w|0)!=4);ke(10,f)|0;i=h;return}function Jc(){var a=0;c[711506]=0;a=1694560;c[a>>2]=0;c[a+4>>2]=0;a=1694568;c[a>>2]=0;c[a+4>>2]=0;c[713507]=0;a=1726576;c[a>>2]=0;c[a+4>>2]=0;a=1726584;c[a>>2]=0;c[a+4>>2]=0;return}function Kc(){Qe(2846028,0,8e3)|0;Qe(1694576,0,16e3)|0;Qe(1710576,0,16e3)|0;return}function Lc(b){b=b|0;var d=0;if(!(a[3763900]|0)){a[3763900]=1;Mc(0)}c[b+655796>>2]=0;c[b+8>>2]=15;c[b+16>>2]=25;c[b+12>>2]=0;c[b>>2]=0;c[b+20>>2]=8;d=b+24|0;c[b+655804>>2]=0;c[b+4>>2]=0;c[b+655600>>2]=0;a[b+659812>>0]=0;c[d>>2]=0;c[d+4>>2]=0;c[d+8>>2]=0;c[d+12>>2]=0;c[d+16>>2]=0;c[d+20>>2]=0;c[b+659808>>2]=c[220];return}function Mc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,j=0,k=0,l=0;a=i;i=i+32768|0;b=a;c[b>>2]=0;c[844580]=15;d=1;e=1;do{f=e<<1;e=d>>>0<f>>>0?e:f;f=e^d;g=(c[b+(f<<2)>>2]|0)>>>2;h=g|50331648;c[b+(d<<2)>>2]=h;j=h<<6;c[2854032+(d<<6)>>2]=j&-16777216;c[2854032+(d<<6)+4>>2]=g<<14&-16777216;c[2854032+(d<<6)+8>>2]=g<<22&-16777216;c[2854032+(d<<6)+12>>2]=g<<30;k=h>>>2;c[2854032+(d<<6)+16>>2]=k&16711680;c[2854032+(d<<6)+20>>2]=j&16711680;l=g<<14;c[2854032+(d<<6)+24>>2]=l&16711680;c[2854032+(d<<6)+28>>2]=g<<22&12582912;g=h>>>10;c[2854032+(d<<6)+32>>2]=g&65280;c[2854032+(d<<6)+36>>2]=k&65280;c[2854032+(d<<6)+40>>2]=j&65280;c[2854032+(d<<6)+44>>2]=l&49152;c[2854032+(d<<6)+48>>2]=h>>>18&255;c[2854032+(d<<6)+52>>2]=g&255;c[2854032+(d<<6)+56>>2]=k&255;c[2854032+(d<<6)+60>>2]=j&192;c[3378320+(d<<2)>>2]=(c[3378320+(f<<2)>>2]|0)+-1;d=d+1|0}while((d|0)!=8192);i=a;return}function Nc(a){a=a|0;var b=0;Oc(a);b=c[a+659808>>2]|0;if((b|0)==0|(b|0)==(c[220]|0))return;ge(b)|0;return}function Oc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0;b=a+655796|0;d=c[b>>2]|0;if(d){e=c[d>>2]|0;if(!e)f=d;else{d=e;while(1){e=c[d>>2]|0;if(!e){g=d;break}else d=e}c[b>>2]=g;f=g}g=f;do{Fe(c[g+12>>2]|0);f=c[b>>2]|0;c[b>>2]=c[f+4>>2];Fe(f);g=c[b>>2]|0}while((g|0)!=0)}c[a+12>>2]=0;g=a+28|0;b=a+655600|0;c[g>>2]=0;c[g+4>>2]=0;c[g+8>>2]=0;c[g+12>>2]=0;c[g+16>>2]=0;if(!(c[b>>2]|0))return;c[b>>2]=0;b=0;do{g=c[a+655408+(b<<4)>>2]|0;if(g)Fe(g);g=c[a+655408+(b<<4)+4>>2]|0;if(g)Fe(g);g=c[a+655408+(b<<4)+8>>2]|0;if(g)Fe(g);g=c[a+655408+(b<<4)+12>>2]|0;if(g)Fe(g);b=b+1|0}while((b|0)!=12);return}function Pc(a,b){a=a|0;b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;d=a+48|0;e=d+80|0;do{c[d>>2]=0;d=d+4|0}while((d|0)<(e|0));f=1;g=2;h=1;do{i=h<<1;j=f>>>0<i>>>0;g=(j&1^1)+g|0;h=j?h:i;d=a+48+(f*80|0)|0;i=a+48+((h^f)*80|0)|0;e=d+80|0;do{c[d>>2]=c[i>>2];d=d+4|0;i=i+4|0}while((d|0)<(e|0));i=a+48+(f*80|0)|0;j=(c[i>>2]|0)>>>2;k=c[b+(g<<2)>>2]<<24|j;c[i>>2]=k;i=a+48+(f*80|0)+4|0;l=(c[i>>2]|0)>>>2;m=c[b+60+(g<<2)>>2]<<24|l;c[i>>2]=m;i=a+48+(f*80|0)+8|0;n=c[i>>2]|0;o=n>>>2;p=c[b+120+(g<<2)>>2]<<24|o;c[i>>2]=p;i=a+48+(f*80|0)+12|0;q=c[i>>2]|0;r=q>>>2;s=c[b+180+(g<<2)>>2]<<24|r;c[i>>2]=s;c[a+48+(f*80|0)+16>>2]=k<<6&-16777216;c[a+48+(f*80|0)+20>>2]=j<<14&-16777216;c[a+48+(f*80|0)+24>>2]=j<<22&-16777216;c[a+48+(f*80|0)+28>>2]=j<<30;c[a+48+(f*80|0)+32>>2]=m>>>2&16711680;c[a+48+(f*80|0)+36>>2]=l<<6&16711680;c[a+48+(f*80|0)+40>>2]=l<<14&16711680;c[a+48+(f*80|0)+44>>2]=l<<22&12582912;c[a+48+(f*80|0)+48>>2]=p>>>10&65280;c[a+48+(f*80|0)+52>>2]=n>>>4&65280;c[a+48+(f*80|0)+56>>2]=o<<6&65280;c[a+48+(f*80|0)+60>>2]=o<<14&49152;c[a+48+(f*80|0)+64>>2]=s>>>18&255;c[a+48+(f*80|0)+68>>2]=q>>>12&255;c[a+48+(f*80|0)+72>>2]=q>>>4&255;c[a+48+(f*80|0)+76>>2]=r<<6&192;f=f+1|0}while((f|0)!=8192);return}function Qc(a,b){a=a|0;b=b|0;c[a+8>>2]=~~(+(b<<10|0)/6359.375);return}function Rc(a,b){a=a|0;b=b|0;c[a+16>>2]=~~(+(b<<10|0)/6359.375);return}function Sc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0;b=a+655600|0;a:do if(!(c[b>>2]|0)){c[b>>2]=1;d=0;while(1){e=Ee(133120)|0;c[a+655408+(d<<4)>>2]=e;if(!e){f=5;break}e=Ee(133120)|0;c[a+655408+(d<<4)+4>>2]=e;if(!e){f=5;break}e=Ee(133120)|0;c[a+655408+(d<<4)+8>>2]=e;if(!e){f=5;break}e=Ee(133120)|0;c[a+655408+(d<<4)+12>>2]=e;if(!e){f=5;break}d=d+1|0;if((d|0)>=12){g=0;break a}}if((f|0)==5)Va(1)}else g=0;while(0);do{f=c[a+655408+(g<<4)>>2]|0;b=0;do{c[f+(b*520|0)>>2]=0;c[f+(b*520|0)+4>>2]=0;b=b+1|0}while((b|0)!=256);c[a+655604+(g<<4)>>2]=0;b=c[a+655408+(g<<4)+4>>2]|0;f=0;do{c[b+(f*520|0)>>2]=0;c[b+(f*520|0)+4>>2]=0;f=f+1|0}while((f|0)!=256);c[a+655604+(g<<4)+4>>2]=0;f=c[a+655408+(g<<4)+8>>2]|0;b=0;do{c[f+(b*520|0)>>2]=0;c[f+(b*520|0)+4>>2]=0;b=b+1|0}while((b|0)!=256);c[a+655604+(g<<4)+8>>2]=0;b=c[a+655408+(g<<4)+12>>2]|0;f=0;do{c[b+(f*520|0)>>2]=0;c[b+(f*520|0)+4>>2]=0;f=f+1|0}while((f|0)!=256);c[a+655604+(g<<4)+12>>2]=0;g=g+1|0}while((g|0)!=12);return}function Tc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;b=a+655796|0;d=c[b>>2]|0;if(!d)return;e=a+28|0;c[e>>2]=(c[e>>2]|0)+1;e=a+12|0;f=c[e>>2]|0;g=a+44|0;h=a+32|0;c[h>>2]=f-(c[g>>2]|0)+(c[h>>2]|0);c[g>>2]=f;h=a+8|0;if((f|0)>(c[h>>2]|0)){i=d;while(1){Fe(c[i+12>>2]|0);j=c[(c[b>>2]|0)+4>>2]|0;c[b>>2]=j;Fe(c[j>>2]|0);j=c[b>>2]|0;c[j>>2]=0;k=(c[e>>2]|0)+-1|0;c[e>>2]=k;if((k|0)>(c[h>>2]|0))i=j;else{l=j;m=k;break}}n=c[g>>2]|0;o=m;p=l}else{n=f;o=f;p=d}d=a+36|0;c[d>>2]=n-o+(c[d>>2]|0);c[g>>2]=o;o=c[p+4>>2]|0;if(!o)q=p;else{p=o;while(1){o=c[p+4>>2]|0;if(!o){r=p;break}else p=o}c[b>>2]=r;q=r}c[q+8>>2]=0;c[a+655800>>2]=c[q+12>>2];q=0;do{r=c[a+655408+(q<<4)>>2]|0;b=0;do{c[r+(b*520|0)>>2]=0;c[r+(b*520|0)+4>>2]=0;b=b+1|0}while((b|0)!=256);c[a+655604+(q<<4)>>2]=0;b=c[a+655408+(q<<4)+4>>2]|0;r=0;do{c[b+(r*520|0)>>2]=0;c[b+(r*520|0)+4>>2]=0;r=r+1|0}while((r|0)!=256);c[a+655604+(q<<4)+4>>2]=0;r=c[a+655408+(q<<4)+8>>2]|0;b=0;do{c[r+(b*520|0)>>2]=0;c[r+(b*520|0)+4>>2]=0;b=b+1|0}while((b|0)!=256);c[a+655604+(q<<4)+8>>2]=0;b=c[a+655408+(q<<4)+12>>2]|0;r=0;do{c[b+(r*520|0)>>2]=0;c[b+(r*520|0)+4>>2]=0;r=r+1|0}while((r|0)!=256);c[a+655604+(q<<4)+12>>2]=0;q=q+1|0}while((q|0)!=12);c[a+4>>2]=0;c[a>>2]=0;return}function Uc(a){a=a|0;return +(+(($(c[a+12>>2]|0,6512e3)|0)+7045120|0)*.0009765625)}function Vc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;b=a+655796|0;d=c[b>>2]|0;e=d;if(!d){f=Ge(1,16)|0;c[b>>2]=f;if(!f)Va(1);g=Ee(6512e3)|0;h=f+12|0;c[h>>2]=g;if(!g)Va(1);c[f>>2]=0;c[f+4>>2]=0;c[f+8>>2]=1;f=c[h>>2]|0;h=a+12|0;c[h>>2]=(c[h>>2]|0)+1;c[a+655800>>2]=f+6512;i=f;return i|0}if((c[a>>2]|0)==1){f=a+655804|0;h=c[f>>2]|0;do if((h|0)==1e3){if(Wc(a)|0){j=c[f>>2]|0;k=0;break}Tc(a);g=(c[b>>2]|0)+8|0;c[g>>2]=(c[g>>2]|0)+1;g=a+655800|0;l=c[g>>2]|0;c[g>>2]=l+6512;i=l;return i|0}else{j=h;k=h}while(0);c[f>>2]=j+1;i=c[a+655808+(k<<2)>>2]|0;return i|0}k=d+8|0;j=c[k>>2]|0;if((j|0)!=1e3){c[k>>2]=j+1;j=a+655800|0;k=c[j>>2]|0;c[j>>2]=k+6512;i=k;return i|0}k=c[d>>2]|0;if(k){c[b>>2]=k;c[k+8>>2]=1;j=c[k+12>>2]|0;c[a+655800>>2]=j+6512;i=j;return i|0}j=a+12|0;k=c[j>>2]|0;if((k|0)==(c[a+16>>2]|0))if(Wc(a)|0){c[a>>2]=1;f=a+655804|0;c[f>>2]=(c[f>>2]|0)+1;i=c[a+655808>>2]|0;return i|0}else{Tc(a);f=(c[b>>2]|0)+8|0;c[f>>2]=(c[f>>2]|0)+1;f=a+655800|0;h=c[f>>2]|0;c[f>>2]=h+6512;i=h;return i|0}h=Ge(1,16)|0;if(!h)if(Wc(a)|0){c[a>>2]=1;f=a+655804|0;c[f>>2]=(c[f>>2]|0)+1;i=c[a+655808>>2]|0;return i|0}else{Tc(a);f=(c[b>>2]|0)+8|0;c[f>>2]=(c[f>>2]|0)+1;f=a+655800|0;l=c[f>>2]|0;c[f>>2]=l+6512;i=l;return i|0}l=Ee(6512e3)|0;f=h+12|0;c[f>>2]=l;if(l){c[h+8>>2]=1;c[h>>2]=0;c[h+4>>2]=e;c[d>>2]=h;c[b>>2]=h;h=c[f>>2]|0;c[j>>2]=k+1;c[a+655800>>2]=h+6512;i=h;return i|0}if(Wc(a)|0){c[a>>2]=1;h=a+655804|0;c[h>>2]=(c[h>>2]|0)+1;i=c[a+655808>>2]|0;return i|0}else{Tc(a);h=(c[b>>2]|0)+8|0;c[h>>2]=(c[h>>2]|0)+1;h=a+655800|0;a=c[h>>2]|0;c[h>>2]=a+6512;i=a;return i|0}return 0}function Wc(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0;b=a+24|0;d=c[b>>2]|0;e=a+20|0;f=c[e>>2]|0;g=a+4|0;h=0;i=a+655408+(f<<4)+(d<<2)|0;a:while(1){j=c[i>>2]|0;k=0;l=h;while(1){m=j+(k*520|0)|0;n=c[m>>2]|0;o=j+(k*520|0)+4|0;p=n;q=l;r=n;b:while(1){n=r;while(1){s=n+-1|0;if((n|0)<=0){t=q;break b}u=j+(k*520|0)+8+(s<<4)|0;v=c[u>>2]|0;w=c[g>>2]|0;x=v+8|0;if((w-(c[x>>2]|0)|0)>1e4){y=u;z=v;A=w;B=x;C=s;break}else n=s}c[z>>2]=0;c[z+4>>2]=0;c[B>>2]=A;c[a+655808+(q<<2)>>2]=z;n=p+-1|0;if((C|0)==(n|0))D=p;else{s=j+(k*520|0)+8+(n<<4)|0;c[y>>2]=c[s>>2];c[y+4>>2]=c[s+4>>2];c[y+8>>2]=c[s+8>>2];c[y+12>>2]=c[s+12>>2];D=c[m>>2]|0}s=D+-1|0;c[m>>2]=s;c[o>>2]=s;n=q+1|0;if((n|0)==1e3)break a;else{p=s;q=n;r=C}}k=k+1|0;if((k|0)>=256){E=t;break}else l=t}l=c[b>>2]|0;k=l+1|0;c[b>>2]=k;if((l|0)>2){c[b>>2]=0;l=c[e>>2]|0;j=(l|0)<1?8:l+-1|0;c[e>>2]=j;F=j;G=0}else{F=c[e>>2]|0;G=k}if((F|0)==(f|0)&(G|0)==(d|0)){H=0;I=19;break}h=E;i=a+655408+(F<<4)+(G<<2)|0}if((I|0)==19)return H|0;I=c[b>>2]|0;c[b>>2]=I+1;if((I|0)>2){c[b>>2]=0;b=c[e>>2]|0;c[e>>2]=(b|0)<1?8:b+-1|0}c[a+655804>>2]=0;b=a+40|0;c[b>>2]=(c[b>>2]|0)+1;H=1;return H|0}function Xc(a,b,d,f,g,h,j){a=a|0;b=b|0;d=d|0;f=f|0;g=g|0;h=h|0;j=j|0;var k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;k=i;i=i+64|0;l=k;m=c[g>>2]|0;n=Re(m|0,0,36)|0;o=D;p=c[g+4>>2]|0;q=Re(p|0,((p|0)<0)<<31>>31|0,24)|0;r=D|o;o=c[g+8>>2]|0;s=Re(o|0,((o|0)<0)<<31>>31|0,12)|0;t=c[g+12>>2]|0;g=q|n|s|t;s=r|D|((t|0)<0)<<31>>31;r=p*5^m^o*25^t*125;t=(r>>>5^r)&255;r=c[a+655408+(b<<4)+(d<<2)>>2]|0;o=r+(t*520|0)|0;m=c[o>>2]|0;do if((m|0)>0){p=0;do{n=r+(t*520|0)+8+(p<<4)+8|0;if((c[n>>2]|0)==(g|0)?(c[n+4>>2]|0)==(s|0):0){u=p;v=10;break}p=p+1|0}while((p|0)<(m|0));if((v|0)==10){p=c[r+(t*520|0)+8+(u<<4)>>2]|0;c[a+655604+(b<<4)+(d<<2)>>2]=p;n=e[f>>1]|0;q=e[f+2>>1]|0;w=e[f+4>>1]|0;x=e[f+6>>1]|0;c[l+4>>2]=c[a+48+(q*80|0)+32>>2]|c[a+48+(n*80|0)+16>>2]|c[a+48+(w*80|0)+48>>2]|c[a+48+(x*80|0)+64>>2];c[l+8>>2]=c[a+48+(q*80|0)+36>>2]|c[a+48+(n*80|0)+20>>2]|c[a+48+(w*80|0)+52>>2]|c[a+48+(x*80|0)+68>>2];c[l+12>>2]=c[a+48+(q*80|0)+40>>2]|c[a+48+(n*80|0)+24>>2]|c[a+48+(w*80|0)+56>>2]|c[a+48+(x*80|0)+72>>2];c[l+16>>2]=c[a+48+(q*80|0)+44>>2]|c[a+48+(n*80|0)+28>>2]|c[a+48+(w*80|0)+60>>2]|c[a+48+(x*80|0)+76>>2];y=Yc(a,l,p,h,j)|0;i=k;return y|0}if((m|0)==32){p=r+(t*520|0)+4|0;x=c[p>>2]|0;if((x|0)==32){c[p>>2]=1;z=0;break}else{c[p>>2]=x+1;z=x;break}}else v=8}else v=8;while(0);if((v|0)==8){v=Vc(a)|0;m=r+(t*520|0)+4|0;j=c[m>>2]|0;c[m>>2]=j+1;c[r+(t*520|0)+8+(j<<4)>>2]=v;c[v+8>>2]=c[a+4>>2];c[o>>2]=(c[o>>2]|0)+1;z=j}j=r+(t*520|0)+8+(z<<4)+8|0;c[j>>2]=g;c[j+4>>2]=s;s=c[r+(t*520|0)+8+(z<<4)>>2]|0;c[s>>2]=0;c[s+4>>2]=0;c[a+655604+(b<<4)+(d<<2)>>2]=s;y=0;i=k;return y|0}function Yc(b,d,e,f,g){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;h=c[e+4>>2]|0;i=h+-1|0;a:do if((h|0)>0){j=c[d+4>>2]|0;k=d+8|0;l=d+12|0;m=i;n=e+12+(i*52|0)|0;b:while(1){do if(!((j^c[n+4>>2])&c[n+20>>2])){o=c[n+40>>2]|0;if((o|0)!=1){if((c[k>>2]^c[n+8>>2])&c[n+24>>2])break;if((o|0)!=2?((c[l>>2]^c[n+12>>2])&c[n+28>>2]|0)!=0:0)break}o=n+44|0;if((a[n+45>>0]|0)>(f|0)){p=o;q=9;break b}if((a[o>>0]|0)<=(f|0)){r=o;q=11;break b}}while(0);if((m|0)>0){m=m+-1|0;n=n+-52|0}else break a}if((q|0)==9){n=b+4|0;m=(c[n>>2]|0)+1|0;c[n>>2]=m;c[e+8>>2]=m;a[g>>0]=1;s=p;return s|0}else if((q|0)==11){m=b+4|0;n=(c[m>>2]|0)+1|0;c[m>>2]=n;c[e+8>>2]=n;a[g>>0]=0;s=r;return s|0}}while(0);r=c[e>>2]|0;p=r+-1|0;if((r|0)<=(h|0)){s=0;return s|0}r=c[d+4>>2]|0;i=d+8|0;n=d+12|0;d=p;m=e+12+(p*52|0)|0;c:while(1){do if(!((r^c[m+4>>2])&c[m+20>>2])){p=c[m+40>>2]|0;if((p|0)!=1){if((c[i>>2]^c[m+8>>2])&c[m+24>>2])break;if((p|0)!=2?((c[n>>2]^c[m+12>>2])&c[m+28>>2]|0)!=0:0)break}p=m+44|0;if((a[m+45>>0]|0)>(f|0)){t=p;q=21;break c}if((a[p>>0]|0)<=(f|0)){u=p;q=23;break c}}while(0);if((d|0)>(h|0)){d=d+-1|0;m=m+-52|0}else{s=0;q=25;break}}if((q|0)==21){a[g>>0]=1;m=b+4|0;d=(c[m>>2]|0)+1|0;c[m>>2]=d;c[e+8>>2]=d;s=t;return s|0}else if((q|0)==23){a[g>>0]=0;g=b+4|0;b=(c[g>>2]|0)+1|0;c[g>>2]=b;c[e+8>>2]=b;s=u;return s|0}else if((q|0)==25)return s|0;return 0}function Zc(f,g,h,j,k,l,m){f=f|0;g=g|0;h=h|0;j=j|0;k=k|0;l=l|0;m=m|0;var n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;n=i;i=i+96|0;o=n+72|0;p=n+56|0;q=n+88|0;r=n;s=f+655604+(g<<4)+(h<<2)|0;if(!(c[s>>2]|0)){i=n;return}h=l;l=h;g=h+4|0;h=d[g>>0]|d[g+1>>0]<<8|d[g+2>>0]<<16|d[g+3>>0]<<24;g=r+44|0;c[g>>2]=d[l>>0]|d[l+1>>0]<<8|d[l+2>>0]<<16|d[l+3>>0]<<24;c[g+4>>2]=h;c[r>>2]=0;h=0;do{g=b[k+(h<<1)>>1]|0;l=g&65535;if(!(g<<16>>16)){a[r+48+h>>0]=0;t=15;u=2854032+(h<<4)|0;v=f+64+(h<<4)|0}else{g=e[j+(h<<1)>>1]&0-(l&0-l);l=c[3378320+(g<<2)>>2]|0;a[r+48+h>>0]=15-l;c[r>>2]=c[r>>2]^c[f+48+(g*80|0)+(h<<2)>>2];t=l&255;u=2854032+(g<<6)+(h<<4)|0;v=f+48+(g*80|0)+16+(h<<4)|0}c[o+(h<<2)>>2]=v;c[p+(h<<2)>>2]=u;a[q+h>>0]=t;h=h+1|0}while((h|0)!=4);h=c[o>>2]|0;t=c[o+4>>2]|0;u=c[o+8>>2]|0;v=c[o+12>>2]|0;o=r+4|0;c[o>>2]=c[t>>2]|c[h>>2]|c[u>>2]|c[v>>2];f=r+8|0;c[f>>2]=c[t+4>>2]|c[h+4>>2]|c[u+4>>2]|c[v+4>>2];j=c[t+8>>2]|c[h+8>>2]|c[u+8>>2]|c[v+8>>2];c[r+12>>2]=j;c[r+16>>2]=c[t+12>>2]|c[h+12>>2]|c[u+12>>2]|c[v+12>>2];v=c[p>>2]|0;u=c[p+4>>2]|0;h=c[p+8>>2]|0;t=c[p+12>>2]|0;c[r+20>>2]=c[u>>2]|c[v>>2]|c[h>>2]|c[t>>2];p=c[u+4>>2]|c[v+4>>2]|c[h+4>>2]|c[t+4>>2];c[r+24>>2]=p;k=c[u+8>>2]|c[v+8>>2]|c[h+8>>2]|c[t+8>>2];c[r+28>>2]=k;g=c[u+12>>2]|c[v+12>>2]|c[h+12>>2]|c[t+12>>2];c[r+32>>2]=g;t=a[q+1>>0]<<8|a[q>>0]<<12|a[q+2>>0]<<4|a[q+3>>0];c[r+36>>2]=t;do if(p){if(!k){c[r+40>>2]=2;break}q=r+40|0;if(!g){c[q>>2]=3;break}else{c[q>>2]=4;break}}else c[r+40>>2]=1;while(0);g=c[s>>2]|0;s=c[g>>2]|0;do if((s|0)>0){k=c[r>>2]|0;p=c[o>>2]|0;q=c[f>>2]|0;h=0;v=g+12|0;while(1){if(((((c[v>>2]|0)==(k|0)?(c[v+36>>2]|0)==(t|0):0)?(c[v+4>>2]|0)==(p|0):0)?(c[v+8>>2]|0)==(q|0):0)?(c[v+12>>2]|0)==(j|0):0){w=v;break}h=h+1|0;if((h|0)>=(s|0)){x=27;break}else v=v+52|0}if((x|0)==27){if((s|0)!=125){x=30;break}v=g+4|0;h=c[v>>2]|0;if((h|0)<=124){y=v;z=h;x=31;break}c[v>>2]=0;y=v;z=0;x=31;break}v=a[r+45>>0]|0;h=w+45|0;if(v<<24>>24>(a[h>>0]|0))a[h>>0]=v;v=c[r+44>>2]|0;h=v&255;q=w+44|0;if(h<<24>>24<(a[q>>0]|0))a[q>>0]=h;a[w+46>>0]=v>>>16;a[w+47>>0]=v>>>24}else x=30;while(0);if((x|0)==30){c[g>>2]=s+1;s=g+4|0;y=s;z=c[s>>2]|0;x=31}if((x|0)==31){c[y>>2]=z+1;y=g+12+(z*52|0)|0;x=r;r=y+52|0;do{c[y>>2]=c[x>>2];y=y+4|0;x=x+4|0}while((y|0)<(r|0));if(!m){a[g+12+(z*52|0)+46>>0]=0;a[g+12+(z*52|0)+47>>0]=0}}i=n;return}function _c(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,j=0,k=0,l=0,m=0;e=i;i=i+32|0;f=e;g=e+16|0;h=e+4|0;j=Ee(256)|0;c[g>>2]=d;k=pe(j,256,b,g)|0;if((k|0)<0){Ie(a,3763902,0);i=e;return}if((k|0)>255){Fe(j);l=k+1|0;c[f>>2]=l;ne(2498,f)|0;f=Ee(l)|0;c[g>>2]=d;if((pe(f,l,b,g)|0)>(k|0)){Ie(a,3763902,0);i=e;return}else m=f}else m=j;Ie(h,m,ve(m)|0);Fe(m);He(a,h);Ke(h);i=e;return}function $c(a){a=a|0;var b=0,d=0;b=i;i=i+16|0;d=b;b=c[219]|0;c[d>>2]=a;je(b,2512,d)|0;Va(1)}function ad(a){a=a|0;var b=0,d=0;b=i;i=i+16|0;d=b;b=c[219]|0;c[d>>2]=a;je(b,2558,d)|0;Va(1)}function bd(b,d,f,g,h){b=b|0;d=d|0;f=f|0;g=g|0;h=h|0;var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;j=i;i=i+48|0;k=j+16|0;l=j;m=j+32|0;n=j+20|0;o=g>>>2;c[m>>2]=0;c[m+4>>2]=0;c[m+8>>2]=0;g=n+8|0;p=n+1|0;q=n+4|0;r=15;s=0;while(1){if(!(e[1256+(r<<1)>>1]&o))t=s;else{if(s)Le(m,2660)|0;c[k>>2]=a[2473+r>>0];_c(n,2662,k);u=a[n>>0]|0;v=(u&1)==0;Me(m,v?p:c[g>>2]|0,v?(u&255)>>>1:c[q>>2]|0)|0;Ke(n);t=s+1|0}r=r+-1|0;if((r|0)<=1)break;else s=t}t=(a[m>>0]&1)==0?m+1|0:c[m+8>>2]|0;c[l>>2]=d<<24>>24;c[l+4>>2]=f<<24>>24;c[l+8>>2]=t;c[l+12>>2]=h;_c(b,2602,l);Ke(m);i=j;return}function cd(b,e,f,g,h){b=b|0;e=e|0;f=f|0;g=g|0;h=h|0;var j=0,k=0,l=0,m=0,n=0,o=0;j=i;i=i+32|0;k=j;l=j+12|0;c[k>>2]=d[2494+f>>0];c[k+4>>2]=g;c[k+8>>2]=h;_c(b,2667,k);k=e+4|0;if((c[k>>2]|0)<=0){Le(b,2726)|0;i=j;return}h=l+8|0;g=l+1|0;f=l+4|0;m=0;do{if(m)Le(b,2660)|0;bd(l,a[2489+(c[e+8+(m<<2)>>2]|0)>>0]|0,a[2473+(c[e+60+(m<<2)>>2]|0)>>0]|0,c[e+112+(m<<2)>>2]|0,c[e+164+(m<<2)>>2]|0);n=a[l>>0]|0;o=(n&1)==0;Me(b,o?g:c[h>>2]|0,o?(n&255)>>>1:c[f>>2]|0)|0;Ke(l);m=m+1|0}while((m|0)<(c[k>>2]|0));Le(b,2726)|0;i=j;return}function dd(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;e=c[a+20>>2]|0;if(!e){f=c[219]|0;me(2732,42,1,f)|0;Va(1)}g=c[a+24>>2]|0;if(!g){f=c[219]|0;me(2732,42,1,f)|0;Va(1)}h=c[a+28>>2]|0;if(!h){f=c[219]|0;me(2732,42,1,f)|0;Va(1)}f=a+8|0;i=c[f>>2]|0;j=c[a+12>>2]|0;k=c[a+16>>2]|0;l=a+4|0;m=c[l>>2]|0;n=c[752+(m<<2)>>2]|0;if((j|0)==(i|0)&(g|0)>(e|0)){o=g;p=i;q=n}else{r=c[a>>2]|0;s=(j|0)!=(r|0)|(i|0)==(r|0);o=s?e:g;p=s?i:j;q=s?m:n}m=c[752+(n<<2)>>2]|0;if((k|0)==(p|0)&(h|0)>(o|0)){t=h;u=k;v=m}else{n=c[a>>2]|0;s=(k|0)!=(n|0)|(p|0)==(n|0);t=s?o:h;u=s?p:k;v=s?q:m}q=c[752+(m<<2)>>2]|0;if((u|0)==(b|0)&(t|0)<(d|0)){w=q;c[f>>2]=0;c[f+4>>2]=0;c[f+8>>2]=0;c[f+12>>2]=0;c[f+16>>2]=0;c[f+20>>2]=0;c[l>>2]=w;return w|0}d=c[a>>2]|0;w=(d|0)!=(b|0)|(u|0)==(d|0)?v:q;c[f>>2]=0;c[f+4>>2]=0;c[f+8>>2]=0;c[f+12>>2]=0;c[f+16>>2]=0;c[f+20>>2]=0;c[l>>2]=w;return w|0}function ed(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;e=i;i=i+160|0;f=e+80|0;g=e+64|0;h=e+48|0;j=e+32|0;k=e+16|0;l=e;m=e+148|0;n=e+136|0;o=e+124|0;p=e+112|0;q=e+100|0;r=c[d+72>>2]|0;s=c[d+68>>2]|0;t=c[d+76>>2]|0;c[l>>2]=c[d+64>>2];c[l+4>>2]=r;c[l+8>>2]=s;c[l+12>>2]=t;_c(m,2775,l);l=(a[m>>0]&1)==0?m+1|0:c[m+8>>2]|0;t=c[d+8>>2]|0;s=c[d+4>>2]|0;r=c[d+12>>2]|0;c[k>>2]=c[d>>2];c[k+4>>2]=t;c[k+8>>2]=s;c[k+12>>2]=r;_c(n,2775,k);k=(a[n>>0]&1)==0?n+1|0:c[n+8>>2]|0;r=c[d+24>>2]|0;s=c[d+20>>2]|0;t=c[d+28>>2]|0;c[j>>2]=c[d+16>>2];c[j+4>>2]=r;c[j+8>>2]=s;c[j+12>>2]=t;_c(o,2775,j);j=(a[o>>0]&1)==0?o+1|0:c[o+8>>2]|0;t=c[d+40>>2]|0;s=c[d+36>>2]|0;r=c[d+44>>2]|0;c[h>>2]=c[d+32>>2];c[h+4>>2]=t;c[h+8>>2]=s;c[h+12>>2]=r;_c(p,2775,h);h=a[p>>0]|0;r=c[p+8>>2]|0;s=c[d+56>>2]|0;t=c[d+52>>2]|0;u=c[d+60>>2]|0;c[g>>2]=c[d+48>>2];c[g+4>>2]=s;c[g+8>>2]=t;c[g+12>>2]=u;_c(q,2775,g);g=(a[q>>0]&1)==0?q+1|0:c[q+8>>2]|0;c[f>>2]=l;c[f+4>>2]=k;c[f+8>>2]=j;c[f+12>>2]=(h&1)==0?p+1|0:r;c[f+16>>2]=g;_c(b,2812,f);Ke(q);Ke(p);Ke(o);Ke(n);Ke(m);i=e;return}function fd(b,e,f,g){b=b|0;e=e|0;f=f|0;g=g|0;var h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0;h=i;i=i+512|0;j=h+416|0;k=h+16|0;l=h+8|0;m=h;n=h+304|0;o=h+320|0;p=h+72|0;q=h+60|0;r=h+48|0;s=h+88|0;t=h+36|0;u=h+24|0;if(!(a[3763901]|0)){yb(0);a[3763901]=1}Je(n,1,a[b>>0]|0);v=(a[n>>0]&1)==0?n+1|0:c[n+8>>2]|0;if((ve(v)|0)!=1)ad(v);switch((Pd(a[v>>0]|0)|0)<<24>>24|0){case 78:{w=0;break}case 83:{w=2;break}case 69:{w=1;break}case 87:{w=3;break}default:ad(v)}Ke(n);if((ve(e)|0)!=1)$c(e);switch((Pd(a[e>>0]|0)|0)<<24>>24|0){case 78:{x=4;break}case 83:{x=0;break}case 72:{x=1;break}case 68:{x=2;break}case 67:{x=3;break}default:$c(e)}c[o>>2]=x;c[o+4>>2]=w;x=kc(b,o+32|0)|0;if((x|0)!=1){Fb(x,j);c[m>>2]=x;c[m+4>>2]=j;_c(p,2854,m);m=a[p>>0]|0;x=(m&1)==0;b=Ee((x?(m&255)>>>1:c[p+4>>2]|0)+1|0)|0;ue(b,x?p+1|0:c[p+8>>2]|0)|0;Ke(p);y=b;i=h;return y|0}b=o+8|0;c[b>>2]=0;c[b+4>>2]=0;c[b+8>>2]=0;c[b+12>>2]=0;c[b+16>>2]=0;c[b+20>>2]=0;a:do if((f|0)>0){b=0;p=0;x=0;m=w;while(1){e=g+(p<<3)|0;n=c[e>>2]|0;v=o+32+(m<<4)+(n<<2)|0;z=c[v>>2]|0;A=g+(p<<3)+4|0;B=c[A>>2]|0;C=1<<B;if(!(C&z)){D=e;E=A;F=m;break}c[v>>2]=z-C;C=(p|0)%4|0;do if((C|0)==3){z=dd(o,n,B)|0;if(!(z&-3)){G=b;H=x+1|0;I=z;break}else{G=b+1|0;H=x;I=z;break}}else{c[o+8+(C<<2)>>2]=n;c[o+20+(C<<2)>>2]=B;G=b;H=x;I=c[752+(m<<2)>>2]|0}while(0);p=p+1|0;if((p|0)>=(f|0)){J=G;K=H;L=I;break a}else{b=G;x=H;m=I}}m=d[2494+F>>0]|0;Ie(r,2729,2);x=c[E>>2]|0;b:do if((x|0)<10)M=x+48&255;else switch(x|0){case 10:{M=84;break b;break}case 11:{M=74;break b;break}case 12:{M=81;break b;break}case 13:{M=75;break b;break}case 14:{M=65;break b;break}default:{M=63;break b}}while(0);x=r+8|0;b=r+1|0;a[((a[r>>0]&1)==0?b:c[x>>2]|0)>>0]=M;a[((a[r>>0]&1)==0?b:c[x>>2]|0)+1>>0]=a[2489+(c[D>>2]|0)>>0]|0;p=(a[r>>0]&1)==0?b:c[x>>2]|0;c[l>>2]=m;c[l+4>>2]=p;_c(q,2908,l);p=a[q>>0]|0;x=(p&1)==0;b=Ee((x?(p&255)>>>1:c[q+4>>2]|0)+1|0)|0;ue(b,x?q+1|0:c[q+8>>2]|0)|0;Ke(q);Ke(r);y=b;i=h;return y|0}else{J=0;K=0;L=w}while(0);w=j;r=o;o=w+96|0;do{c[w>>2]=c[r>>2];w=w+4|0;r=r+4|0}while((w|0)<(o|0));r=Cc(j,-1,3,1,s,0)|0;if((r|0)==1){cd(u,s,L,K,J);J=a[u>>0]|0;K=(J&1)==0;L=Ee((K?(J&255)>>>1:c[u+4>>2]|0)+1|0)|0;ue(L,K?u+1|0:c[u+8>>2]|0)|0;Ke(u);N=L}else{Fb(r,j);c[k>>2]=r;c[k+4>>2]=j;_c(t,2960,k);k=a[t>>0]|0;j=(k&1)==0;r=Ee((j?(k&255)>>>1:c[t+4>>2]|0)+1|0)|0;ue(r,j?t+1|0:c[t+8>>2]|0)|0;Ke(t);N=r}y=N;i=h;return y|0}function gd(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,j=0,k=0,l=0,m=0;d=i;i=i+272|0;e=d+192|0;f=d;g=d+32|0;h=d+112|0;j=d+20|0;k=d+8|0;if(!(a[3763901]|0)){yb(0);a[3763901]=1}ue(h,b)|0;b=e;l=h;h=b+80|0;do{a[b>>0]=a[l>>0]|0;b=b+1|0;l=l+1|0}while((b|0)<(h|0));l=xb(e,g)|0;if((l|0)==1){ed(k,g);g=a[k>>0]|0;b=(g&1)==0;h=Ee((b?(g&255)>>>1:c[k+4>>2]|0)+1|0)|0;ue(h,b?k+1|0:c[k+8>>2]|0)|0;Ke(k);m=h;i=d;return m|0}else{Fb(l,e);c[f>>2]=l;c[f+4>>2]=e;_c(j,2991,f);f=a[j>>0]|0;e=(f&1)==0;l=Ee((e?(f&255)>>>1:c[j+4>>2]|0)+1|0)|0;ue(l,e?j+1|0:c[j+8>>2]|0)|0;Ke(j);m=l;i=d;return m|0}return 0}function hd(a,b){a=a|0;b=b|0;var d=0,e=0;d=i;i=i+16|0;e=d;c[e>>2]=b;b=c[219]|0;oe(b,a,e)|0;ke(10,b)|0;sa()}function id(){var a=0,b=0;a=i;i=i+16|0;if(!(Ha(3411096,2)|0)){b=Fa(c[852773]|0)|0;i=a;return b|0}else hd(3265,a);return 0}function jd(a){a=a|0;var b=0,d=0,e=0;b=(a|0)==0?1:a;while(1){a=Ee(b)|0;if(a){d=a;e=6;break}a=qd()|0;if(!a){e=5;break}cb[a&3]()}if((e|0)==5){b=wa(4)|0;c[b>>2]=780;Na(b|0,312,1)}else if((e|0)==6)return d|0;return 0}function kd(a){a=a|0;Fe(a);return}function ld(a){a=a|0;return}function md(a){a=a|0;kd(a);return}function nd(a){a=a|0;return 3314}function od(a){a=a|0;var b=0;b=i;i=i+16|0;cb[a&3]();hd(3329,b)}function pd(){var a=0,b=0;a=id()|0;if(((a|0)!=0?(b=c[a>>2]|0,(b|0)!=0):0)?(a=b+48|0,(c[a>>2]&-256|0)==1126902528?(c[a+4>>2]|0)==1129074247:0):0)od(c[b+12>>2]|0);b=c[192]|0;c[192]=b+0;od(b)}function qd(){var a=0;a=c[852772]|0;c[852772]=a+0;return a|0}function rd(a){a=a|0;return}function sd(a){a=a|0;return}function td(a){a=a|0;return}function ud(a){a=a|0;return}function vd(a){a=a|0;return}function wd(a){a=a|0;kd(a);return}function xd(a){a=a|0;kd(a);return}function yd(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,j=0,k=0;e=i;i=i+64|0;f=e;if((a|0)!=(b|0))if((b|0)!=0?(g=Cd(b,344,360,0)|0,(g|0)!=0):0){b=f;h=b+56|0;do{c[b>>2]=0;b=b+4|0}while((b|0)<(h|0));c[f>>2]=g;c[f+8>>2]=a;c[f+12>>2]=-1;c[f+48>>2]=1;fb[c[(c[g>>2]|0)+28>>2]&3](g,f,c[d>>2]|0,1);if((c[f+24>>2]|0)==1){c[d>>2]=c[f+16>>2];j=1}else j=0;k=j}else k=0;else k=1;i=e;return k|0}function zd(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0;b=d+16|0;g=c[b>>2]|0;do if(g){if((g|0)!=(e|0)){h=d+36|0;c[h>>2]=(c[h>>2]|0)+1;c[d+24>>2]=2;a[d+54>>0]=1;break}h=d+24|0;if((c[h>>2]|0)==2)c[h>>2]=f}else{c[b>>2]=e;c[d+24>>2]=f;c[d+36>>2]=1}while(0);return}function Ad(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;if((a|0)==(c[b+8>>2]|0))zd(0,b,d,e);return}function Bd(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0;if((a|0)==(c[b+8>>2]|0))zd(0,b,d,e);else{f=c[a+8>>2]|0;fb[c[(c[f>>2]|0)+28>>2]&3](f,b,d,e)}return}function Cd(d,e,f,g){d=d|0;e=e|0;f=f|0;g=g|0;var h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;h=i;i=i+64|0;j=h;k=c[d>>2]|0;l=d+(c[k+-8>>2]|0)|0;m=c[k+-4>>2]|0;c[j>>2]=f;c[j+4>>2]=d;c[j+8>>2]=e;c[j+12>>2]=g;g=j+16|0;e=j+20|0;d=j+24|0;k=j+28|0;n=j+32|0;o=j+40|0;p=(m|0)==(f|0);q=g;r=q+36|0;do{c[q>>2]=0;q=q+4|0}while((q|0)<(r|0));b[g+36>>1]=0;a[g+38>>0]=0;a:do if(p){c[j+48>>2]=1;eb[c[(c[f>>2]|0)+20>>2]&3](f,j,l,l,1,0);s=(c[d>>2]|0)==1?l:0}else{_a[c[(c[m>>2]|0)+24>>2]&3](m,j,l,1,0);switch(c[j+36>>2]|0){case 0:{s=(c[o>>2]|0)==1&(c[k>>2]|0)==1&(c[n>>2]|0)==1?c[e>>2]|0:0;break a;break}case 1:break;default:{s=0;break a}}if((c[d>>2]|0)!=1?!((c[o>>2]|0)==0&(c[k>>2]|0)==1&(c[n>>2]|0)==1):0){s=0;break}s=c[g>>2]|0}while(0);i=h;return s|0}function Dd(b,d,e,f,g){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0;a[d+53>>0]=1;do if((c[d+4>>2]|0)==(f|0)){a[d+52>>0]=1;b=d+16|0;h=c[b>>2]|0;if(!h){c[b>>2]=e;c[d+24>>2]=g;c[d+36>>2]=1;if(!((g|0)==1?(c[d+48>>2]|0)==1:0))break;a[d+54>>0]=1;break}if((h|0)!=(e|0)){h=d+36|0;c[h>>2]=(c[h>>2]|0)+1;a[d+54>>0]=1;break}h=d+24|0;b=c[h>>2]|0;if((b|0)==2){c[h>>2]=g;i=g}else i=b;if((i|0)==1?(c[d+48>>2]|0)==1:0)a[d+54>>0]=1}while(0);return}function Ed(b,d,e,f,g){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;do if((b|0)==(c[d+8>>2]|0)){if((c[d+4>>2]|0)==(e|0)?(h=d+28|0,(c[h>>2]|0)!=1):0)c[h>>2]=f}else{if((b|0)!=(c[d>>2]|0)){h=c[b+8>>2]|0;_a[c[(c[h>>2]|0)+24>>2]&3](h,d,e,f,g);break}if((c[d+16>>2]|0)!=(e|0)?(h=d+20|0,(c[h>>2]|0)!=(e|0)):0){c[d+32>>2]=f;i=d+44|0;if((c[i>>2]|0)==4)break;j=d+52|0;a[j>>0]=0;k=d+53|0;a[k>>0]=0;l=c[b+8>>2]|0;eb[c[(c[l>>2]|0)+20>>2]&3](l,d,e,e,1,g);if(a[k>>0]|0)if(!(a[j>>0]|0)){m=1;n=13}else o=1;else{m=0;n=13}if((n|0)==13){c[h>>2]=e;h=d+40|0;c[h>>2]=(c[h>>2]|0)+1;if((c[d+36>>2]|0)==1?(c[d+24>>2]|0)==2:0){a[d+54>>0]=1;o=m}else o=m}c[i>>2]=o?3:4;break}if((f|0)==1)c[d+32>>2]=1}while(0);return}function Fd(b,d,e,f,g){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;do if((b|0)==(c[d+8>>2]|0)){if((c[d+4>>2]|0)==(e|0)?(g=d+28|0,(c[g>>2]|0)!=1):0)c[g>>2]=f}else if((b|0)==(c[d>>2]|0)){if((c[d+16>>2]|0)!=(e|0)?(g=d+20|0,(c[g>>2]|0)!=(e|0)):0){c[d+32>>2]=f;c[g>>2]=e;g=d+40|0;c[g>>2]=(c[g>>2]|0)+1;if((c[d+36>>2]|0)==1?(c[d+24>>2]|0)==2:0)a[d+54>>0]=1;c[d+44>>2]=4;break}if((f|0)==1)c[d+32>>2]=1}while(0);return}function Gd(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0;if((a|0)==(c[b+8>>2]|0))Dd(0,b,d,e,f);else{h=c[a+8>>2]|0;eb[c[(c[h>>2]|0)+20>>2]&3](h,b,d,e,f,g)}return}function Hd(a,b,d,e,f,g){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;if((a|0)==(c[b+8>>2]|0))Dd(0,b,d,e,f);return}function Id(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0;e=i;i=i+16|0;f=e;c[f>>2]=c[d>>2];g=Za[c[(c[a>>2]|0)+16>>2]&7](a,b,f)|0;if(g)c[d>>2]=c[f>>2];i=e;return g&1|0}function Jd(a){a=a|0;var b=0;if(!a)b=0;else b=(Cd(a,344,392,0)|0)!=0;return b&1|0}function Kd(){var a=0,b=0,d=0,e=0,f=0,g=0,h=0,j=0,k=0,l=0;a=i;i=i+48|0;b=a+32|0;d=a+24|0;e=a+16|0;f=a;g=a+36|0;a=id()|0;if((a|0)!=0?(h=c[a>>2]|0,(h|0)!=0):0){a=h+48|0;j=c[a>>2]|0;k=c[a+4>>2]|0;if(!((j&-256|0)==1126902528&(k|0)==1129074247)){c[d>>2]=c[218];hd(3697,d)}if((j|0)==1126902529&(k|0)==1129074247)l=c[h+44>>2]|0;else l=h+80|0;c[g>>2]=l;l=c[h>>2]|0;h=c[l+4>>2]|0;if(Za[c[(c[82]|0)+16>>2]&7](328,l,g)|0){l=c[g>>2]|0;g=c[218]|0;k=bb[c[(c[l>>2]|0)+8>>2]&3](l)|0;c[f>>2]=g;c[f+4>>2]=h;c[f+8>>2]=k;hd(3611,f)}else{c[e>>2]=c[218];c[e+4>>2]=h;hd(3656,e)}}hd(3735,b)}function Ld(){var a=0;a=i;i=i+16|0;if(!(Ia(3411092,10)|0)){i=a;return}else hd(3369,a)}function Md(a){a=a|0;var b=0;b=i;i=i+16|0;Fe(a);if(!(La(c[852773]|0,0)|0)){i=b;return}else hd(3419,b)}function Nd(a){a=a|0;va(3472,3501,1164,3590)}function Od(a){a=a|0;return (a+-97|0)>>>0<26|0}function Pd(a){a=a|0;var b=0;b=(Od(a)|0)==0;return (b?a:a&95)|0}function Qd(){var a=0;if(!(c[852775]|0))a=3411144;else a=c[(Ga()|0)+60>>2]|0;return a|0}function Rd(b){b=b|0;var c=0,e=0,f=0,g=0,h=0,i=0,j=0;c=0;while(1){if((d[3756+c>>0]|0)==(b|0)){e=c;f=2;break}c=c+1|0;if((c|0)==87){g=87;h=3844;f=5;break}}if((f|0)==2)if(!e)i=3844;else{g=e;h=3844;f=5}if((f|0)==5)while(1){f=0;e=h;while(1){c=e+1|0;if(!(a[e>>0]|0)){j=c;break}else e=c}g=g+-1|0;if(!g){i=j;break}else{h=j;f=5}}return i|0}function Sd(a){a=a|0;var b=0;if(a>>>0>4294963200){c[(Qd()|0)>>2]=0-a;b=-1}else b=a;return b|0}function Td(a,b){a=+a;b=b|0;var d=0,e=0,f=0,g=0,i=0.0,j=0.0,l=0,m=0.0;h[k>>3]=a;d=c[k>>2]|0;e=c[k+4>>2]|0;f=Te(d|0,e|0,52)|0;g=f&2047;switch(g|0){case 0:{if(a!=0.0){i=+Td(a*18446744073709551616.0,b);j=i;l=(c[b>>2]|0)+-64|0}else{j=a;l=0}c[b>>2]=l;m=j;break}case 2047:{m=a;break}default:{c[b>>2]=g+-1022;c[k>>2]=d;c[k+4>>2]=e&-2146435073|1071644672;m=+h[k>>3]}}return +m}function Ud(a,b){a=+a;b=b|0;return +(+Td(a,b))}function Vd(b,d,e){b=b|0;d=d|0;e=e|0;var f=0;do if(b){if(d>>>0<128){a[b>>0]=d;f=1;break}if(d>>>0<2048){a[b>>0]=d>>>6|192;a[b+1>>0]=d&63|128;f=2;break}if(d>>>0<55296|(d&-8192|0)==57344){a[b>>0]=d>>>12|224;a[b+1>>0]=d>>>6&63|128;a[b+2>>0]=d&63|128;f=3;break}if((d+-65536|0)>>>0<1048576){a[b>>0]=d>>>18|240;a[b+1>>0]=d>>>12&63|128;a[b+2>>0]=d>>>6&63|128;a[b+3>>0]=d&63|128;f=4;break}else{c[(Qd()|0)>>2]=84;f=-1;break}}else f=1;while(0);return f|0}function Wd(a,b){a=a|0;b=b|0;var c=0;if(!a)c=0;else c=Vd(a,b,0)|0;return c|0}function Xd(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;e=i;i=i+112|0;f=e+40|0;g=e+24|0;h=e+16|0;j=e;k=e+52|0;l=a[d>>0]|0;if(qe(5648,l<<24>>24,4)|0){m=Ee(1144)|0;if(!m)n=0;else{o=m;p=o+112|0;do{c[o>>2]=0;o=o+4|0}while((o|0)<(p|0));if(!(se(d,43)|0))c[m>>2]=l<<24>>24==114?8:4;if(!(se(d,101)|0))q=l;else{c[j>>2]=b;c[j+4>>2]=2;c[j+8>>2]=1;oa(221,j|0)|0;q=a[d>>0]|0}if(q<<24>>24==97){c[h>>2]=b;c[h+4>>2]=3;q=oa(221,h|0)|0;if(!(q&1024)){c[g>>2]=b;c[g+4>>2]=4;c[g+8>>2]=q|1024;oa(221,g|0)|0}g=c[m>>2]|128;c[m>>2]=g;r=g}else r=c[m>>2]|0;c[m+60>>2]=b;c[m+44>>2]=m+120;c[m+48>>2]=1024;g=m+75|0;a[g>>0]=-1;if((r&8|0)==0?(c[f>>2]=b,c[f+4>>2]=21505,c[f+8>>2]=k,(qa(54,f|0)|0)==0):0)a[g>>0]=10;c[m+32>>2]=6;c[m+36>>2]=3;c[m+40>>2]=4;c[m+12>>2]=2;if(!(c[852776]|0))c[m+76>>2]=-1;Pa(3411128);g=c[852781]|0;c[m+56>>2]=g;if(g)c[g+52>>2]=m;c[852781]=m;Ja(3411128);n=m}}else{c[(Qd()|0)>>2]=22;n=0}i=e;return n|0}function Yd(b){b=b|0;var c=0,d=0,e=0,f=0;c=(se(b,43)|0)==0;d=a[b>>0]|0;e=c?d<<24>>24!=114&1:2;c=(se(b,120)|0)==0;f=c?e:e|128;e=(se(b,101)|0)==0;b=e?f:f|524288;f=d<<24>>24==114?b:b|64;b=d<<24>>24==119?f|512:f;return (d<<24>>24==97?b|1024:b)|0}function Zd(a){a=a|0;return 0}function _d(a){a=a|0;return}function $d(b,e){b=b|0;e=e|0;var f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0;f=i;i=i+16|0;g=f;h=e&255;a[g>>0]=h;j=b+16|0;k=c[j>>2]|0;if(!k)if(!(fe(b)|0)){l=c[j>>2]|0;m=4}else n=-1;else{l=k;m=4}do if((m|0)==4){k=b+20|0;j=c[k>>2]|0;if(j>>>0<l>>>0?(o=e&255,(o|0)!=(a[b+75>>0]|0)):0){c[k>>2]=j+1;a[j>>0]=h;n=o;break}if((Za[c[b+36>>2]&7](b,g,1)|0)==1)n=d[g>>0]|0;else n=-1}while(0);i=f;return n|0}function ae(a){a=a|0;var b=0,d=0;b=i;i=i+16|0;d=b;c[d>>2]=c[a+60>>2];a=Sd(Qa(6,d|0)|0)|0;i=b;return a|0}function be(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;f=i;i=i+48|0;g=f+16|0;h=f;j=f+32|0;c[j>>2]=d;k=j+4|0;l=b+48|0;m=c[l>>2]|0;c[k>>2]=e-((m|0)!=0&1);n=b+44|0;c[j+8>>2]=c[n>>2];c[j+12>>2]=m;if(!(c[852775]|0)){c[g>>2]=c[b+60>>2];c[g+4>>2]=j;c[g+8>>2]=2;o=Sd(Wa(145,g|0)|0)|0}else{ra(11,b|0);c[h>>2]=c[b+60>>2];c[h+4>>2]=j;c[h+8>>2]=2;j=Sd(Wa(145,h|0)|0)|0;na(0);o=j}if((o|0)>=1){j=c[k>>2]|0;if(o>>>0>j>>>0){k=c[n>>2]|0;n=b+4|0;c[n>>2]=k;h=k;c[b+8>>2]=h+(o-j);if(!(c[l>>2]|0))p=e;else{c[n>>2]=h+1;a[d+(e+-1)>>0]=a[h>>0]|0;p=e}}else p=o}else{c[b>>2]=c[b>>2]|o&48^16;c[b+8>>2]=0;c[b+4>>2]=0;p=o}i=f;return p|0}function ce(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0;e=i;i=i+32|0;f=e;g=e+20|0;c[f>>2]=c[a+60>>2];c[f+4>>2]=0;c[f+8>>2]=b;c[f+12>>2]=g;c[f+16>>2]=d;if((Sd(Ua(140,f|0)|0)|0)<0){c[g>>2]=-1;h=-1}else h=c[g>>2]|0;i=e;return h|0}function de(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;e=i;i=i+48|0;f=e+16|0;g=e;h=e+32|0;j=a+28|0;k=c[j>>2]|0;c[h>>2]=k;l=a+20|0;m=(c[l>>2]|0)-k|0;c[h+4>>2]=m;c[h+8>>2]=b;c[h+12>>2]=d;b=a+60|0;k=a+44|0;n=h;h=2;o=m+d|0;while(1){if(!(c[852775]|0)){c[f>>2]=c[b>>2];c[f+4>>2]=n;c[f+8>>2]=h;p=Sd(Xa(146,f|0)|0)|0}else{ra(12,a|0);c[g>>2]=c[b>>2];c[g+4>>2]=n;c[g+8>>2]=h;m=Sd(Xa(146,g|0)|0)|0;na(0);p=m}if((o|0)==(p|0)){q=6;break}if((p|0)<0){r=n;s=h;q=8;break}m=o-p|0;t=c[n+4>>2]|0;if(p>>>0<=t>>>0)if((h|0)==2){c[j>>2]=(c[j>>2]|0)+p;u=t;v=p;w=n;x=2}else{u=t;v=p;w=n;x=h}else{y=c[k>>2]|0;c[j>>2]=y;c[l>>2]=y;u=c[n+12>>2]|0;v=p-t|0;w=n+8|0;x=h+-1|0}c[w>>2]=(c[w>>2]|0)+v;c[w+4>>2]=u-v;n=w;h=x;o=m}if((q|0)==6){o=c[k>>2]|0;c[a+16>>2]=o+(c[a+48>>2]|0);k=o;c[j>>2]=k;c[l>>2]=k;z=d}else if((q|0)==8){c[a+16>>2]=0;c[j>>2]=0;c[l>>2]=0;c[a>>2]=c[a>>2]|32;if((s|0)==2)z=0;else z=d-(c[r+4>>2]|0)|0}i=e;return z|0}function ee(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0;f=i;i=i+80|0;g=f;c[b+36>>2]=3;if((c[b>>2]&64|0)==0?(c[g>>2]=c[b+60>>2],c[g+4>>2]=21505,c[g+8>>2]=f+12,(qa(54,g|0)|0)!=0):0)a[b+75>>0]=-1;g=de(b,d,e)|0;i=f;return g|0}function fe(b){b=b|0;var d=0,e=0,f=0;d=b+74|0;e=a[d>>0]|0;a[d>>0]=e+255|e;e=c[b>>2]|0;if(!(e&8)){c[b+8>>2]=0;c[b+4>>2]=0;d=c[b+44>>2]|0;c[b+28>>2]=d;c[b+20>>2]=d;c[b+16>>2]=d+(c[b+48>>2]|0);f=0}else{c[b>>2]=e|32;f=-1}return f|0}function ge(a){a=a|0;var b=0,d=0,e=0,f=0;b=(c[a>>2]&1|0)!=0;if(!b){Pa(3411128);d=c[a+52>>2]|0;e=a+56|0;if(d)c[d+56>>2]=c[e>>2];f=c[e>>2]|0;if(f)c[f+52>>2]=d;if((c[852781]|0)==(a|0))c[852781]=f;Ja(3411128)}f=he(a)|0;d=bb[c[a+12>>2]&3](a)|0|f;f=c[a+92>>2]|0;if(f)Fe(f);if(!b)Fe(a);return d|0}function he(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0;do if(a){if((c[a+76>>2]|0)<=-1){b=ye(a)|0;break}d=(Zd(a)|0)==0;e=ye(a)|0;if(d)b=e;else{_d(a);b=e}}else{if(!(c[221]|0))f=0;else f=he(c[221]|0)|0;Pa(3411128);e=c[852781]|0;if(!e)g=f;else{d=e;e=f;while(1){if((c[d+76>>2]|0)>-1)h=Zd(d)|0;else h=0;if((c[d+20>>2]|0)>>>0>(c[d+28>>2]|0)>>>0)i=ye(d)|0|e;else i=e;if(h)_d(d);d=c[d+56>>2]|0;if(!d){g=i;break}else e=i}}Ja(3411128);b=g}while(0);return b|0}function ie(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,j=0;e=i;i=i+32|0;f=e+16|0;g=e;if(qe(5648,a[d>>0]|0,4)|0){h=Yd(d)|0|32768;c[g>>2]=b;c[g+4>>2]=h;c[g+8>>2]=438;h=Sd(Ra(5,g|0)|0)|0;if((h|0)>=0){g=Xd(h,d)|0;if(!g){c[f>>2]=h;Qa(6,f|0)|0;j=0}else j=g}else j=0}else{c[(Qd()|0)>>2]=22;j=0}i=e;return j|0}function je(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0;e=i;i=i+16|0;f=e;c[f>>2]=d;d=oe(a,b,f)|0;i=e;return d|0}function ke(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;if((c[d+76>>2]|0)>=0?(Zd(d)|0)!=0:0){if((a[d+75>>0]|0)!=(b|0)?(e=d+20|0,f=c[e>>2]|0,f>>>0<(c[d+16>>2]|0)>>>0):0){c[e>>2]=f+1;a[f>>0]=b;g=b&255}else g=$d(d,b)|0;_d(d);h=g}else i=3;do if((i|0)==3){if((a[d+75>>0]|0)!=(b|0)?(g=d+20|0,f=c[g>>2]|0,f>>>0<(c[d+16>>2]|0)>>>0):0){c[g>>2]=f+1;a[f>>0]=b;h=b&255;break}h=$d(d,b)|0}while(0);return h|0}function le(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;f=e+16|0;g=c[f>>2]|0;if(!g)if(!(fe(e)|0)){h=c[f>>2]|0;i=5}else j=0;else{h=g;i=5}a:do if((i|0)==5){g=e+20|0;f=c[g>>2]|0;k=f;if((h-f|0)>>>0<d>>>0){j=Za[c[e+36>>2]&7](e,b,d)|0;break}b:do if((a[e+75>>0]|0)>-1){f=d;while(1){if(!f){l=d;m=b;n=k;o=0;break b}p=f+-1|0;if((a[b+p>>0]|0)==10){q=f;break}else f=p}if((Za[c[e+36>>2]&7](e,b,q)|0)>>>0<q>>>0){j=q;break a}l=d-q|0;m=b+q|0;n=c[g>>2]|0;o=q}else{l=d;m=b;n=k;o=0}while(0);Ue(n|0,m|0,l|0)|0;c[g>>2]=(c[g>>2]|0)+l;j=o+l|0}while(0);return j|0}function me(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0;f=$(d,b)|0;if((c[e+76>>2]|0)>-1){g=(Zd(e)|0)==0;h=le(a,f,e)|0;if(g)i=h;else{_d(e);i=h}}else i=le(a,f,e)|0;if((i|0)==(f|0))j=d;else j=(i>>>0)/(b>>>0)|0;return j|0}function ne(a,b){a=a|0;b=b|0;var d=0,e=0;d=i;i=i+16|0;e=d;c[e>>2]=b;b=oe(c[220]|0,a,e)|0;i=d;return b|0}function oe(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;f=i;i=i+224|0;g=f+120|0;h=f+80|0;j=f;k=f+136|0;l=h;m=l+40|0;do{c[l>>2]=0;l=l+4|0}while((l|0)<(m|0));c[g>>2]=c[e>>2];if((ze(0,d,g,j,h)|0)<0)n=-1;else{if((c[b+76>>2]|0)>-1)o=Zd(b)|0;else o=0;e=c[b>>2]|0;l=e&32;if((a[b+74>>0]|0)<1)c[b>>2]=e&-33;e=b+48|0;if(!(c[e>>2]|0)){m=b+44|0;p=c[m>>2]|0;c[m>>2]=k;q=b+28|0;c[q>>2]=k;r=b+20|0;c[r>>2]=k;c[e>>2]=80;s=b+16|0;c[s>>2]=k+80;k=ze(b,d,g,j,h)|0;if(!p)t=k;else{Za[c[b+36>>2]&7](b,0,0)|0;u=(c[r>>2]|0)==0?-1:k;c[m>>2]=p;c[e>>2]=0;c[s>>2]=0;c[q>>2]=0;c[r>>2]=0;t=u}}else t=ze(b,d,g,j,h)|0;h=c[b>>2]|0;c[b>>2]=h|l;if(o)_d(b);n=(h&32|0)==0?t:-1}i=f;return n|0}function pe(b,d,e,f){b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;g=i;i=i+128|0;h=g+112|0;j=g;k=j;l=888;m=k+112|0;do{c[k>>2]=c[l>>2];k=k+4|0;l=l+4|0}while((k|0)<(m|0));if((d+-1|0)>>>0>2147483646)if(!d){n=h;o=1;p=4}else{c[(Qd()|0)>>2]=75;q=-1}else{n=b;o=d;p=4}if((p|0)==4){p=-2-n|0;d=o>>>0>p>>>0?p:o;c[j+48>>2]=d;o=j+20|0;c[o>>2]=n;c[j+44>>2]=n;p=n+d|0;n=j+16|0;c[n>>2]=p;c[j+28>>2]=p;p=oe(j,e,f)|0;if(!d)q=p;else{d=c[o>>2]|0;a[d+(((d|0)==(c[n>>2]|0))<<31>>31)>>0]=0;q=p}}i=g;return q|0}function qe(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0;f=d&255;g=(e|0)!=0;a:do if(g&(b&3|0)!=0){h=d&255;i=e;j=b;while(1){if((a[j>>0]|0)==h<<24>>24){k=i;l=j;m=6;break a}n=j+1|0;o=i+-1|0;p=(o|0)!=0;if(p&(n&3|0)!=0){i=o;j=n}else{q=o;r=p;s=n;m=5;break}}}else{q=e;r=g;s=b;m=5}while(0);if((m|0)==5)if(r){k=q;l=s;m=6}else{t=0;u=s}b:do if((m|0)==6){s=d&255;if((a[l>>0]|0)==s<<24>>24){t=k;u=l}else{q=$(f,16843009)|0;c:do if(k>>>0>3){r=k;b=l;while(1){g=c[b>>2]^q;if((g&-2139062144^-2139062144)&g+-16843009){v=r;w=b;break}g=b+4|0;e=r+-4|0;if(e>>>0>3){r=e;b=g}else{x=e;y=g;m=11;break c}}z=v;A=w}else{x=k;y=l;m=11}while(0);if((m|0)==11)if(!x){t=0;u=y;break}else{z=x;A=y}while(1){if((a[A>>0]|0)==s<<24>>24){t=z;u=A;break b}q=A+1|0;z=z+-1|0;if(!z){t=0;u=q;break}else A=q}}}while(0);return ((t|0)!=0?u:0)|0}function re(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;e=d;a:do if(!((e^b)&3)){if(!(e&3)){f=d;g=b}else{h=b;i=d;while(1){j=a[i>>0]|0;a[h>>0]=j;if(!(j<<24>>24)){k=h;break a}j=i+1|0;l=h+1|0;if(!(j&3)){f=j;g=l;break}else{h=l;i=j}}}i=c[f>>2]|0;if(!((i&-2139062144^-2139062144)&i+-16843009)){h=i;i=g;j=f;while(1){l=j+4|0;m=i+4|0;c[i>>2]=h;h=c[l>>2]|0;if((h&-2139062144^-2139062144)&h+-16843009){n=m;o=l;break}else{i=m;j=l}}}else{n=g;o=f}p=o;q=n;r=8}else{p=d;q=b;r=8}while(0);if((r|0)==8){r=a[p>>0]|0;a[q>>0]=r;if(!(r<<24>>24))k=q;else{r=q;q=p;while(1){q=q+1|0;p=r+1|0;b=a[q>>0]|0;a[p>>0]=b;if(!(b<<24>>24)){k=p;break}else r=p}}}return k|0}function se(b,c){b=b|0;c=c|0;var d=0;d=te(b,c)|0;return ((a[d>>0]|0)==(c&255)<<24>>24?d:0)|0}function te(b,d){b=b|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;e=d&255;a:do if(!e)f=b+(ve(b)|0)|0;else{if(!(b&3))g=b;else{h=d&255;i=b;while(1){j=a[i>>0]|0;if(j<<24>>24==0?1:j<<24>>24==h<<24>>24){f=i;break a}j=i+1|0;if(!(j&3)){g=j;break}else i=j}}i=$(e,16843009)|0;h=c[g>>2]|0;b:do if(!((h&-2139062144^-2139062144)&h+-16843009)){j=h;k=g;while(1){l=j^i;if((l&-2139062144^-2139062144)&l+-16843009){m=k;break b}l=k+4|0;j=c[l>>2]|0;if((j&-2139062144^-2139062144)&j+-16843009){m=l;break}else k=l}}else m=g;while(0);i=d&255;h=m;while(1){k=a[h>>0]|0;if(k<<24>>24==0?1:k<<24>>24==i<<24>>24){f=h;break}else h=h+1|0}}while(0);return f|0}function ue(a,b){a=a|0;b=b|0;re(a,b)|0;return a|0}function ve(b){b=b|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;d=b;a:do if(!(d&3)){e=b;f=4}else{g=b;h=d;while(1){if(!(a[g>>0]|0)){i=h;break a}j=g+1|0;h=j;if(!(h&3)){e=j;f=4;break}else g=j}}while(0);if((f|0)==4){f=e;while(1){e=c[f>>2]|0;if(!((e&-2139062144^-2139062144)&e+-16843009))f=f+4|0;else{k=e;l=f;break}}if(!((k&255)<<24>>24))m=l;else{k=l;while(1){l=k+1|0;if(!(a[l>>0]|0)){m=l;break}else k=l}}i=m}return i-d|0}function we(a){a=a|0;if(!(c[a+68>>2]|0))_d(a);return}function xe(a){a=a|0;if(!(c[a+68>>2]|0))_d(a);return}function ye(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0;b=a+20|0;d=a+28|0;if((c[b>>2]|0)>>>0>(c[d>>2]|0)>>>0?(Za[c[a+36>>2]&7](a,0,0)|0,(c[b>>2]|0)==0):0)e=-1;else{f=a+4|0;g=c[f>>2]|0;h=a+8|0;i=c[h>>2]|0;if(g>>>0<i>>>0)Za[c[a+40>>2]&7](a,g-i|0,1)|0;c[a+16>>2]=0;c[d>>2]=0;c[b>>2]=0;c[h>>2]=0;c[f>>2]=0;e=0}return e|0}function ze(e,f,g,j,l){e=e|0;f=f|0;g=g|0;j=j|0;l=l|0;var m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0,Qa=0,Ra=0,Sa=0,Ta=0,Ua=0,Va=0,Wa=0,Xa=0,Ya=0,Za=0,_a=0,$a=0,ab=0,bb=0,cb=0.0,db=0.0,eb=0,fb=0,gb=0,hb=0,ib=0,jb=0.0,kb=0.0,lb=0.0,mb=0.0,nb=0,ob=0,pb=0,qb=0,rb=0,sb=0,tb=0,ub=0,vb=0.0,wb=0,xb=0,yb=0,zb=0,Ab=0,Bb=0,Cb=0,Db=0,Eb=0,Fb=0,Gb=0,Hb=0,Ib=0,Jb=0,Kb=0,Lb=0,Mb=0,Nb=0,Ob=0,Pb=0,Qb=0,Rb=0,Sb=0,Tb=0,Ub=0,Vb=0,Wb=0,Xb=0.0,Yb=0.0,Zb=0.0,_b=0,$b=0,ac=0,bc=0,cc=0,dc=0,ec=0,fc=0,gc=0,hc=0,ic=0,jc=0,kc=0,lc=0,mc=0,nc=0,oc=0,pc=0,qc=0,rc=0,sc=0,tc=0,uc=0,vc=0,wc=0,xc=0,yc=0,zc=0,Ac=0,Bc=0,Cc=0,Dc=0,Ec=0;m=i;i=i+624|0;n=m+24|0;o=m+16|0;p=m+588|0;q=m+576|0;r=m;s=m+536|0;t=m+8|0;u=m+528|0;v=(e|0)!=0;w=s+40|0;x=w;y=s+39|0;s=t+4|0;z=p;A=0-z|0;B=q+12|0;C=q+11|0;q=B;E=q-z|0;F=-2-z|0;G=q+2|0;H=n+288|0;I=p+9|0;J=I;K=p+8|0;L=0;M=0;N=0;O=f;a:while(1){do if((L|0)>-1)if((M|0)>(2147483647-L|0)){c[(Qd()|0)>>2]=75;P=-1;break}else{P=M+L|0;break}else P=L;while(0);f=a[O>>0]|0;if(!(f<<24>>24)){Q=P;R=N;S=244;break}else{T=f;U=O}b:while(1){switch(T<<24>>24){case 37:{V=U;W=U;S=9;break b;break}case 0:{X=U;Y=U;break b;break}default:{}}f=U+1|0;T=a[f>>0]|0;U=f}c:do if((S|0)==9)while(1){S=0;if((a[V+1>>0]|0)!=37){X=V;Y=W;break c}f=W+1|0;Z=V+2|0;if((a[Z>>0]|0)==37){V=Z;W=f;S=9}else{X=Z;Y=f;break}}while(0);f=Y-O|0;if(v?(c[e>>2]&32|0)==0:0)le(O,f,e)|0;if((Y|0)!=(O|0)){L=P;M=f;O=X;continue}Z=X+1|0;_=a[Z>>0]|0;aa=(_<<24>>24)+-48|0;if(aa>>>0<10){ba=(a[X+2>>0]|0)==36;ca=ba?X+3|0:Z;da=a[ca>>0]|0;ea=ba?aa:-1;fa=ba?1:N;ga=ca}else{da=_;ea=-1;fa=N;ga=Z}Z=da<<24>>24;d:do if((Z&-32|0)==32){_=Z;ca=da;ba=0;aa=ga;while(1){if(!(1<<_+-32&75913)){ha=ca;ia=ba;ja=aa;break d}ka=1<<(ca<<24>>24)+-32|ba;la=aa+1|0;ma=a[la>>0]|0;_=ma<<24>>24;if((_&-32|0)!=32){ha=ma;ia=ka;ja=la;break}else{ca=ma;ba=ka;aa=la}}}else{ha=da;ia=0;ja=ga}while(0);do if(ha<<24>>24==42){Z=ja+1|0;aa=(a[Z>>0]|0)+-48|0;if(aa>>>0<10?(a[ja+2>>0]|0)==36:0){c[l+(aa<<2)>>2]=10;na=1;oa=ja+3|0;pa=c[j+((a[Z>>0]|0)+-48<<3)>>2]|0}else{if(fa){qa=-1;break a}if(!v){ra=ia;sa=0;ta=Z;ua=0;break}aa=(c[g>>2]|0)+(4-1)&~(4-1);ba=c[aa>>2]|0;c[g>>2]=aa+4;na=0;oa=Z;pa=ba}if((pa|0)<0){ra=ia|8192;sa=na;ta=oa;ua=0-pa|0}else{ra=ia;sa=na;ta=oa;ua=pa}}else{ba=(ha<<24>>24)+-48|0;if(ba>>>0<10){Z=ja;aa=0;ca=ba;while(1){ba=(aa*10|0)+ca|0;_=Z+1|0;ca=(a[_>>0]|0)+-48|0;if(ca>>>0>=10){va=ba;wa=_;break}else{Z=_;aa=ba}}if((va|0)<0){qa=-1;break a}else{ra=ia;sa=fa;ta=wa;ua=va}}else{ra=ia;sa=fa;ta=ja;ua=0}}while(0);e:do if((a[ta>>0]|0)==46){aa=ta+1|0;Z=a[aa>>0]|0;if(Z<<24>>24!=42){ca=(Z<<24>>24)+-48|0;if(ca>>>0<10){xa=aa;ya=0;za=ca}else{Aa=0;Ba=aa;break}while(1){aa=(ya*10|0)+za|0;ca=xa+1|0;za=(a[ca>>0]|0)+-48|0;if(za>>>0>=10){Aa=aa;Ba=ca;break e}else{xa=ca;ya=aa}}}aa=ta+2|0;ca=(a[aa>>0]|0)+-48|0;if(ca>>>0<10?(a[ta+3>>0]|0)==36:0){c[l+(ca<<2)>>2]=10;Aa=c[j+((a[aa>>0]|0)+-48<<3)>>2]|0;Ba=ta+4|0;break}if(sa){qa=-1;break a}if(v){ca=(c[g>>2]|0)+(4-1)&~(4-1);Z=c[ca>>2]|0;c[g>>2]=ca+4;Aa=Z;Ba=aa}else{Aa=0;Ba=aa}}else{Aa=-1;Ba=ta}while(0);aa=Ba;Z=0;while(1){ca=(a[aa>>0]|0)+-65|0;if(ca>>>0>57){qa=-1;break a}ba=aa+1|0;_=a[5652+(Z*58|0)+ca>>0]|0;ca=_&255;if((ca+-1|0)>>>0<8){aa=ba;Z=ca}else{Ca=ba;Da=_;Ea=ca;Fa=aa;Ga=Z;break}}if(!(Da<<24>>24)){qa=-1;break}Z=(ea|0)>-1;do if(Da<<24>>24==19)if(Z){qa=-1;break a}else S=52;else{if(Z){c[l+(ea<<2)>>2]=Ea;aa=j+(ea<<3)|0;ca=c[aa+4>>2]|0;_=r;c[_>>2]=c[aa>>2];c[_+4>>2]=ca;S=52;break}if(!v){qa=0;break a}Be(r,Ea,g)}while(0);if((S|0)==52?(S=0,!v):0){L=P;M=f;N=sa;O=Ca;continue}Z=a[Fa>>0]|0;ca=(Ga|0)!=0&(Z&15|0)==3?Z&-33:Z;Z=ra&-65537;_=(ra&8192|0)==0?ra:Z;f:do switch(ca|0){case 110:{switch(Ga|0){case 0:{c[c[r>>2]>>2]=P;L=P;M=f;N=sa;O=Ca;continue a;break}case 1:{c[c[r>>2]>>2]=P;L=P;M=f;N=sa;O=Ca;continue a;break}case 2:{aa=c[r>>2]|0;c[aa>>2]=P;c[aa+4>>2]=((P|0)<0)<<31>>31;L=P;M=f;N=sa;O=Ca;continue a;break}case 3:{b[c[r>>2]>>1]=P;L=P;M=f;N=sa;O=Ca;continue a;break}case 4:{a[c[r>>2]>>0]=P;L=P;M=f;N=sa;O=Ca;continue a;break}case 6:{c[c[r>>2]>>2]=P;L=P;M=f;N=sa;O=Ca;continue a;break}case 7:{aa=c[r>>2]|0;c[aa>>2]=P;c[aa+4>>2]=((P|0)<0)<<31>>31;L=P;M=f;N=sa;O=Ca;continue a;break}default:{L=P;M=f;N=sa;O=Ca;continue a}}break}case 112:{Ha=_|8;Ia=Aa>>>0>8?Aa:8;Ja=120;S=64;break}case 88:case 120:{Ha=_;Ia=Aa;Ja=ca;S=64;break}case 111:{aa=r;ba=c[aa>>2]|0;la=c[aa+4>>2]|0;if((ba|0)==0&(la|0)==0)Ka=w;else{aa=w;ka=ba;ba=la;while(1){la=aa+-1|0;a[la>>0]=ka&7|48;ka=Te(ka|0,ba|0,3)|0;ba=D;if((ka|0)==0&(ba|0)==0){Ka=la;break}else aa=la}}if(!(_&8)){La=Ka;Ma=_;Na=Aa;Oa=0;Pa=6132;S=77}else{aa=x-Ka|0;La=Ka;Ma=_;Na=(Aa|0)>(aa|0)?Aa:aa+1|0;Oa=0;Pa=6132;S=77}break}case 105:case 100:{aa=r;ba=c[aa>>2]|0;ka=c[aa+4>>2]|0;if((ka|0)<0){aa=Pe(0,0,ba|0,ka|0)|0;la=D;ma=r;c[ma>>2]=aa;c[ma+4>>2]=la;Qa=aa;Ra=la;Sa=1;Ta=6132;S=76;break f}if(!(_&2048)){la=_&1;Qa=ba;Ra=ka;Sa=la;Ta=(la|0)==0?6132:6134;S=76}else{Qa=ba;Ra=ka;Sa=1;Ta=6133;S=76}break}case 117:{ka=r;Qa=c[ka>>2]|0;Ra=c[ka+4>>2]|0;Sa=0;Ta=6132;S=76;break}case 99:{a[y>>0]=c[r>>2];Ua=y;Va=Z;Wa=1;Xa=0;Ya=6132;Za=w;break}case 109:{_a=Rd(c[(Qd()|0)>>2]|0)|0;S=82;break}case 115:{ka=c[r>>2]|0;_a=(ka|0)!=0?ka:6142;S=82;break}case 67:{c[t>>2]=c[r>>2];c[s>>2]=0;c[r>>2]=t;$a=t;ab=-1;S=86;break}case 83:{ka=c[r>>2]|0;if(!Aa){De(e,32,ua,0,_);bb=0;S=97}else{$a=ka;ab=Aa;S=86}break}case 65:case 71:case 70:case 69:case 97:case 103:case 102:case 101:{cb=+h[r>>3];c[o>>2]=0;h[k>>3]=cb;if((c[k+4>>2]|0)>=0)if(!(_&2048)){ka=_&1;db=cb;eb=ka;fb=(ka|0)==0?6150:6155}else{db=cb;eb=1;fb=6152}else{db=-cb;eb=1;fb=6149}h[k>>3]=db;ka=c[k+4>>2]&2146435072;do if(ka>>>0<2146435072|(ka|0)==2146435072&0<0){cb=+Ud(db,o)*2.0;ba=cb!=0.0;if(ba)c[o>>2]=(c[o>>2]|0)+-1;la=ca|32;if((la|0)==97){aa=ca&32;ma=(aa|0)==0?fb:fb+9|0;gb=eb|2;hb=12-Aa|0;do if(!(Aa>>>0>11|(hb|0)==0)){ib=hb;jb=8.0;while(1){ib=ib+-1|0;kb=jb*16.0;if(!ib){lb=kb;break}else jb=kb}if((a[ma>>0]|0)==45){mb=-(lb+(-cb-lb));break}else{mb=cb+lb-lb;break}}else mb=cb;while(0);hb=c[o>>2]|0;ib=(hb|0)<0?0-hb|0:hb;nb=Ce(ib,((ib|0)<0)<<31>>31,B)|0;if((nb|0)==(B|0)){a[C>>0]=48;ob=C}else ob=nb;a[ob+-1>>0]=(hb>>31&2)+43;hb=ob+-2|0;a[hb>>0]=ca+15;nb=(Aa|0)<1;ib=(_&8|0)==0;jb=mb;pb=p;while(1){qb=~~jb;rb=pb+1|0;a[pb>>0]=d[6116+qb>>0]|aa;jb=(jb-+(qb|0))*16.0;do if((rb-z|0)==1){if(ib&(nb&jb==0.0)){sb=rb;break}a[rb>>0]=46;sb=pb+2|0}else sb=rb;while(0);if(!(jb!=0.0)){tb=sb;break}else pb=sb}pb=tb;nb=hb;ib=(Aa|0)!=0&(F+pb|0)<(Aa|0)?G+Aa-nb|0:E-nb+pb|0;aa=ib+gb|0;De(e,32,ua,aa,_);if(!(c[e>>2]&32))le(ma,gb,e)|0;De(e,48,ua,aa,_^65536);rb=pb-z|0;if(!(c[e>>2]&32))le(p,rb,e)|0;pb=q-nb|0;De(e,48,ib-(rb+pb)|0,0,0);if(!(c[e>>2]&32))le(hb,pb,e)|0;De(e,32,ua,aa,_^8192);ub=(aa|0)<(ua|0)?ua:aa;break}aa=(Aa|0)<0?6:Aa;if(ba){pb=(c[o>>2]|0)+-28|0;c[o>>2]=pb;vb=cb*268435456.0;wb=pb}else{vb=cb;wb=c[o>>2]|0}pb=(wb|0)<0?n:H;rb=pb;jb=vb;ib=pb;while(1){nb=~~jb>>>0;c[ib>>2]=nb;qb=ib+4|0;jb=(jb-+(nb>>>0))*1.0e9;if(!(jb!=0.0)){xb=qb;break}else ib=qb}ib=c[o>>2]|0;if((ib|0)>0){ba=ib;hb=pb;gb=xb;while(1){ma=(ba|0)>29?29:ba;qb=gb+-4|0;do if(qb>>>0<hb>>>0)yb=hb;else{nb=0;zb=qb;while(1){Ab=Re(c[zb>>2]|0,0,ma|0)|0;Bb=Se(Ab|0,D|0,nb|0,0)|0;Ab=D;Cb=af(Bb|0,Ab|0,1e9,0)|0;c[zb>>2]=Cb;Cb=$e(Bb|0,Ab|0,1e9,0)|0;zb=zb+-4|0;if(zb>>>0<hb>>>0){Db=Cb;break}else nb=Cb}if(!Db){yb=hb;break}nb=hb+-4|0;c[nb>>2]=Db;yb=nb}while(0);qb=gb;while(1){if(qb>>>0<=yb>>>0){Eb=qb;break}nb=qb+-4|0;if(!(c[nb>>2]|0))qb=nb;else{Eb=qb;break}}qb=(c[o>>2]|0)-ma|0;c[o>>2]=qb;if((qb|0)>0){ba=qb;hb=yb;gb=Eb}else{Fb=qb;Gb=yb;Hb=Eb;break}}}else{Fb=ib;Gb=pb;Hb=xb}if((Fb|0)<0){gb=((aa+25|0)/9|0)+1|0;hb=(la|0)==102;ba=Fb;qb=Gb;nb=Hb;while(1){zb=0-ba|0;Cb=(zb|0)>9?9:zb;do if(qb>>>0<nb>>>0){zb=(1<<Cb)+-1|0;Ab=1e9>>>Cb;Bb=0;Ib=qb;while(1){Jb=c[Ib>>2]|0;c[Ib>>2]=(Jb>>>Cb)+Bb;Kb=$(Jb&zb,Ab)|0;Ib=Ib+4|0;if(Ib>>>0>=nb>>>0){Lb=Kb;break}else Bb=Kb}Bb=(c[qb>>2]|0)==0?qb+4|0:qb;if(!Lb){Mb=Bb;Nb=nb;break}c[nb>>2]=Lb;Mb=Bb;Nb=nb+4|0}else{Mb=(c[qb>>2]|0)==0?qb+4|0:qb;Nb=nb}while(0);ma=hb?pb:Mb;Bb=(Nb-ma>>2|0)>(gb|0)?ma+(gb<<2)|0:Nb;ba=(c[o>>2]|0)+Cb|0;c[o>>2]=ba;if((ba|0)>=0){Ob=Mb;Pb=Bb;break}else{qb=Mb;nb=Bb}}}else{Ob=Gb;Pb=Hb}do if(Ob>>>0<Pb>>>0){nb=(rb-Ob>>2)*9|0;qb=c[Ob>>2]|0;if(qb>>>0<10){Qb=nb;break}else{Rb=nb;Sb=10}while(1){Sb=Sb*10|0;nb=Rb+1|0;if(qb>>>0<Sb>>>0){Qb=nb;break}else Rb=nb}}else Qb=0;while(0);qb=(la|0)==103;Cb=(aa|0)!=0;nb=aa-((la|0)!=102?Qb:0)+((Cb&qb)<<31>>31)|0;if((nb|0)<(((Pb-rb>>2)*9|0)+-9|0)){ba=nb+9216|0;nb=pb+4+(((ba|0)/9|0)+-1024<<2)|0;gb=((ba|0)%9|0)+1|0;if((gb|0)<9){ba=10;hb=gb;while(1){gb=ba*10|0;hb=hb+1|0;if((hb|0)==9){Tb=gb;break}else ba=gb}}else Tb=10;ba=c[nb>>2]|0;hb=(ba>>>0)%(Tb>>>0)|0;la=(nb+4|0)==(Pb|0);do if(la&(hb|0)==0){Ub=Ob;Vb=nb;Wb=Qb}else{jb=(((ba>>>0)/(Tb>>>0)|0)&1|0)==0?9007199254740992.0:9007199254740994.0;gb=(Tb|0)/2|0;if(hb>>>0<gb>>>0)Xb=.5;else Xb=la&(hb|0)==(gb|0)?1.0:1.5;do if(!eb){Yb=jb;Zb=Xb}else{if((a[fb>>0]|0)!=45){Yb=jb;Zb=Xb;break}Yb=-jb;Zb=-Xb}while(0);gb=ba-hb|0;c[nb>>2]=gb;if(!(Yb+Zb!=Yb)){Ub=Ob;Vb=nb;Wb=Qb;break}ib=gb+Tb|0;c[nb>>2]=ib;if(ib>>>0>999999999){ib=Ob;gb=nb;while(1){Bb=gb+-4|0;c[gb>>2]=0;if(Bb>>>0<ib>>>0){ma=ib+-4|0;c[ma>>2]=0;_b=ma}else _b=ib;ma=(c[Bb>>2]|0)+1|0;c[Bb>>2]=ma;if(ma>>>0>999999999){ib=_b;gb=Bb}else{$b=_b;ac=Bb;break}}}else{$b=Ob;ac=nb}gb=(rb-$b>>2)*9|0;ib=c[$b>>2]|0;if(ib>>>0<10){Ub=$b;Vb=ac;Wb=gb;break}else{bc=gb;cc=10}while(1){cc=cc*10|0;gb=bc+1|0;if(ib>>>0<cc>>>0){Ub=$b;Vb=ac;Wb=gb;break}else bc=gb}}while(0);nb=Vb+4|0;dc=Ub;ec=Wb;fc=Pb>>>0>nb>>>0?nb:Pb}else{dc=Ob;ec=Qb;fc=Pb}nb=0-ec|0;hb=fc;while(1){if(hb>>>0<=dc>>>0){gc=0;hc=hb;break}ba=hb+-4|0;if(!(c[ba>>2]|0))hb=ba;else{gc=1;hc=hb;break}}do if(qb){hb=(Cb&1^1)+aa|0;if((hb|0)>(ec|0)&(ec|0)>-5){ic=ca+-1|0;jc=hb+-1-ec|0}else{ic=ca+-2|0;jc=hb+-1|0}hb=_&8;if(hb){kc=ic;lc=jc;mc=hb;break}do if(gc){hb=c[hc+-4>>2]|0;if(!hb){nc=9;break}if(!((hb>>>0)%10|0)){oc=10;pc=0}else{nc=0;break}while(1){oc=oc*10|0;ba=pc+1|0;if((hb>>>0)%(oc>>>0)|0){nc=ba;break}else pc=ba}}else nc=9;while(0);hb=((hc-rb>>2)*9|0)+-9|0;if((ic|32|0)==102){ba=hb-nc|0;la=(ba|0)<0?0:ba;kc=ic;lc=(jc|0)<(la|0)?jc:la;mc=0;break}else{la=hb+ec-nc|0;hb=(la|0)<0?0:la;kc=ic;lc=(jc|0)<(hb|0)?jc:hb;mc=0;break}}else{kc=ca;lc=aa;mc=_&8}while(0);aa=lc|mc;rb=(aa|0)!=0&1;Cb=(kc|32|0)==102;if(Cb){qc=(ec|0)>0?ec:0;rc=0}else{qb=(ec|0)<0?nb:ec;hb=Ce(qb,((qb|0)<0)<<31>>31,B)|0;if((q-hb|0)<2){qb=hb;while(1){la=qb+-1|0;a[la>>0]=48;if((q-la|0)<2)qb=la;else{sc=la;break}}}else sc=hb;a[sc+-1>>0]=(ec>>31&2)+43;qb=sc+-2|0;a[qb>>0]=kc;qc=q-qb|0;rc=qb}qb=eb+1+lc+rb+qc|0;De(e,32,ua,qb,_);if(!(c[e>>2]&32))le(fb,eb,e)|0;De(e,48,ua,qb,_^65536);do if(Cb){nb=dc>>>0>pb>>>0?pb:dc;la=nb;while(1){ba=Ce(c[la>>2]|0,0,I)|0;do if((la|0)==(nb|0)){if((ba|0)!=(I|0)){tc=ba;break}a[K>>0]=48;tc=K}else{if(ba>>>0<=p>>>0){tc=ba;break}Qe(p|0,48,ba-z|0)|0;ib=ba;while(1){gb=ib+-1|0;if(gb>>>0>p>>>0)ib=gb;else{tc=gb;break}}}while(0);if(!(c[e>>2]&32))le(tc,J-tc|0,e)|0;ba=la+4|0;if(ba>>>0>pb>>>0){uc=ba;break}else la=ba}do if(aa){if(c[e>>2]&32)break;le(6184,1,e)|0}while(0);if((lc|0)>0&uc>>>0<hc>>>0){la=lc;nb=uc;while(1){ba=Ce(c[nb>>2]|0,0,I)|0;if(ba>>>0>p>>>0){Qe(p|0,48,ba-z|0)|0;ib=ba;while(1){gb=ib+-1|0;if(gb>>>0>p>>>0)ib=gb;else{vc=gb;break}}}else vc=ba;if(!(c[e>>2]&32))le(vc,(la|0)>9?9:la,e)|0;nb=nb+4|0;ib=la+-9|0;if(!((la|0)>9&nb>>>0<hc>>>0)){wc=ib;break}else la=ib}}else wc=lc;De(e,48,wc+9|0,9,0)}else{la=gc?hc:dc+4|0;if((lc|0)>-1){nb=(mc|0)==0;ib=lc;gb=dc;while(1){Bb=Ce(c[gb>>2]|0,0,I)|0;if((Bb|0)==(I|0)){a[K>>0]=48;xc=K}else xc=Bb;do if((gb|0)==(dc|0)){Bb=xc+1|0;if(!(c[e>>2]&32))le(xc,1,e)|0;if(nb&(ib|0)<1){yc=Bb;break}if(c[e>>2]&32){yc=Bb;break}le(6184,1,e)|0;yc=Bb}else{if(xc>>>0<=p>>>0){yc=xc;break}Qe(p|0,48,xc+A|0)|0;Bb=xc;while(1){ma=Bb+-1|0;if(ma>>>0>p>>>0)Bb=ma;else{yc=ma;break}}}while(0);ba=J-yc|0;if(!(c[e>>2]&32))le(yc,(ib|0)>(ba|0)?ba:ib,e)|0;Bb=ib-ba|0;gb=gb+4|0;if(!(gb>>>0<la>>>0&(Bb|0)>-1)){zc=Bb;break}else ib=Bb}}else zc=lc;De(e,48,zc+18|0,18,0);if(c[e>>2]&32)break;le(rc,q-rc|0,e)|0}while(0);De(e,32,ua,qb,_^8192);ub=(qb|0)<(ua|0)?ua:qb}else{aa=(ca&32|0)!=0;pb=db!=db|0.0!=0.0;Cb=pb?0:eb;rb=Cb+3|0;De(e,32,ua,rb,Z);hb=c[e>>2]|0;if(!(hb&32)){le(fb,Cb,e)|0;Ac=c[e>>2]|0}else Ac=hb;if(!(Ac&32))le(pb?(aa?6176:6180):aa?6168:6172,3,e)|0;De(e,32,ua,rb,_^8192);ub=(rb|0)<(ua|0)?ua:rb}while(0);L=P;M=ub;N=sa;O=Ca;continue a;break}default:{Ua=O;Va=_;Wa=Aa;Xa=0;Ya=6132;Za=w}}while(0);g:do if((S|0)==64){S=0;ca=r;f=c[ca>>2]|0;ka=c[ca+4>>2]|0;ca=Ja&32;if(!((f|0)==0&(ka|0)==0)){rb=w;aa=f;f=ka;while(1){ka=rb+-1|0;a[ka>>0]=d[6116+(aa&15)>>0]|ca;aa=Te(aa|0,f|0,4)|0;f=D;if((aa|0)==0&(f|0)==0){Bc=ka;break}else rb=ka}rb=r;if((Ha&8|0)==0|(c[rb>>2]|0)==0&(c[rb+4>>2]|0)==0){La=Bc;Ma=Ha;Na=Ia;Oa=0;Pa=6132;S=77}else{La=Bc;Ma=Ha;Na=Ia;Oa=2;Pa=6132+(Ja>>4)|0;S=77}}else{La=w;Ma=Ha;Na=Ia;Oa=0;Pa=6132;S=77}}else if((S|0)==76){S=0;La=Ce(Qa,Ra,w)|0;Ma=_;Na=Aa;Oa=Sa;Pa=Ta;S=77}else if((S|0)==82){S=0;rb=qe(_a,0,Aa)|0;f=(rb|0)==0;Ua=_a;Va=Z;Wa=f?Aa:rb-_a|0;Xa=0;Ya=6132;Za=f?_a+Aa|0:rb}else if((S|0)==86){S=0;rb=0;f=0;aa=$a;while(1){ca=c[aa>>2]|0;if(!ca){Cc=rb;Dc=f;break}ka=Wd(u,ca)|0;if((ka|0)<0|ka>>>0>(ab-rb|0)>>>0){Cc=rb;Dc=ka;break}ca=ka+rb|0;if(ab>>>0>ca>>>0){rb=ca;f=ka;aa=aa+4|0}else{Cc=ca;Dc=ka;break}}if((Dc|0)<0){qa=-1;break a}De(e,32,ua,Cc,_);if(!Cc){bb=0;S=97}else{aa=0;f=$a;while(1){rb=c[f>>2]|0;if(!rb){bb=Cc;S=97;break g}ka=Wd(u,rb)|0;aa=ka+aa|0;if((aa|0)>(Cc|0)){bb=Cc;S=97;break g}if(!(c[e>>2]&32))le(u,ka,e)|0;if(aa>>>0>=Cc>>>0){bb=Cc;S=97;break}else f=f+4|0}}}while(0);if((S|0)==97){S=0;De(e,32,ua,bb,_^8192);L=P;M=(ua|0)>(bb|0)?ua:bb;N=sa;O=Ca;continue}if((S|0)==77){S=0;Z=(Na|0)>-1?Ma&-65537:Ma;f=r;aa=(c[f>>2]|0)!=0|(c[f+4>>2]|0)!=0;if((Na|0)!=0|aa){f=(aa&1^1)+(x-La)|0;Ua=La;Va=Z;Wa=(Na|0)>(f|0)?Na:f;Xa=Oa;Ya=Pa;Za=w}else{Ua=w;Va=Z;Wa=0;Xa=Oa;Ya=Pa;Za=w}}Z=Za-Ua|0;f=(Wa|0)<(Z|0)?Z:Wa;aa=Xa+f|0;ka=(ua|0)<(aa|0)?aa:ua;De(e,32,ka,aa,Va);if(!(c[e>>2]&32))le(Ya,Xa,e)|0;De(e,48,ka,aa,Va^65536);De(e,48,f,Z,0);if(!(c[e>>2]&32))le(Ua,Z,e)|0;De(e,32,ka,aa,Va^8192);L=P;M=ka;N=sa;O=Ca}h:do if((S|0)==244)if(!e)if(R){Ca=1;while(1){O=c[l+(Ca<<2)>>2]|0;if(!O){Ec=Ca;break}Be(j+(Ca<<3)|0,O,g);Ca=Ca+1|0;if((Ca|0)>=10){qa=1;break h}}if((Ec|0)<10){Ca=Ec;while(1){if(c[l+(Ca<<2)>>2]|0){qa=-1;break h}Ca=Ca+1|0;if((Ca|0)>=10){qa=1;break}}}else qa=1}else qa=0;else qa=Q;while(0);i=m;return qa|0}function Ae(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0;e=a+20|0;f=c[e>>2]|0;g=(c[a+16>>2]|0)-f|0;a=g>>>0>d>>>0?d:g;Ue(f|0,b|0,a|0)|0;c[e>>2]=(c[e>>2]|0)+a;return d|0}function Be(a,b,d){a=a|0;b=b|0;d=d|0;var e=0,f=0,g=0,i=0,j=0.0;a:do if(b>>>0<=20)do switch(b|0){case 9:{e=(c[d>>2]|0)+(4-1)&~(4-1);f=c[e>>2]|0;c[d>>2]=e+4;c[a>>2]=f;break a;break}case 10:{f=(c[d>>2]|0)+(4-1)&~(4-1);e=c[f>>2]|0;c[d>>2]=f+4;f=a;c[f>>2]=e;c[f+4>>2]=((e|0)<0)<<31>>31;break a;break}case 11:{e=(c[d>>2]|0)+(4-1)&~(4-1);f=c[e>>2]|0;c[d>>2]=e+4;e=a;c[e>>2]=f;c[e+4>>2]=0;break a;break}case 12:{e=(c[d>>2]|0)+(8-1)&~(8-1);f=e;g=c[f>>2]|0;i=c[f+4>>2]|0;c[d>>2]=e+8;e=a;c[e>>2]=g;c[e+4>>2]=i;break a;break}case 13:{i=(c[d>>2]|0)+(4-1)&~(4-1);e=c[i>>2]|0;c[d>>2]=i+4;i=(e&65535)<<16>>16;e=a;c[e>>2]=i;c[e+4>>2]=((i|0)<0)<<31>>31;break a;break}case 14:{i=(c[d>>2]|0)+(4-1)&~(4-1);e=c[i>>2]|0;c[d>>2]=i+4;i=a;c[i>>2]=e&65535;c[i+4>>2]=0;break a;break}case 15:{i=(c[d>>2]|0)+(4-1)&~(4-1);e=c[i>>2]|0;c[d>>2]=i+4;i=(e&255)<<24>>24;e=a;c[e>>2]=i;c[e+4>>2]=((i|0)<0)<<31>>31;break a;break}case 16:{i=(c[d>>2]|0)+(4-1)&~(4-1);e=c[i>>2]|0;c[d>>2]=i+4;i=a;c[i>>2]=e&255;c[i+4>>2]=0;break a;break}case 17:{i=(c[d>>2]|0)+(8-1)&~(8-1);j=+h[i>>3];c[d>>2]=i+8;h[a>>3]=j;break a;break}case 18:{i=(c[d>>2]|0)+(8-1)&~(8-1);j=+h[i>>3];c[d>>2]=i+8;h[a>>3]=j;break a;break}default:break a}while(0);while(0);return}function Ce(b,c,d){b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;if(c>>>0>0|(c|0)==0&b>>>0>4294967295){e=d;f=b;g=c;while(1){c=af(f|0,g|0,10,0)|0;h=e+-1|0;a[h>>0]=c|48;c=$e(f|0,g|0,10,0)|0;if(g>>>0>9|(g|0)==9&f>>>0>4294967295){e=h;f=c;g=D}else{i=h;j=c;break}}k=i;l=j}else{k=d;l=b}if(!l)m=k;else{b=k;k=l;while(1){l=b+-1|0;a[l>>0]=(k>>>0)%10|0|48;if(k>>>0<10){m=l;break}else{b=l;k=(k>>>0)/10|0}}}return m|0}function De(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;g=i;i=i+256|0;h=g;do if((d|0)>(e|0)&(f&73728|0)==0){j=d-e|0;Qe(h|0,b|0,(j>>>0>256?256:j)|0)|0;k=c[a>>2]|0;l=(k&32|0)==0;if(j>>>0>255){m=d-e|0;n=j;o=k;k=l;while(1){if(k){le(h,256,a)|0;p=c[a>>2]|0}else p=o;n=n+-256|0;k=(p&32|0)==0;if(n>>>0<=255)break;else o=p}if(k)q=m&255;else break}else if(l)q=j;else break;le(h,q,a)|0}while(0);i=g;return}
function Ee(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,Aa=0,Ba=0,Ca=0,Da=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0;do if(a>>>0<245){b=a>>>0<11?16:a+11&-8;d=b>>>3;e=c[852787]|0;f=e>>>d;if(f&3){g=(f&1^1)+d|0;h=3411188+(g<<1<<2)|0;i=h+8|0;j=c[i>>2]|0;k=j+8|0;l=c[k>>2]|0;do if((h|0)!=(l|0)){if(l>>>0<(c[852791]|0)>>>0)sa();m=l+12|0;if((c[m>>2]|0)==(j|0)){c[m>>2]=h;c[i>>2]=l;break}else sa()}else c[852787]=e&~(1<<g);while(0);l=g<<3;c[j+4>>2]=l|3;i=j+l+4|0;c[i>>2]=c[i>>2]|1;n=k;return n|0}i=c[852789]|0;if(b>>>0>i>>>0){if(f){l=2<<d;h=f<<d&(l|0-l);l=(h&0-h)+-1|0;h=l>>>12&16;m=l>>>h;l=m>>>5&8;o=m>>>l;m=o>>>2&4;p=o>>>m;o=p>>>1&2;q=p>>>o;p=q>>>1&1;r=(l|h|m|o|p)+(q>>>p)|0;p=3411188+(r<<1<<2)|0;q=p+8|0;o=c[q>>2]|0;m=o+8|0;h=c[m>>2]|0;do if((p|0)!=(h|0)){if(h>>>0<(c[852791]|0)>>>0)sa();l=h+12|0;if((c[l>>2]|0)==(o|0)){c[l>>2]=p;c[q>>2]=h;s=c[852789]|0;break}else sa()}else{c[852787]=e&~(1<<r);s=i}while(0);i=(r<<3)-b|0;c[o+4>>2]=b|3;e=o+b|0;c[e+4>>2]=i|1;c[e+i>>2]=i;if(s){h=c[852792]|0;q=s>>>3;p=3411188+(q<<1<<2)|0;d=c[852787]|0;f=1<<q;if(d&f){q=p+8|0;k=c[q>>2]|0;if(k>>>0<(c[852791]|0)>>>0)sa();else{t=q;u=k}}else{c[852787]=d|f;t=p+8|0;u=p}c[t>>2]=h;c[u+12>>2]=h;c[h+8>>2]=u;c[h+12>>2]=p}c[852789]=i;c[852792]=e;n=m;return n|0}e=c[852788]|0;if(e){i=(e&0-e)+-1|0;e=i>>>12&16;p=i>>>e;i=p>>>5&8;h=p>>>i;p=h>>>2&4;f=h>>>p;h=f>>>1&2;d=f>>>h;f=d>>>1&1;k=c[3411452+((i|e|p|h|f)+(d>>>f)<<2)>>2]|0;f=(c[k+4>>2]&-8)-b|0;d=k;h=k;while(1){k=c[d+16>>2]|0;if(!k){p=c[d+20>>2]|0;if(!p){v=f;w=h;break}else x=p}else x=k;k=(c[x+4>>2]&-8)-b|0;p=k>>>0<f>>>0;f=p?k:f;d=x;h=p?x:h}h=c[852791]|0;if(w>>>0<h>>>0)sa();d=w+b|0;if(w>>>0>=d>>>0)sa();f=c[w+24>>2]|0;m=c[w+12>>2]|0;do if((m|0)==(w|0)){o=w+20|0;r=c[o>>2]|0;if(!r){p=w+16|0;k=c[p>>2]|0;if(!k){y=0;break}else{z=k;A=p}}else{z=r;A=o}while(1){o=z+20|0;r=c[o>>2]|0;if(r){z=r;A=o;continue}o=z+16|0;r=c[o>>2]|0;if(!r){B=z;C=A;break}else{z=r;A=o}}if(C>>>0<h>>>0)sa();else{c[C>>2]=0;y=B;break}}else{o=c[w+8>>2]|0;if(o>>>0<h>>>0)sa();r=o+12|0;if((c[r>>2]|0)!=(w|0))sa();p=m+8|0;if((c[p>>2]|0)==(w|0)){c[r>>2]=m;c[p>>2]=o;y=m;break}else sa()}while(0);do if(f){m=c[w+28>>2]|0;h=3411452+(m<<2)|0;if((w|0)==(c[h>>2]|0)){c[h>>2]=y;if(!y){c[852788]=c[852788]&~(1<<m);break}}else{if(f>>>0<(c[852791]|0)>>>0)sa();m=f+16|0;if((c[m>>2]|0)==(w|0))c[m>>2]=y;else c[f+20>>2]=y;if(!y)break}m=c[852791]|0;if(y>>>0<m>>>0)sa();c[y+24>>2]=f;h=c[w+16>>2]|0;do if(h)if(h>>>0<m>>>0)sa();else{c[y+16>>2]=h;c[h+24>>2]=y;break}while(0);h=c[w+20>>2]|0;if(h)if(h>>>0<(c[852791]|0)>>>0)sa();else{c[y+20>>2]=h;c[h+24>>2]=y;break}}while(0);if(v>>>0<16){f=v+b|0;c[w+4>>2]=f|3;h=w+f+4|0;c[h>>2]=c[h>>2]|1}else{c[w+4>>2]=b|3;c[d+4>>2]=v|1;c[d+v>>2]=v;h=c[852789]|0;if(h){f=c[852792]|0;m=h>>>3;h=3411188+(m<<1<<2)|0;o=c[852787]|0;p=1<<m;if(o&p){m=h+8|0;r=c[m>>2]|0;if(r>>>0<(c[852791]|0)>>>0)sa();else{D=m;E=r}}else{c[852787]=o|p;D=h+8|0;E=h}c[D>>2]=f;c[E+12>>2]=f;c[f+8>>2]=E;c[f+12>>2]=h}c[852789]=v;c[852792]=d}n=w+8|0;return n|0}else F=b}else F=b}else if(a>>>0<=4294967231){h=a+11|0;f=h&-8;p=c[852788]|0;if(p){o=0-f|0;r=h>>>8;if(r)if(f>>>0>16777215)G=31;else{h=(r+1048320|0)>>>16&8;m=r<<h;r=(m+520192|0)>>>16&4;k=m<<r;m=(k+245760|0)>>>16&2;e=14-(r|h|m)+(k<<m>>>15)|0;G=f>>>(e+7|0)&1|e<<1}else G=0;e=c[3411452+(G<<2)>>2]|0;a:do if(!e){H=o;I=0;J=0;K=86}else{m=o;k=0;h=f<<((G|0)==31?0:25-(G>>>1)|0);r=e;i=0;while(1){q=c[r+4>>2]&-8;j=q-f|0;if(j>>>0<m>>>0)if((q|0)==(f|0)){L=j;M=r;N=r;K=90;break a}else{O=j;P=r}else{O=m;P=i}j=c[r+20>>2]|0;r=c[r+16+(h>>>31<<2)>>2]|0;q=(j|0)==0|(j|0)==(r|0)?k:j;j=(r|0)==0;if(j){H=O;I=q;J=P;K=86;break}else{m=O;k=q;h=h<<(j&1^1);i=P}}}while(0);if((K|0)==86){if((I|0)==0&(J|0)==0){e=2<<G;o=p&(e|0-e);if(!o){F=f;break}e=(o&0-o)+-1|0;o=e>>>12&16;b=e>>>o;e=b>>>5&8;d=b>>>e;b=d>>>2&4;i=d>>>b;d=i>>>1&2;h=i>>>d;i=h>>>1&1;Q=c[3411452+((e|o|b|d|i)+(h>>>i)<<2)>>2]|0}else Q=I;if(!Q){R=H;S=J}else{L=H;M=Q;N=J;K=90}}if((K|0)==90)while(1){K=0;i=(c[M+4>>2]&-8)-f|0;h=i>>>0<L>>>0;d=h?i:L;i=h?M:N;h=c[M+16>>2]|0;if(h){L=d;M=h;N=i;K=90;continue}M=c[M+20>>2]|0;if(!M){R=d;S=i;break}else{L=d;N=i;K=90}}if((S|0)!=0?R>>>0<((c[852789]|0)-f|0)>>>0:0){p=c[852791]|0;if(S>>>0<p>>>0)sa();i=S+f|0;if(S>>>0>=i>>>0)sa();d=c[S+24>>2]|0;h=c[S+12>>2]|0;do if((h|0)==(S|0)){b=S+20|0;o=c[b>>2]|0;if(!o){e=S+16|0;k=c[e>>2]|0;if(!k){T=0;break}else{U=k;V=e}}else{U=o;V=b}while(1){b=U+20|0;o=c[b>>2]|0;if(o){U=o;V=b;continue}b=U+16|0;o=c[b>>2]|0;if(!o){W=U;X=V;break}else{U=o;V=b}}if(X>>>0<p>>>0)sa();else{c[X>>2]=0;T=W;break}}else{b=c[S+8>>2]|0;if(b>>>0<p>>>0)sa();o=b+12|0;if((c[o>>2]|0)!=(S|0))sa();e=h+8|0;if((c[e>>2]|0)==(S|0)){c[o>>2]=h;c[e>>2]=b;T=h;break}else sa()}while(0);do if(d){h=c[S+28>>2]|0;p=3411452+(h<<2)|0;if((S|0)==(c[p>>2]|0)){c[p>>2]=T;if(!T){c[852788]=c[852788]&~(1<<h);break}}else{if(d>>>0<(c[852791]|0)>>>0)sa();h=d+16|0;if((c[h>>2]|0)==(S|0))c[h>>2]=T;else c[d+20>>2]=T;if(!T)break}h=c[852791]|0;if(T>>>0<h>>>0)sa();c[T+24>>2]=d;p=c[S+16>>2]|0;do if(p)if(p>>>0<h>>>0)sa();else{c[T+16>>2]=p;c[p+24>>2]=T;break}while(0);p=c[S+20>>2]|0;if(p)if(p>>>0<(c[852791]|0)>>>0)sa();else{c[T+20>>2]=p;c[p+24>>2]=T;break}}while(0);do if(R>>>0>=16){c[S+4>>2]=f|3;c[i+4>>2]=R|1;c[i+R>>2]=R;d=R>>>3;if(R>>>0<256){p=3411188+(d<<1<<2)|0;h=c[852787]|0;b=1<<d;if(h&b){d=p+8|0;e=c[d>>2]|0;if(e>>>0<(c[852791]|0)>>>0)sa();else{Y=d;Z=e}}else{c[852787]=h|b;Y=p+8|0;Z=p}c[Y>>2]=i;c[Z+12>>2]=i;c[i+8>>2]=Z;c[i+12>>2]=p;break}p=R>>>8;if(p)if(R>>>0>16777215)_=31;else{b=(p+1048320|0)>>>16&8;h=p<<b;p=(h+520192|0)>>>16&4;e=h<<p;h=(e+245760|0)>>>16&2;d=14-(p|b|h)+(e<<h>>>15)|0;_=R>>>(d+7|0)&1|d<<1}else _=0;d=3411452+(_<<2)|0;c[i+28>>2]=_;h=i+16|0;c[h+4>>2]=0;c[h>>2]=0;h=c[852788]|0;e=1<<_;if(!(h&e)){c[852788]=h|e;c[d>>2]=i;c[i+24>>2]=d;c[i+12>>2]=i;c[i+8>>2]=i;break}e=R<<((_|0)==31?0:25-(_>>>1)|0);h=c[d>>2]|0;while(1){if((c[h+4>>2]&-8|0)==(R|0)){$=h;K=148;break}d=h+16+(e>>>31<<2)|0;b=c[d>>2]|0;if(!b){aa=d;ba=h;K=145;break}else{e=e<<1;h=b}}if((K|0)==145)if(aa>>>0<(c[852791]|0)>>>0)sa();else{c[aa>>2]=i;c[i+24>>2]=ba;c[i+12>>2]=i;c[i+8>>2]=i;break}else if((K|0)==148){h=$+8|0;e=c[h>>2]|0;b=c[852791]|0;if(e>>>0>=b>>>0&$>>>0>=b>>>0){c[e+12>>2]=i;c[h>>2]=i;c[i+8>>2]=e;c[i+12>>2]=$;c[i+24>>2]=0;break}else sa()}}else{e=R+f|0;c[S+4>>2]=e|3;h=S+e+4|0;c[h>>2]=c[h>>2]|1}while(0);n=S+8|0;return n|0}else F=f}else F=f}else F=-1;while(0);S=c[852789]|0;if(S>>>0>=F>>>0){R=S-F|0;$=c[852792]|0;if(R>>>0>15){ba=$+F|0;c[852792]=ba;c[852789]=R;c[ba+4>>2]=R|1;c[ba+R>>2]=R;c[$+4>>2]=F|3}else{c[852789]=0;c[852792]=0;c[$+4>>2]=S|3;R=$+S+4|0;c[R>>2]=c[R>>2]|1}n=$+8|0;return n|0}$=c[852790]|0;if($>>>0>F>>>0){R=$-F|0;c[852790]=R;$=c[852793]|0;S=$+F|0;c[852793]=S;c[S+4>>2]=R|1;c[$+4>>2]=F|3;n=$+8|0;return n|0}do if(!(c[852905]|0)){$=Ea(30)|0;if(!($+-1&$)){c[852907]=$;c[852906]=$;c[852908]=-1;c[852909]=-1;c[852910]=0;c[852898]=0;c[852905]=(Sa(0)|0)&-16^1431655768;break}else sa()}while(0);$=F+48|0;R=c[852907]|0;S=F+47|0;ba=R+S|0;aa=0-R|0;R=ba&aa;if(R>>>0<=F>>>0){n=0;return n|0}_=c[852897]|0;if((_|0)!=0?(Z=c[852895]|0,Y=Z+R|0,Y>>>0<=Z>>>0|Y>>>0>_>>>0):0){n=0;return n|0}b:do if(!(c[852898]&4)){_=c[852793]|0;c:do if(_){Y=3411596;while(1){Z=c[Y>>2]|0;if(Z>>>0<=_>>>0?(T=Y+4|0,(Z+(c[T>>2]|0)|0)>>>0>_>>>0):0){ca=Y;da=T;break}Y=c[Y+8>>2]|0;if(!Y){K=173;break c}}Y=ba-(c[852790]|0)&aa;if(Y>>>0<2147483647){T=za(Y|0)|0;if((T|0)==((c[ca>>2]|0)+(c[da>>2]|0)|0)){if((T|0)!=(-1|0)){ea=T;fa=Y;K=193;break b}}else{ga=T;ha=Y;K=183}}}else K=173;while(0);do if((K|0)==173?(_=za(0)|0,(_|0)!=(-1|0)):0){f=_;Y=c[852906]|0;T=Y+-1|0;if(!(T&f))ia=R;else ia=R-f+(T+f&0-Y)|0;Y=c[852895]|0;f=Y+ia|0;if(ia>>>0>F>>>0&ia>>>0<2147483647){T=c[852897]|0;if((T|0)!=0?f>>>0<=Y>>>0|f>>>0>T>>>0:0)break;T=za(ia|0)|0;if((T|0)==(_|0)){ea=_;fa=ia;K=193;break b}else{ga=T;ha=ia;K=183}}}while(0);d:do if((K|0)==183){T=0-ha|0;do if($>>>0>ha>>>0&(ha>>>0<2147483647&(ga|0)!=(-1|0))?(_=c[852907]|0,f=S-ha+_&0-_,f>>>0<2147483647):0)if((za(f|0)|0)==(-1|0)){za(T|0)|0;break d}else{ja=f+ha|0;break}else ja=ha;while(0);if((ga|0)!=(-1|0)){ea=ga;fa=ja;K=193;break b}}while(0);c[852898]=c[852898]|4;K=190}else K=190;while(0);if((((K|0)==190?R>>>0<2147483647:0)?(ja=za(R|0)|0,R=za(0)|0,ja>>>0<R>>>0&((ja|0)!=(-1|0)&(R|0)!=(-1|0))):0)?(ga=R-ja|0,ga>>>0>(F+40|0)>>>0):0){ea=ja;fa=ga;K=193}if((K|0)==193){ga=(c[852895]|0)+fa|0;c[852895]=ga;if(ga>>>0>(c[852896]|0)>>>0)c[852896]=ga;ga=c[852793]|0;do if(ga){ja=3411596;do{R=c[ja>>2]|0;ha=ja+4|0;S=c[ha>>2]|0;if((ea|0)==(R+S|0)){ka=R;la=ha;ma=S;na=ja;K=203;break}ja=c[ja+8>>2]|0}while((ja|0)!=0);if(((K|0)==203?(c[na+12>>2]&8|0)==0:0)?ga>>>0<ea>>>0&ga>>>0>=ka>>>0:0){c[la>>2]=ma+fa;ja=ga+8|0;S=(ja&7|0)==0?0:0-ja&7;ja=ga+S|0;ha=fa-S+(c[852790]|0)|0;c[852793]=ja;c[852790]=ha;c[ja+4>>2]=ha|1;c[ja+ha+4>>2]=40;c[852794]=c[852909];break}ha=c[852791]|0;if(ea>>>0<ha>>>0){c[852791]=ea;oa=ea}else oa=ha;ha=ea+fa|0;ja=3411596;while(1){if((c[ja>>2]|0)==(ha|0)){pa=ja;qa=ja;K=211;break}ja=c[ja+8>>2]|0;if(!ja){ra=3411596;break}}if((K|0)==211)if(!(c[qa+12>>2]&8)){c[pa>>2]=ea;ja=qa+4|0;c[ja>>2]=(c[ja>>2]|0)+fa;ja=ea+8|0;S=ea+((ja&7|0)==0?0:0-ja&7)|0;ja=ha+8|0;R=ha+((ja&7|0)==0?0:0-ja&7)|0;ja=S+F|0;$=R-S-F|0;c[S+4>>2]=F|3;do if((R|0)!=(ga|0)){if((R|0)==(c[852792]|0)){ia=(c[852789]|0)+$|0;c[852789]=ia;c[852792]=ja;c[ja+4>>2]=ia|1;c[ja+ia>>2]=ia;break}ia=c[R+4>>2]|0;if((ia&3|0)==1){da=ia&-8;ca=ia>>>3;e:do if(ia>>>0>=256){aa=c[R+24>>2]|0;ba=c[R+12>>2]|0;do if((ba|0)==(R|0)){T=R+16|0;f=T+4|0;_=c[f>>2]|0;if(!_){Y=c[T>>2]|0;if(!Y){ta=0;break}else{ua=Y;va=T}}else{ua=_;va=f}while(1){f=ua+20|0;_=c[f>>2]|0;if(_){ua=_;va=f;continue}f=ua+16|0;_=c[f>>2]|0;if(!_){wa=ua;xa=va;break}else{ua=_;va=f}}if(xa>>>0<oa>>>0)sa();else{c[xa>>2]=0;ta=wa;break}}else{f=c[R+8>>2]|0;if(f>>>0<oa>>>0)sa();_=f+12|0;if((c[_>>2]|0)!=(R|0))sa();T=ba+8|0;if((c[T>>2]|0)==(R|0)){c[_>>2]=ba;c[T>>2]=f;ta=ba;break}else sa()}while(0);if(!aa)break;ba=c[R+28>>2]|0;f=3411452+(ba<<2)|0;do if((R|0)!=(c[f>>2]|0)){if(aa>>>0<(c[852791]|0)>>>0)sa();T=aa+16|0;if((c[T>>2]|0)==(R|0))c[T>>2]=ta;else c[aa+20>>2]=ta;if(!ta)break e}else{c[f>>2]=ta;if(ta)break;c[852788]=c[852788]&~(1<<ba);break e}while(0);ba=c[852791]|0;if(ta>>>0<ba>>>0)sa();c[ta+24>>2]=aa;f=R+16|0;T=c[f>>2]|0;do if(T)if(T>>>0<ba>>>0)sa();else{c[ta+16>>2]=T;c[T+24>>2]=ta;break}while(0);T=c[f+4>>2]|0;if(!T)break;if(T>>>0<(c[852791]|0)>>>0)sa();else{c[ta+20>>2]=T;c[T+24>>2]=ta;break}}else{T=c[R+8>>2]|0;ba=c[R+12>>2]|0;aa=3411188+(ca<<1<<2)|0;do if((T|0)!=(aa|0)){if(T>>>0<oa>>>0)sa();if((c[T+12>>2]|0)==(R|0))break;sa()}while(0);if((ba|0)==(T|0)){c[852787]=c[852787]&~(1<<ca);break}do if((ba|0)==(aa|0))ya=ba+8|0;else{if(ba>>>0<oa>>>0)sa();f=ba+8|0;if((c[f>>2]|0)==(R|0)){ya=f;break}sa()}while(0);c[T+12>>2]=ba;c[ya>>2]=T}while(0);Aa=R+da|0;Ba=da+$|0}else{Aa=R;Ba=$}ca=Aa+4|0;c[ca>>2]=c[ca>>2]&-2;c[ja+4>>2]=Ba|1;c[ja+Ba>>2]=Ba;ca=Ba>>>3;if(Ba>>>0<256){ia=3411188+(ca<<1<<2)|0;aa=c[852787]|0;f=1<<ca;do if(!(aa&f)){c[852787]=aa|f;Ca=ia+8|0;Da=ia}else{ca=ia+8|0;_=c[ca>>2]|0;if(_>>>0>=(c[852791]|0)>>>0){Ca=ca;Da=_;break}sa()}while(0);c[Ca>>2]=ja;c[Da+12>>2]=ja;c[ja+8>>2]=Da;c[ja+12>>2]=ia;break}f=Ba>>>8;do if(!f)Fa=0;else{if(Ba>>>0>16777215){Fa=31;break}aa=(f+1048320|0)>>>16&8;da=f<<aa;_=(da+520192|0)>>>16&4;ca=da<<_;da=(ca+245760|0)>>>16&2;Y=14-(_|aa|da)+(ca<<da>>>15)|0;Fa=Ba>>>(Y+7|0)&1|Y<<1}while(0);f=3411452+(Fa<<2)|0;c[ja+28>>2]=Fa;ia=ja+16|0;c[ia+4>>2]=0;c[ia>>2]=0;ia=c[852788]|0;Y=1<<Fa;if(!(ia&Y)){c[852788]=ia|Y;c[f>>2]=ja;c[ja+24>>2]=f;c[ja+12>>2]=ja;c[ja+8>>2]=ja;break}Y=Ba<<((Fa|0)==31?0:25-(Fa>>>1)|0);ia=c[f>>2]|0;while(1){if((c[ia+4>>2]&-8|0)==(Ba|0)){Ga=ia;K=281;break}f=ia+16+(Y>>>31<<2)|0;da=c[f>>2]|0;if(!da){Ha=f;Ia=ia;K=278;break}else{Y=Y<<1;ia=da}}if((K|0)==278)if(Ha>>>0<(c[852791]|0)>>>0)sa();else{c[Ha>>2]=ja;c[ja+24>>2]=Ia;c[ja+12>>2]=ja;c[ja+8>>2]=ja;break}else if((K|0)==281){ia=Ga+8|0;Y=c[ia>>2]|0;da=c[852791]|0;if(Y>>>0>=da>>>0&Ga>>>0>=da>>>0){c[Y+12>>2]=ja;c[ia>>2]=ja;c[ja+8>>2]=Y;c[ja+12>>2]=Ga;c[ja+24>>2]=0;break}else sa()}}else{Y=(c[852790]|0)+$|0;c[852790]=Y;c[852793]=ja;c[ja+4>>2]=Y|1}while(0);n=S+8|0;return n|0}else ra=3411596;while(1){ja=c[ra>>2]|0;if(ja>>>0<=ga>>>0?($=ja+(c[ra+4>>2]|0)|0,$>>>0>ga>>>0):0){Ja=$;break}ra=c[ra+8>>2]|0}S=Ja+-47|0;$=S+8|0;ja=S+(($&7|0)==0?0:0-$&7)|0;$=ga+16|0;S=ja>>>0<$>>>0?ga:ja;ja=S+8|0;R=ea+8|0;ha=(R&7|0)==0?0:0-R&7;R=ea+ha|0;Y=fa+-40-ha|0;c[852793]=R;c[852790]=Y;c[R+4>>2]=Y|1;c[R+Y+4>>2]=40;c[852794]=c[852909];Y=S+4|0;c[Y>>2]=27;c[ja>>2]=c[852899];c[ja+4>>2]=c[852900];c[ja+8>>2]=c[852901];c[ja+12>>2]=c[852902];c[852899]=ea;c[852900]=fa;c[852902]=0;c[852901]=ja;ja=S+24|0;do{ja=ja+4|0;c[ja>>2]=7}while((ja+4|0)>>>0<Ja>>>0);if((S|0)!=(ga|0)){ja=S-ga|0;c[Y>>2]=c[Y>>2]&-2;c[ga+4>>2]=ja|1;c[S>>2]=ja;R=ja>>>3;if(ja>>>0<256){ha=3411188+(R<<1<<2)|0;ia=c[852787]|0;da=1<<R;if(ia&da){R=ha+8|0;f=c[R>>2]|0;if(f>>>0<(c[852791]|0)>>>0)sa();else{Ka=R;La=f}}else{c[852787]=ia|da;Ka=ha+8|0;La=ha}c[Ka>>2]=ga;c[La+12>>2]=ga;c[ga+8>>2]=La;c[ga+12>>2]=ha;break}ha=ja>>>8;if(ha)if(ja>>>0>16777215)Ma=31;else{da=(ha+1048320|0)>>>16&8;ia=ha<<da;ha=(ia+520192|0)>>>16&4;f=ia<<ha;ia=(f+245760|0)>>>16&2;R=14-(ha|da|ia)+(f<<ia>>>15)|0;Ma=ja>>>(R+7|0)&1|R<<1}else Ma=0;R=3411452+(Ma<<2)|0;c[ga+28>>2]=Ma;c[ga+20>>2]=0;c[$>>2]=0;ia=c[852788]|0;f=1<<Ma;if(!(ia&f)){c[852788]=ia|f;c[R>>2]=ga;c[ga+24>>2]=R;c[ga+12>>2]=ga;c[ga+8>>2]=ga;break}f=ja<<((Ma|0)==31?0:25-(Ma>>>1)|0);ia=c[R>>2]|0;while(1){if((c[ia+4>>2]&-8|0)==(ja|0)){Na=ia;K=307;break}R=ia+16+(f>>>31<<2)|0;da=c[R>>2]|0;if(!da){Oa=R;Pa=ia;K=304;break}else{f=f<<1;ia=da}}if((K|0)==304)if(Oa>>>0<(c[852791]|0)>>>0)sa();else{c[Oa>>2]=ga;c[ga+24>>2]=Pa;c[ga+12>>2]=ga;c[ga+8>>2]=ga;break}else if((K|0)==307){ia=Na+8|0;f=c[ia>>2]|0;ja=c[852791]|0;if(f>>>0>=ja>>>0&Na>>>0>=ja>>>0){c[f+12>>2]=ga;c[ia>>2]=ga;c[ga+8>>2]=f;c[ga+12>>2]=Na;c[ga+24>>2]=0;break}else sa()}}}else{f=c[852791]|0;if((f|0)==0|ea>>>0<f>>>0)c[852791]=ea;c[852899]=ea;c[852900]=fa;c[852902]=0;c[852796]=c[852905];c[852795]=-1;f=0;do{ia=3411188+(f<<1<<2)|0;c[ia+12>>2]=ia;c[ia+8>>2]=ia;f=f+1|0}while((f|0)!=32);f=ea+8|0;ia=(f&7|0)==0?0:0-f&7;f=ea+ia|0;ja=fa+-40-ia|0;c[852793]=f;c[852790]=ja;c[f+4>>2]=ja|1;c[f+ja+4>>2]=40;c[852794]=c[852909]}while(0);fa=c[852790]|0;if(fa>>>0>F>>>0){ea=fa-F|0;c[852790]=ea;fa=c[852793]|0;ga=fa+F|0;c[852793]=ga;c[ga+4>>2]=ea|1;c[fa+4>>2]=F|3;n=fa+8|0;return n|0}}c[(Qd()|0)>>2]=12;n=0;return n|0}function Fe(a){a=a|0;var b=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0;if(!a)return;b=a+-8|0;d=c[852791]|0;if(b>>>0<d>>>0)sa();e=c[a+-4>>2]|0;a=e&3;if((a|0)==1)sa();f=e&-8;g=b+f|0;do if(!(e&1)){h=c[b>>2]|0;if(!a)return;i=b+(0-h)|0;j=h+f|0;if(i>>>0<d>>>0)sa();if((i|0)==(c[852792]|0)){k=g+4|0;l=c[k>>2]|0;if((l&3|0)!=3){m=i;n=j;break}c[852789]=j;c[k>>2]=l&-2;c[i+4>>2]=j|1;c[i+j>>2]=j;return}l=h>>>3;if(h>>>0<256){h=c[i+8>>2]|0;k=c[i+12>>2]|0;o=3411188+(l<<1<<2)|0;if((h|0)!=(o|0)){if(h>>>0<d>>>0)sa();if((c[h+12>>2]|0)!=(i|0))sa()}if((k|0)==(h|0)){c[852787]=c[852787]&~(1<<l);m=i;n=j;break}if((k|0)!=(o|0)){if(k>>>0<d>>>0)sa();o=k+8|0;if((c[o>>2]|0)==(i|0))p=o;else sa()}else p=k+8|0;c[h+12>>2]=k;c[p>>2]=h;m=i;n=j;break}h=c[i+24>>2]|0;k=c[i+12>>2]|0;do if((k|0)==(i|0)){o=i+16|0;l=o+4|0;q=c[l>>2]|0;if(!q){r=c[o>>2]|0;if(!r){s=0;break}else{t=r;u=o}}else{t=q;u=l}while(1){l=t+20|0;q=c[l>>2]|0;if(q){t=q;u=l;continue}l=t+16|0;q=c[l>>2]|0;if(!q){v=t;w=u;break}else{t=q;u=l}}if(w>>>0<d>>>0)sa();else{c[w>>2]=0;s=v;break}}else{l=c[i+8>>2]|0;if(l>>>0<d>>>0)sa();q=l+12|0;if((c[q>>2]|0)!=(i|0))sa();o=k+8|0;if((c[o>>2]|0)==(i|0)){c[q>>2]=k;c[o>>2]=l;s=k;break}else sa()}while(0);if(h){k=c[i+28>>2]|0;l=3411452+(k<<2)|0;if((i|0)==(c[l>>2]|0)){c[l>>2]=s;if(!s){c[852788]=c[852788]&~(1<<k);m=i;n=j;break}}else{if(h>>>0<(c[852791]|0)>>>0)sa();k=h+16|0;if((c[k>>2]|0)==(i|0))c[k>>2]=s;else c[h+20>>2]=s;if(!s){m=i;n=j;break}}k=c[852791]|0;if(s>>>0<k>>>0)sa();c[s+24>>2]=h;l=i+16|0;o=c[l>>2]|0;do if(o)if(o>>>0<k>>>0)sa();else{c[s+16>>2]=o;c[o+24>>2]=s;break}while(0);o=c[l+4>>2]|0;if(o)if(o>>>0<(c[852791]|0)>>>0)sa();else{c[s+20>>2]=o;c[o+24>>2]=s;m=i;n=j;break}else{m=i;n=j}}else{m=i;n=j}}else{m=b;n=f}while(0);if(m>>>0>=g>>>0)sa();f=g+4|0;b=c[f>>2]|0;if(!(b&1))sa();if(!(b&2)){if((g|0)==(c[852793]|0)){s=(c[852790]|0)+n|0;c[852790]=s;c[852793]=m;c[m+4>>2]=s|1;if((m|0)!=(c[852792]|0))return;c[852792]=0;c[852789]=0;return}if((g|0)==(c[852792]|0)){s=(c[852789]|0)+n|0;c[852789]=s;c[852792]=m;c[m+4>>2]=s|1;c[m+s>>2]=s;return}s=(b&-8)+n|0;d=b>>>3;do if(b>>>0>=256){v=c[g+24>>2]|0;w=c[g+12>>2]|0;do if((w|0)==(g|0)){u=g+16|0;t=u+4|0;p=c[t>>2]|0;if(!p){a=c[u>>2]|0;if(!a){x=0;break}else{y=a;z=u}}else{y=p;z=t}while(1){t=y+20|0;p=c[t>>2]|0;if(p){y=p;z=t;continue}t=y+16|0;p=c[t>>2]|0;if(!p){A=y;B=z;break}else{y=p;z=t}}if(B>>>0<(c[852791]|0)>>>0)sa();else{c[B>>2]=0;x=A;break}}else{t=c[g+8>>2]|0;if(t>>>0<(c[852791]|0)>>>0)sa();p=t+12|0;if((c[p>>2]|0)!=(g|0))sa();u=w+8|0;if((c[u>>2]|0)==(g|0)){c[p>>2]=w;c[u>>2]=t;x=w;break}else sa()}while(0);if(v){w=c[g+28>>2]|0;j=3411452+(w<<2)|0;if((g|0)==(c[j>>2]|0)){c[j>>2]=x;if(!x){c[852788]=c[852788]&~(1<<w);break}}else{if(v>>>0<(c[852791]|0)>>>0)sa();w=v+16|0;if((c[w>>2]|0)==(g|0))c[w>>2]=x;else c[v+20>>2]=x;if(!x)break}w=c[852791]|0;if(x>>>0<w>>>0)sa();c[x+24>>2]=v;j=g+16|0;i=c[j>>2]|0;do if(i)if(i>>>0<w>>>0)sa();else{c[x+16>>2]=i;c[i+24>>2]=x;break}while(0);i=c[j+4>>2]|0;if(i)if(i>>>0<(c[852791]|0)>>>0)sa();else{c[x+20>>2]=i;c[i+24>>2]=x;break}}}else{i=c[g+8>>2]|0;w=c[g+12>>2]|0;v=3411188+(d<<1<<2)|0;if((i|0)!=(v|0)){if(i>>>0<(c[852791]|0)>>>0)sa();if((c[i+12>>2]|0)!=(g|0))sa()}if((w|0)==(i|0)){c[852787]=c[852787]&~(1<<d);break}if((w|0)!=(v|0)){if(w>>>0<(c[852791]|0)>>>0)sa();v=w+8|0;if((c[v>>2]|0)==(g|0))C=v;else sa()}else C=w+8|0;c[i+12>>2]=w;c[C>>2]=i}while(0);c[m+4>>2]=s|1;c[m+s>>2]=s;if((m|0)==(c[852792]|0)){c[852789]=s;return}else D=s}else{c[f>>2]=b&-2;c[m+4>>2]=n|1;c[m+n>>2]=n;D=n}n=D>>>3;if(D>>>0<256){b=3411188+(n<<1<<2)|0;f=c[852787]|0;s=1<<n;if(f&s){n=b+8|0;C=c[n>>2]|0;if(C>>>0<(c[852791]|0)>>>0)sa();else{E=n;F=C}}else{c[852787]=f|s;E=b+8|0;F=b}c[E>>2]=m;c[F+12>>2]=m;c[m+8>>2]=F;c[m+12>>2]=b;return}b=D>>>8;if(b)if(D>>>0>16777215)G=31;else{F=(b+1048320|0)>>>16&8;E=b<<F;b=(E+520192|0)>>>16&4;s=E<<b;E=(s+245760|0)>>>16&2;f=14-(b|F|E)+(s<<E>>>15)|0;G=D>>>(f+7|0)&1|f<<1}else G=0;f=3411452+(G<<2)|0;c[m+28>>2]=G;c[m+20>>2]=0;c[m+16>>2]=0;E=c[852788]|0;s=1<<G;do if(E&s){F=D<<((G|0)==31?0:25-(G>>>1)|0);b=c[f>>2]|0;while(1){if((c[b+4>>2]&-8|0)==(D|0)){H=b;I=130;break}C=b+16+(F>>>31<<2)|0;n=c[C>>2]|0;if(!n){J=C;K=b;I=127;break}else{F=F<<1;b=n}}if((I|0)==127)if(J>>>0<(c[852791]|0)>>>0)sa();else{c[J>>2]=m;c[m+24>>2]=K;c[m+12>>2]=m;c[m+8>>2]=m;break}else if((I|0)==130){b=H+8|0;F=c[b>>2]|0;j=c[852791]|0;if(F>>>0>=j>>>0&H>>>0>=j>>>0){c[F+12>>2]=m;c[b>>2]=m;c[m+8>>2]=F;c[m+12>>2]=H;c[m+24>>2]=0;break}else sa()}}else{c[852788]=E|s;c[f>>2]=m;c[m+24>>2]=f;c[m+12>>2]=m;c[m+8>>2]=m}while(0);m=(c[852795]|0)+-1|0;c[852795]=m;if(!m)L=3411604;else return;while(1){m=c[L>>2]|0;if(!m)break;else L=m+8|0}c[852795]=-1;return}function Ge(a,b){a=a|0;b=b|0;var d=0,e=0;if(a){d=$(b,a)|0;if((b|a)>>>0>65535)e=((d>>>0)/(a>>>0)|0|0)==(b|0)?d:-1;else e=d}else e=0;d=Ee(e)|0;if(!d)return d|0;if(!(c[d+-4>>2]&3))return d|0;Qe(d|0,0,e|0)|0;return d|0}function He(b,d){b=b|0;d=d|0;if(!(a[d>>0]&1)){c[b>>2]=c[d>>2];c[b+4>>2]=c[d+4>>2];c[b+8>>2]=c[d+8>>2]}else Ie(b,c[d+8>>2]|0,c[d+4>>2]|0);return}function Ie(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0;if(e>>>0>4294967279)Nd(b);if(e>>>0<11){a[b>>0]=e<<1;f=b+1|0}else{g=e+16&-16;h=jd(g)|0;c[b+8>>2]=h;c[b>>2]=g|1;c[b+4>>2]=e;f=h}Ue(f|0,d|0,e|0)|0;a[f+e>>0]=0;return}function Je(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0;if(d>>>0>4294967279)Nd(b);if(d>>>0<11){a[b>>0]=d<<1;f=b+1|0}else{g=d+16&-16;h=jd(g)|0;c[b+8>>2]=h;c[b>>2]=g|1;c[b+4>>2]=d;f=h}Qe(f|0,e|0,d|0)|0;a[f+d>>0]=0;return}function Ke(b){b=b|0;if(a[b>>0]&1)kd(c[b+8>>2]|0);return}function Le(a,b){a=a|0;b=b|0;return Me(a,b,ve(b)|0)|0}function Me(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0;f=a[b>>0]|0;if(!(f&1)){g=10;h=f}else{f=c[b>>2]|0;g=(f&-2)+-1|0;h=f&255}if(!(h&1))i=(h&255)>>>1;else i=c[b+4>>2]|0;if((g-i|0)>>>0>=e>>>0){if(e){if(!(h&1))j=b+1|0;else j=c[b+8>>2]|0;Ue(j+i|0,d|0,e|0)|0;h=i+e|0;if(!(a[b>>0]&1))a[b>>0]=h<<1;else c[b+4>>2]=h;a[j+h>>0]=0}}else Ne(b,g,e-g+i|0,i,i,0,e,d);return b|0}function Ne(b,d,e,f,g,h,i,j){b=b|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;var k=0,l=0,m=0,n=0;if((-18-d|0)>>>0<e>>>0)Nd(b);if(!(a[b>>0]&1))k=b+1|0;else k=c[b+8>>2]|0;if(d>>>0<2147483623){l=e+d|0;e=d<<1;m=l>>>0<e>>>0?e:l;n=m>>>0<11?11:m+16&-16}else n=-17;m=jd(n)|0;if(g)Ue(m|0,k|0,g|0)|0;if(i)Ue(m+g|0,j|0,i|0)|0;j=f-h|0;if((j|0)!=(g|0))Ue(m+g+i|0,k+g+h|0,j-g|0)|0;if((d|0)!=10)kd(k);c[b+8>>2]=m;c[b>>2]=n|1;n=j+i|0;c[b+4>>2]=n;a[m+n>>0]=0;return}function Oe(){}function Pe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=b-d>>>0;e=b-d-(c>>>0>a>>>0|0)>>>0;return (D=e,a-c>>>0|0)|0}function Qe(b,d,e){b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0;f=b+e|0;if((e|0)>=20){d=d&255;g=b&3;h=d|d<<8|d<<16|d<<24;i=f&~3;if(g){g=b+4-g|0;while((b|0)<(g|0)){a[b>>0]=d;b=b+1|0}}while((b|0)<(i|0)){c[b>>2]=h;b=b+4|0}}while((b|0)<(f|0)){a[b>>0]=d;b=b+1|0}return b-e|0}function Re(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){D=b<<c|(a&(1<<c)-1<<32-c)>>>32-c;return a<<c}D=a<<c-32;return 0}function Se(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=a+c>>>0;return (D=b+d+(e>>>0<a>>>0|0)>>>0,e|0)|0}function Te(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){D=b>>>c;return a>>>c|(b&(1<<c)-1)<<32-c}D=0;return b>>>c-32|0}function Ue(b,d,e){b=b|0;d=d|0;e=e|0;var f=0;if((e|0)>=4096)return Ba(b|0,d|0,e|0)|0;f=b|0;if((b&3)==(d&3)){while(b&3){if(!e)return f|0;a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0;e=e-1|0}while((e|0)>=4){c[b>>2]=c[d>>2];b=b+4|0;d=d+4|0;e=e-4|0}}while((e|0)>0){a[b>>0]=a[d>>0]|0;b=b+1|0;d=d+1|0;e=e-1|0}return f|0}function Ve(a,b,c){a=a|0;b=b|0;c=c|0;if((c|0)<32){D=b>>c;return a>>>c|(b&(1<<c)-1)<<32-c}D=(b|0)<0?-1:0;return b>>c-32|0}function We(b){b=b|0;var c=0;c=a[m+(b&255)>>0]|0;if((c|0)<8)return c|0;c=a[m+(b>>8&255)>>0]|0;if((c|0)<8)return c+8|0;c=a[m+(b>>16&255)>>0]|0;if((c|0)<8)return c+16|0;return (a[m+(b>>>24)>>0]|0)+24|0}function Xe(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;c=a&65535;d=b&65535;e=$(d,c)|0;f=a>>>16;a=(e>>>16)+($(d,f)|0)|0;d=b>>>16;b=$(d,c)|0;return (D=(a>>>16)+($(d,f)|0)+(((a&65535)+b|0)>>>16)|0,a+b<<16|e&65535|0)|0}function Ye(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;e=b>>31|((b|0)<0?-1:0)<<1;f=((b|0)<0?-1:0)>>31|((b|0)<0?-1:0)<<1;g=d>>31|((d|0)<0?-1:0)<<1;h=((d|0)<0?-1:0)>>31|((d|0)<0?-1:0)<<1;i=Pe(e^a,f^b,e,f)|0;b=D;a=g^e;e=h^f;return Pe((bf(i,b,Pe(g^c,h^d,g,h)|0,D,0)|0)^a,D^e,a,e)|0}function Ze(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0,h=0,j=0,k=0,l=0,m=0;f=i;i=i+16|0;g=f|0;h=b>>31|((b|0)<0?-1:0)<<1;j=((b|0)<0?-1:0)>>31|((b|0)<0?-1:0)<<1;k=e>>31|((e|0)<0?-1:0)<<1;l=((e|0)<0?-1:0)>>31|((e|0)<0?-1:0)<<1;m=Pe(h^a,j^b,h,j)|0;b=D;bf(m,b,Pe(k^d,l^e,k,l)|0,D,g)|0;l=Pe(c[g>>2]^h,c[g+4>>2]^j,h,j)|0;j=D;i=f;return (D=j,l)|0}function _e(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=a;a=c;c=Xe(e,a)|0;f=D;return (D=($(b,a)|0)+($(d,e)|0)+f|f&0,c|0|0)|0}function $e(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return bf(a,b,c,d,0)|0}function af(a,b,d,e){a=a|0;b=b|0;d=d|0;e=e|0;var f=0,g=0;f=i;i=i+16|0;g=f|0;bf(a,b,d,e,g)|0;i=f;return (D=c[g+4>>2]|0,c[g>>2]|0)|0}function bf(a,b,d,e,f){a=a|0;b=b|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,E=0,F=0,G=0,H=0;g=a;h=b;i=h;j=d;k=e;l=k;if(!i){m=(f|0)!=0;if(!l){if(m){c[f>>2]=(g>>>0)%(j>>>0);c[f+4>>2]=0}n=0;o=(g>>>0)/(j>>>0)>>>0;return (D=n,o)|0}else{if(!m){n=0;o=0;return (D=n,o)|0}c[f>>2]=a|0;c[f+4>>2]=b&0;n=0;o=0;return (D=n,o)|0}}m=(l|0)==0;do if(j){if(!m){p=(ba(l|0)|0)-(ba(i|0)|0)|0;if(p>>>0<=31){q=p+1|0;r=31-p|0;s=p-31>>31;t=q;u=g>>>(q>>>0)&s|i<<r;v=i>>>(q>>>0)&s;w=0;x=g<<r;break}if(!f){n=0;o=0;return (D=n,o)|0}c[f>>2]=a|0;c[f+4>>2]=h|b&0;n=0;o=0;return (D=n,o)|0}r=j-1|0;if(r&j){s=(ba(j|0)|0)+33-(ba(i|0)|0)|0;q=64-s|0;p=32-s|0;y=p>>31;z=s-32|0;A=z>>31;t=s;u=p-1>>31&i>>>(z>>>0)|(i<<p|g>>>(s>>>0))&A;v=A&i>>>(s>>>0);w=g<<q&y;x=(i<<q|g>>>(z>>>0))&y|g<<p&s-33>>31;break}if(f){c[f>>2]=r&g;c[f+4>>2]=0}if((j|0)==1){n=h|b&0;o=a|0|0;return (D=n,o)|0}else{r=We(j|0)|0;n=i>>>(r>>>0)|0;o=i<<32-r|g>>>(r>>>0)|0;return (D=n,o)|0}}else{if(m){if(f){c[f>>2]=(i>>>0)%(j>>>0);c[f+4>>2]=0}n=0;o=(i>>>0)/(j>>>0)>>>0;return (D=n,o)|0}if(!g){if(f){c[f>>2]=0;c[f+4>>2]=(i>>>0)%(l>>>0)}n=0;o=(i>>>0)/(l>>>0)>>>0;return (D=n,o)|0}r=l-1|0;if(!(r&l)){if(f){c[f>>2]=a|0;c[f+4>>2]=r&i|b&0}n=0;o=i>>>((We(l|0)|0)>>>0);return (D=n,o)|0}r=(ba(l|0)|0)-(ba(i|0)|0)|0;if(r>>>0<=30){s=r+1|0;p=31-r|0;t=s;u=i<<p|g>>>(s>>>0);v=i>>>(s>>>0);w=0;x=g<<p;break}if(!f){n=0;o=0;return (D=n,o)|0}c[f>>2]=a|0;c[f+4>>2]=h|b&0;n=0;o=0;return (D=n,o)|0}while(0);if(!t){B=x;C=w;E=v;F=u;G=0;H=0}else{b=d|0|0;d=k|e&0;e=Se(b|0,d|0,-1,-1)|0;k=D;h=x;x=w;w=v;v=u;u=t;t=0;do{a=h;h=x>>>31|h<<1;x=t|x<<1;g=v<<1|a>>>31|0;a=v>>>31|w<<1|0;Pe(e,k,g,a)|0;i=D;l=i>>31|((i|0)<0?-1:0)<<1;t=l&1;v=Pe(g,a,l&b,(((i|0)<0?-1:0)>>31|((i|0)<0?-1:0)<<1)&d)|0;w=D;u=u-1|0}while((u|0)!=0);B=h;C=x;E=w;F=v;G=0;H=t}t=C;C=0;if(f){c[f>>2]=F;c[f+4>>2]=E}n=(t|0)>>>31|(B|C)<<1|(C<<1|t>>>31)&0|G;o=(t<<1|0>>>31)&-2|H;return (D=n,o)|0}function cf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return Za[a&7](b|0,c|0,d|0)|0}function df(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;_a[a&3](b|0,c|0,d|0,e|0,f|0)}function ef(a,b){a=a|0;b=b|0;$a[a&15](b|0)}function ff(a,b,c){a=a|0;b=b|0;c=c|0;ab[a&15](b|0,c|0)}function gf(a,b){a=a|0;b=b|0;return bb[a&3](b|0)|0}function hf(a){a=a|0;cb[a&3]()}function jf(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;return db[a&7](b|0,c|0,d|0,e|0)|0}function kf(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;eb[a&3](b|0,c|0,d|0,e|0,f|0,g|0)}function lf(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;fb[a&3](b|0,c|0,d|0,e|0)}function mf(a,b,c){a=a|0;b=b|0;c=c|0;ca(0);return 0}function nf(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;ca(1)}function of(a){a=a|0;ca(2)}function pf(a,b){a=a|0;b=b|0;ca(3)}function qf(a){a=a|0;ca(4);return 0}function rf(){ca(5)}function sf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;ca(6);return 0}function tf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;ca(7)}function uf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;ca(8)}

// EMSCRIPTEN_END_FUNCS
var Za=[mf,yd,Ae,de,ce,ee,be,mf];var _a=[nf,Fd,Ed,nf];var $a=[of,ld,md,td,wd,ud,vd,xd,Ib,sc,Md,we,xe,of,of,of];var ab=[pf,Mb,Nb,Ob,Pb,Qb,Rb,Sb,Tb,Ub,Vb,Wb,pf,pf,pf,pf];var bb=[qf,nd,ae,qf];var cb=[rf,Kd,Ld,rf];var db=[sf,pb,qb,tb,ub,sf,sf,sf];var eb=[tf,Hd,Gd,tf];var fb=[uf,Ad,Bd,uf];return{___cxa_can_catch:Id,_fflush:he,___cxa_is_pointer_type:Jd,_i64Add:Se,_i64Subtract:Pe,_memset:Qe,_malloc:Ee,_memcpy:Ue,_solve:fd,_bitshift64Lshr:Te,_free:Fe,_generateDDTable:gd,___errno_location:Qd,_bitshift64Shl:Re,__GLOBAL__sub_I_Init_cpp:Gb,runPostSets:Oe,stackAlloc:gb,stackSave:hb,stackRestore:ib,establishStackSpace:jb,setThrew:kb,setTempRet0:nb,getTempRet0:ob,dynCall_iiii:cf,dynCall_viiiii:df,dynCall_vi:ef,dynCall_vii:ff,dynCall_ii:gf,dynCall_v:hf,dynCall_iiiii:jf,dynCall_viiiiii:kf,dynCall_viiii:lf}})


// EMSCRIPTEN_END_ASM
(e.bb,e.cb,buffer);e.___cxa_can_catch=M.___cxa_can_catch;e._fflush=M._fflush;e.runPostSets=M.runPostSets;
e.___cxa_is_pointer_type=M.___cxa_is_pointer_type;var sd=e._i64Add=M._i64Add,rb=e._memset=M._memset,H=e._malloc=M._malloc,vd=e._memcpy=M._memcpy;e.___errno_location=M.___errno_location;var td=e._bitshift64Lshr=M._bitshift64Lshr,Ia=e._free=M._free;e._generateDDTable=M._generateDDTable;var kb=e._i64Subtract=M._i64Subtract,gb=e.__GLOBAL__sub_I_Init_cpp=M.__GLOBAL__sub_I_Init_cpp;e._solve=M._solve;var sb=e._bitshift64Shl=M._bitshift64Shl;e.dynCall_iiii=M.dynCall_iiii;e.dynCall_viiiii=M.dynCall_viiiii;
e.dynCall_vi=M.dynCall_vi;e.dynCall_vii=M.dynCall_vii;e.dynCall_ii=M.dynCall_ii;e.dynCall_v=M.dynCall_v;e.dynCall_iiiii=M.dynCall_iiiii;e.dynCall_viiiiii=M.dynCall_viiiiii;e.dynCall_viiii=M.dynCall_viiii;n.da=M.stackAlloc;n.va=M.stackSave;n.ea=M.stackRestore;n.Fd=M.establishStackSpace;n.ub=M.setTempRet0;n.ib=M.getTempRet0;function ia(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}ia.prototype=Error();ia.prototype.constructor=ia;
var yd=null,db=function zd(){e.calledRun||Ad();e.calledRun||(db=zd)};
e.callMain=e.Bd=function(a){function b(){for(var a=0;3>a;a++)d.push(0)}assert(0==bb,"cannot call main when async dependencies remain! (listen on __ATMAIN__)");assert(0==Ua.length,"cannot call main when preRun functions remain to be called");a=a||[];Ca||(Ca=!0,Ta(Va));var c=a.length+1,d=[G($a(e.thisProgram),"i8",0)];b();for(var f=0;f<c-1;f+=1)d.push(G($a(a[f]),"i8",0)),b();d.push(0);d=G(d,"i32",0);try{var g=e._main(c,d,0);Bd(g,!0)}catch(h){if(!(h instanceof ia))if("SimulateInfiniteLoop"==h)e.noExitRuntime=
!0;else throw h&&"object"===typeof h&&h.stack&&e.X("exception thrown: "+[h,h.stack]),h;}finally{}};
function Ad(a){function b(){if(!e.calledRun&&(e.calledRun=!0,!y)){Ca||(Ca=!0,Ta(Va));Ta(Wa);if(e.onRuntimeInitialized)e.onRuntimeInitialized();e._main&&Cd&&e.callMain(a);if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)Za(e.postRun.shift());Ta(Xa)}}a=a||e.arguments;null===yd&&(yd=Date.now());if(!(0<bb)){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)Ya(e.preRun.shift());Ta(Ua);0<bb||e.calledRun||(e.setStatus?(e.setStatus("Running..."),
setTimeout(function(){setTimeout(function(){e.setStatus("")},1);b()},1)):b())}}e.run=e.run=Ad;function Bd(a,b){if(!b||!e.noExitRuntime){if(!e.noExitRuntime&&(y=!0,m=void 0,Ta(L),e.onExit))e.onExit(a);da?(process.stdout.once("drain",function(){process.exit(a)}),console.log(" "),setTimeout(function(){process.exit(a)},500)):ea&&"function"===typeof quit&&quit(a);throw new ia(a);}}e.exit=e.exit=Bd;var Dd=[];
function x(a){void 0!==a?(e.print(a),e.X(a),a=JSON.stringify(a)):a="";y=!0;var b="abort("+a+") at "+Ja()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.";Dd&&Dd.forEach(function(c){b=c(b,a)});throw b;}e.abort=e.abort=x;if(e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);0<e.preInit.length;)e.preInit.pop()();var Cd=!0;e.noInitialRun&&(Cd=!1);Ad();

